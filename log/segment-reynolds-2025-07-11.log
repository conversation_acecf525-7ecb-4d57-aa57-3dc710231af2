Fri Jul 11 2025 03:47:57 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Fri Jul 11 2025 03:47:57 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
Fri Jul 11 2025 03:48:00 GMT+0000 : <PERSON> : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-10T10:05:59.926Z","uniqueId":"rc20250710100559926317"}
Fri Jul 11 2025 03:48:00 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-10T10:05:59.926Z","uniqueId":"rc20250710100559926317"}
Fri Jul 11 2025 03:48:00 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-10T10:05:59.926Z","uniqueId":"rc20250710100559926317"}
Fri Jul 11 2025 03:48:00 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:48:00.684Z","uniqueId":"rc20250710100559926317"}]
Fri Jul 11 2025 03:48:00 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 03:48:00 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 03:48:00 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 03:48:09 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Fri Jul 11 2025 03:48:09 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:48:00.684Z","uniqueId":"rc20250711034800684334"}
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:48:00.684Z","uniqueId":"rc20250711034800684334"}
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:48:00.684Z","uniqueId":"rc20250711034800684334"}
Fri Jul 11 2025 03:58:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:58:02.758Z","uniqueId":"rc20250711034800684334"}]
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 03:58:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:58:02.758Z","uniqueId":"rc20250711035802758689"}
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:58:02.758Z","uniqueId":"rc20250711035802758689"}
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T03:58:02.758Z","uniqueId":"rc20250711035802758689"}
Fri Jul 11 2025 04:08:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:08:02.823Z","uniqueId":"rc20250711035802758689"}]
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:08:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:08:02.823Z","uniqueId":"rc20250711040802823994"}
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:08:02.823Z","uniqueId":"rc20250711040802823994"}
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:08:02.823Z","uniqueId":"rc20250711040802823994"}
Fri Jul 11 2025 04:18:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:18:02.873Z","uniqueId":"rc20250711040802823994"}]
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:18:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:18:02.873Z","uniqueId":"rc20250711041802874204"}
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:18:02.873Z","uniqueId":"rc20250711041802874204"}
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:18:02.873Z","uniqueId":"rc20250711041802874204"}
Fri Jul 11 2025 04:28:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:28:02.894Z","uniqueId":"rc20250711041802874204"}]
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:28:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:28:02.894Z","uniqueId":"rc20250711042802895837"}
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:28:02.894Z","uniqueId":"rc20250711042802895837"}
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:28:02.894Z","uniqueId":"rc20250711042802895837"}
Fri Jul 11 2025 04:38:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:38:02.931Z","uniqueId":"rc20250711042802895837"}]
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:38:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:38:02.931Z","uniqueId":"rc20250711043802931442"}
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:38:02.931Z","uniqueId":"rc20250711043802931442"}
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:38:02.931Z","uniqueId":"rc20250711043802931442"}
Fri Jul 11 2025 04:48:02 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:48:02.970Z","uniqueId":"rc20250711043802931442"}]
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:48:02 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:48:02.970Z","uniqueId":"rc20250711044802970647"}
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:48:02.970Z","uniqueId":"rc20250711044802970647"}
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:48:02.970Z","uniqueId":"rc20250711044802970647"}
Fri Jul 11 2025 04:58:03 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:58:03.003Z","uniqueId":"rc20250711044802970647"}]
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 04:58:03 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:58:03.003Z","uniqueId":"rc20250711045803003983"}
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:58:03.003Z","uniqueId":"rc20250711045803003983"}
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T04:58:03.003Z","uniqueId":"rc20250711045803003983"}
Fri Jul 11 2025 05:08:03 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/204841281442454__01_10_1","fileDate":"07/30/2024","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:08:03.053Z","uniqueId":"rc20250711045803003983"}]
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds:storeIdentification : 204841281442454__01_10_1
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds
Fri Jul 11 2025 05:08:03 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_204841281442454__01_10_1_355c3b56-f22c-4fba-bfe1-b5a93799e50b_20240730093030.gz
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:09:54 GMT+0000 : Extraction Job Data: [{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z"}]
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:09:54 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip
Fri Jul 11 2025 05:09:54 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:09:54 GMT+0000 : Reynolds : Extraction process for store QAS10232 exited code 0
Fri Jul 11 2025 05:10:13 GMT+0000 : Reynolds : Found one Store extraction > QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip to process now
Fri Jul 11 2025 05:10:13 GMT+0000 : Reynolds : Process JSON schedule started with file > QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip
Fri Jul 11 2025 05:10:13 GMT+0000 : Groupname : QA1023
Fri Jul 11 2025 05:10:13 GMT+0000 : Location Id: : 569431390514428
Fri Jul 11 2025 05:10:13 GMT+0000 : storeName : QAS10232
Fri Jul 11 2025 05:10:13 GMT+0000 : extractedFileTimeStamp : _01_01_20250711050954
Fri Jul 11 2025 05:10:13 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:10:13 GMT+0000 : jobsTmp : [{"_id":"686ce8944c3089cae3bece5e","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","invoiceMasterCSVFilePath":"/etl/etl-vagrant/etl-reynolds/reynolds-inv/Trophy_Kia_DTLA_ALL_INVOICE_MASTER_20230523.zip","switchBranch":false,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/08/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","isCombinedAll":true,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-08","startTime":"2025-07-08T09:44:54.003Z","uniqueId":"rc20250708094454004745","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250708094454.zip","endTime":"2025-07-08T09:44:54.159Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-08T09:44:54.001Z","lastFinishedAt":"2025-07-08T09:44:54.161Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"},{"_id":"686ce98e4c3089cae3bece98","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/08/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","isCombinedAll":true,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-08","invoiceMasterCSVFilePath":"","startTime":"2025-07-08T09:49:04.004Z","uniqueId":"rc20250708094904004421","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250708094904.zip","endTime":"2025-07-08T09:49:04.084Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-08T09:49:04.001Z","lastFinishedAt":"2025-07-08T09:49:04.085Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"},{"_id":"686de3c8a13e99c4c5ebb7fe","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/09/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","isCombinedAll":false,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-09","invoiceMasterCSVFilePath":"","startTime":"2025-07-09T03:36:42.001Z","uniqueId":"rc20250709033642002339","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250709033642.zip","endTime":"2025-07-09T03:36:42.044Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-09T03:36:42.000Z","lastFinishedAt":"2025-07-09T03:36:42.045Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"},{"_id":"686f5e5e0510f340e6d9b9fc","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","isCombinedAll":true,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-10T06:31:59.004Z","uniqueId":"rc20250710063159004445","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250710063159.zip","endTime":"2025-07-10T06:31:59.077Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-10T06:31:59.000Z","lastFinishedAt":"2025-07-10T06:31:59.079Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"},{"_id":"686f62a00510f340e6d9bb01","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/10/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-10","invoiceMasterCSVFilePath":"","startTime":"2025-07-10T06:50:10.002Z","uniqueId":"rc20250710065010002612","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250710065010.zip","endTime":"2025-07-10T06:50:10.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-10T06:50:10.000Z","lastFinishedAt":"2025-07-10T06:50:10.063Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"},{"_id":"68709ca0eb5a7206566980fc","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:09:54.000Z","lastFinishedAt":"2025-07-11T05:09:54.066Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"}]
Fri Jul 11 2025 05:10:13 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"68709ca0eb5a7206566980fc","name":"REYNOLDS","data":{"groupName":"QA1023","storeDataArray":[{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:09:54.000Z","lastFinishedAt":"2025-07-11T05:09:54.066Z","type":"normal","nextRunAt":"2025-07-11T05:10:13.022Z"}
Fri Jul 11 2025 05:10:13 GMT+0000 : Location Id : 569431390514428
Fri Jul 11 2025 05:10:13 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:10:13 GMT+0000 : agendaObject : [{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:10:13 GMT+0000 : Sorted agenda object : [{"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:10:13 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:10:13 GMT+0000 : Extracted agenda object : {"locationId":"569431390514428","sourceId":"569431390514428","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [569431390514428,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"569431390514428","assignedtoCn":"Netspective Team","brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:09:54.002Z","uniqueId":"rc20250711050954002601","processFileName":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","endTime":"2025-07-11T05:09:54.065Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:10:13 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:10:13 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:10:13 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:10:13 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:10:13 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:10:13 GMT+0000 : fopcStore : false
Fri Jul 11 2025 05:10:13 GMT+0000 : extractionId : 68709ca0eb5a7206566980fc
Fri Jul 11 2025 05:10:13 GMT+0000 : buildProxies : true
Fri Jul 11 2025 05:10:13 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:10:13 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:10:13 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:10:13 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:10:13 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:10:13 GMT+0000 : customBranchName : 
Fri Jul 11 2025 05:10:13 GMT+0000 : uniqueId : rc20250711050954002601-1752210613023
Fri Jul 11 2025 05:10:13 GMT+0000 : companyIds : *********,
Fri Jul 11 2025 05:10:13 GMT+0000 : companyObj : [object Object]
Fri Jul 11 2025 05:10:13 GMT+0000 : testData : false
Fri Jul 11 2025 05:10:13 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:10:13 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:10:13 GMT+0000 : Reynolds : Start processing of extraction > QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip
Fri Jul 11 2025 05:10:13 GMT+0000 : stdout: UUID: rc20250711050954002601-1752210613023

Fri Jul 11 2025 05:10:13 GMT+0000 : stdout: PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Fri Jul 11 2025 05:10:13 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:13 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:13 GMT+0000 : stdout: Processor status: Processing Started

Fri Jul 11 2025 05:10:14 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:10:14 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip[0m

Fri Jul 11 2025 05:10:14 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:14 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:14 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:10:15 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Fri Jul 11 2025 05:10:15 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:15 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:15 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:10:16 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:16 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:16 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:10:17 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:10:17 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:17 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:17 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:10:18 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:10:18 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:10:18 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:18 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:18 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:10:22 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:10:22 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:10:22 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Fri Jul 11 2025 05:10:22 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:10:23 GMT+0000 : stdout: Fri Jul 11 05:10:23 UTC 2025 Found # 29

Fri Jul 11 2025 05:10:23 GMT+0000 : stdout: Fri Jul 11 05:10:23 UTC 2025 Transform Begin

Fri Jul 11 2025 05:10:23 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:10:23 GMT+0000 : stdout: Fri Jul 11 05:10:23 UTC 2025 Transform End

Fri Jul 11 2025 05:10:23 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:23 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:23 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:10:24 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:10:24 GMT+0000 : stdout: Fri Jul 11 05:10:24 UTC 2025

Fri Jul 11 2025 05:10:24 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:10:34 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:34 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:34 GMT+0000 : stdout: Fri Jul 11 05:10:34 UTC 2025

Fri Jul 11 2025 05:10:34 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:10:35 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:10:35 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:35 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:35 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:10:36 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:10:36 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:10:36 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:36 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:36 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:10:36 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: [1;36mCreating exclusion reports
Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:10:37 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:10:37 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:10:37 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:10:37 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: im_count:     0
im_exception:     0
im_count less than zero

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117
suffixed_invoices_count:1
punch time missing count :34.38 

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:10:38 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Fri Jul 11 2025 05:10:40 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:10:40 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:40 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:40 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:10:41 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting[0m

Fri Jul 11 2025 05:10:42 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954-REPORT.zip-----

Fri Jul 11 2025 05:10:42 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:10:42 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:42 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:42 GMT+0000 : stdout:  (deflated 86%)

Fri Jul 11 2025 05:10:42 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:10:43 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Fri Jul 11 2025 05:10:43 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:43 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:43 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:10:43 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:10:44 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:44 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:44 GMT+0000 : stdout: COMPANY_ID: *********
Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COMPANY_BRAND: GM

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COPY 4

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: INSERT 0 49

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: COPY 63

Fri Jul 11 2025 05:10:45 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:45 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:10:45 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Completed

Fri Jul 11 2025 05:10:46 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:46 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:46 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Started

Fri Jul 11 2025 05:10:47 GMT+0000 : stdout: 
zip error: Nothing to do! (try: zip -q -jmr import-files.zip . -i ./import-files)

Fri Jul 11 2025 05:10:47 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:47 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:47 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Completed

Fri Jul 11 2025 05:10:48 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:48 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:48 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Started

Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: [1;36m05:10:49 : Ready to Generate Exception Analysis Report[0m

Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: CSV file '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/All_exception_details.csv' already exists.

Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: File copied to /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:10:49 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:49 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: [1;36m05:10:49 : Done Generating Exception Analysis Report[0m

Fri Jul 11 2025 05:10:49 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Completed

Fri Jul 11 2025 05:10:50 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:50 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:50 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Started

Fri Jul 11 2025 05:10:51 GMT+0000 : stdout: [1;36m05:10:51 : Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:10:51 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:51 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:51 GMT+0000 : stdout: [1;36m05:10:51 : Done Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:10:51 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Completed

Fri Jul 11 2025 05:10:52 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:52 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:52 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Started

Fri Jul 11 2025 05:10:53 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:53 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:53 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin: line 41: BASE_SRC_SCHEMA: unbound variable

Fri Jul 11 2025 05:10:53 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:10:53 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Completed

Fri Jul 11 2025 05:10:54 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:54 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:54 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Started

Fri Jul 11 2025 05:10:55 GMT+0000 : stdout: [1;36m05:10:55 : Ready to Extract Text ROs to TSV[0m

Fri Jul 11 2025 05:10:56 GMT+0000 : stderr: Traceback (most recent call last):
  File "/home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/src/extract/python/./extract-text-proxy-to-tsv.py", line 14, in <module>

Fri Jul 11 2025 05:10:56 GMT+0000 : stderr:     for ro_file in os.listdir(proxy_file_path):
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice/text'

Fri Jul 11 2025 05:10:56 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:10:56 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:10:56 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:56 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:56 GMT+0000 : stdout: [1;36m05:10:56 : Done Extracting Text ROs to TSV[0m

Fri Jul 11 2025 05:10:56 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Completed

Fri Jul 11 2025 05:10:57 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:57 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:57 GMT+0000 : stdout: Processor status: 14/16 Compressing Proxy Directory Started

Fri Jul 11 2025 05:10:58 GMT+0000 : stdout: [1;36mcompress /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice
Fri Jul 11 2025 05:10:58 GMT+0000 : stdout: [0m
/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice

Fri Jul 11 2025 05:10:58 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 151: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:10:58 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:58 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:58 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 181: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:10:58 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:10:58 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:10:58 GMT+0000 : stdout: Processor status: 14/16 Compressing Directory Completed

Fri Jul 11 2025 05:10:59 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:10:59 GMT+0000 : processorStatus
Fri Jul 11 2025 05:10:59 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Started

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: [1;36m
Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Generating Store Specific Rules[0m

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Paytype import halt checking

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Department import halt checking

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Inv Seq halt checking

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Unassigned Make import halt checking

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: paytype_halt     false
inv_seq_halt     true
make_halt        false

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: deptment_halt    false

Fri Jul 11 2025 05:11:00 GMT+0000 : stderr: rm: cannot remove '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt'
Fri Jul 11 2025 05:11:00 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:11:00 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:11:00 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: [1;36mInserting Halt status to database[0m

Fri Jul 11 2025 05:11:00 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:11:00 GMT+0000 : processorStatus
Fri Jul 11 2025 05:11:00 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Completed

Fri Jul 11 2025 05:11:01 GMT+0000 : stdout: INPUT_TYPE ::: json
OLD_SCHEMA1 ::: du_dms_reynoldsrci_model

Fri Jul 11 2025 05:11:01 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:11:01 GMT+0000 : stdout: NEW_SCHEMA ::: du_dms_rc202507110509540026011752210613023_*********

Fri Jul 11 2025 05:11:01 GMT+0000 : stdout: OLD_SCHEMA ::: du_dms_reynoldsrci_model
NEW_SCHEMA ::: du_dms_rc202507110509540026011752210613023_*********
[1;36mProcessing schema duplication[0m

Fri Jul 11 2025 05:11:01 GMT+0000 : stderr: NOTICE:  schema "du_dms_rc202507110509540026011752210613023_*********" does not exist, skipping

Fri Jul 11 2025 05:11:01 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:11:18 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:11:18 GMT+0000 : processorStatus
Fri Jul 11 2025 05:11:18 GMT+0000 : stdout: Dump file created for new Schema: 'du_dms_rc202507110509540026011752210613023_*********'

Fri Jul 11 2025 05:11:18 GMT+0000 : stdout: OLD_SCHEMA2 ::: du_dms_rc202507110509540026011752210613023_*********
Processor status: 16/16 Moving Work to Bundle Directory Started

Fri Jul 11 2025 05:11:19 GMT+0000 : stdout: [1;36mMoving all input files under the store to archive[0m

Fri Jul 11 2025 05:11:19 GMT+0000 : stdout: replacedElement:*_199001288329074__01_01_1_*.gz
199001288329074__01_01_1

Fri Jul 11 2025 05:11:19 GMT+0000 : stdout: archiveFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/archive

Fri Jul 11 2025 05:11:19 GMT+0000 : stdout: inputFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/*_199001288329074__01_01_1_*.gz
The inputFilePath does not contain 'archive@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@'.
[1;36mMoving Work to Bundle Directory[0m

Fri Jul 11 2025 05:11:22 GMT+0000 : stdout: [1;36mDistribute Function Result: 0[0m

Fri Jul 11 2025 05:11:22 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:11:22 GMT+0000 : processorStatus
Fri Jul 11 2025 05:11:22 GMT+0000 : stdout: Processor status: 16/16 Moving Work to Bundle Directory Completed

Fri Jul 11 2025 05:11:23 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:11:23 GMT+0000 : processorStatus
Fri Jul 11 2025 05:11:23 GMT+0000 : stdout: Processor status: Processing Completed

Fri Jul 11 2025 05:11:24 GMT+0000 : stdout: [1;36mClearing working directory and remove input if requested[0m

Fri Jul 11 2025 05:11:24 GMT+0000 : stdout: [1;32mZapping Input Zip File As Requested[0m

Fri Jul 11 2025 05:11:24 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:11:24 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:11:24 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:11:24 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:11:24 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:11:24 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:11:24 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:11:24 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 0
Fri Jul 11 2025 05:11:24 GMT+0000 : Job saved to DB {"_id":"68709cb5eb5a720656698101","name":"REYNOLDSRCI-PROCESS-JSON","data":{"inputFile":"QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip","createdAt":"_01_01_20250711050954","operation":"json-processing","storeID":"569431390514428","inputFilePath1":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","processorUniqueId":"rc20250711050954002601-1752210613023","outputFile":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip & /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/etl/PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954-ETL.zip","status":true,"message":"Success","warningMessage":{"scheduled_by":"<EMAIL>","invalidmiscpaytypeCount":17,"invalidmiscpaytypeFilePath":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv","estimateCount":94,"punchTimeMissingCount":"34.38\n","PUNCH_TIME_MISSING_FILEPATH":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv"},"invalidmiscpaytypeCount":17,"estimateCount":94,"punchTimeMissingCount":"34.38\n","suffixedInvoicesCsvData":""},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":"2025-07-11T05:11:24.625Z","type":"normal","nextRunAt":null,"lastRunAt":"2025-07-11T05:10:13.019Z"}
Fri Jul 11 2025 05:11:47 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:11:47 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:11:47 GMT+0000 : Reynolds:  processing distribution
Fri Jul 11 2025 05:11:47 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip[0m

Fri Jul 11 2025 05:11:47 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip[0m

Fri Jul 11 2025 05:11:48 GMT+0000 : stdout: This store has import halt exception

Fri Jul 11 2025 05:11:51 GMT+0000 : stdout: Zip file /etl/audit-import-halt/PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954_rc20250711050954002601-1752210613023_*********-ETL.zip copied to manual import dir /etl/scheduler-manual-import

Fri Jul 11 2025 05:11:51 GMT+0000 : stdout: [1;36mDistributing Full Bundle[0m

Fri Jul 11 2025 05:11:51 GMT+0000 : stdout: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip' -> '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-569431390514428-_01_01_20250711050954.zip'

Fri Jul 11 2025 05:11:51 GMT+0000 : stdout: [1;32mBundle Distribution Completed[0m

Fri Jul 11 2025 05:11:51 GMT+0000 : stdout: [1;36mMoving Bundle to Archive[0m

Fri Jul 11 2025 05:11:51 GMT+0000 : close: Success
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:15:03 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z"}]
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:15:03 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:15:03 GMT+0000 : Reynolds : Extraction process for store QASREY523 exited code 0
Fri Jul 11 2025 05:15:52 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip to process now
Fri Jul 11 2025 05:15:52 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:52 GMT+0000 : Groupname : QAREY52
Fri Jul 11 2025 05:15:52 GMT+0000 : Location Id: : 936351325615505
Fri Jul 11 2025 05:15:52 GMT+0000 : storeName : QASREY523
Fri Jul 11 2025 05:15:52 GMT+0000 : extractedFileTimeStamp : _01_01_20250711051503
Fri Jul 11 2025 05:15:52 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:15:52 GMT+0000 : jobsTmp : [{"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-11T05:15:52.025Z"}]
Fri Jul 11 2025 05:15:52 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-11T05:15:52.025Z"}
Fri Jul 11 2025 05:15:52 GMT+0000 : Location Id : 936351325615505
Fri Jul 11 2025 05:15:52 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:15:52 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:15:52 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:15:52 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:15:52 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:15:52 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:15:52 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:15:52 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:15:52 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:15:52 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:15:52 GMT+0000 : buildProxies : true
Fri Jul 11 2025 05:15:52 GMT+0000 : fopcStore : false
Fri Jul 11 2025 05:15:52 GMT+0000 : extractionId : 68709dd5eb5a72065669813e
Fri Jul 11 2025 05:15:52 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:15:52 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:15:52 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:15:52 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:15:52 GMT+0000 : customBranchName : 
Fri Jul 11 2025 05:15:52 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:15:52 GMT+0000 : uniqueId : rc20250711051503002053-1752210952027
Fri Jul 11 2025 05:15:52 GMT+0000 : companyIds : *********,*********,
Fri Jul 11 2025 05:15:52 GMT+0000 : companyObj : [object Object],[object Object]
Fri Jul 11 2025 05:15:52 GMT+0000 : testData : false
Fri Jul 11 2025 05:15:52 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:15:52 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:15:52 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:52 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:52 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:52 GMT+0000 : stdout: UUID: rc20250711051503002053-1752210952027
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true
Processor status: Processing Started

Fri Jul 11 2025 05:15:53 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:15:53 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip[0m

Fri Jul 11 2025 05:15:53 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:53 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:53 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:15:54 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Fri Jul 11 2025 05:15:54 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:54 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:54 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:15:55 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:55 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:55 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:15:56 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:15:56 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:56 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:56 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:15:57 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:15:57 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:15:57 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:15:57 GMT+0000 : processorStatus
Fri Jul 11 2025 05:15:57 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:16:01 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: Fri Jul 11 05:16:01 UTC 2025 Found # 29

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: Fri Jul 11 05:16:01 UTC 2025 Transform Begin

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: Fri Jul 11 05:16:01 UTC 2025 Transform End

Fri Jul 11 2025 05:16:01 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:01 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:01 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:16:02 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:16:02 GMT+0000 : stdout: Fri Jul 11 05:16:02 UTC 2025

Fri Jul 11 2025 05:16:03 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:16:13 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:13 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:13 GMT+0000 : stdout: Fri Jul 11 05:16:13 UTC 2025
[1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:16:14 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:14 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:14 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:16:14 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:16:15 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:16:15 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:16:15 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:15 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:15 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:16:15 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36m
Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: Creating exclusion reports[0m

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36m
Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: Enumerating ROs[0m

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:16:16 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:16:16 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:16:16 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: im_count:     0
im_exception:     0

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: im_count less than zero

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: suffixed_invoices_count:1
punch time missing count :34.38 

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema
Fri Jul 11 2025 05:16:16 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:16:17 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:16:17 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:17 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:17 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:16:18 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting
Fri Jul 11 2025 05:16:18 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:16:18 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503-REPORT.zip-----

Fri Jul 11 2025 05:16:18 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:16:18 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:18 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:18 GMT+0000 : stdout:  (deflated 86%)

Fri Jul 11 2025 05:16:18 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:16:19 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Fri Jul 11 2025 05:16:19 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:19 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:19 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:16:19 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:16:20 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:16:20 GMT+0000 : processorStatus
Fri Jul 11 2025 05:16:20 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:16:20 GMT+0000 : stdout: COMPANY_ID: *********
Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:16:21 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:16:22 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:16:22 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:16:22 GMT+0000 : stderr: [1;31mCOMPANY_BRAND is empty or does not exist[0m

Fri Jul 11 2025 05:16:22 GMT+0000 : stdout: [1;33mMoving input to dead-letter bin: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed[0m

Fri Jul 11 2025 05:16:22 GMT+0000 : stderr: cp: cannot stat '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip'
Fri Jul 11 2025 05:16:22 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:16:22 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:16:22 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:16:22 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:16:22 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:16:22 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:16:22 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:16:22 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:16:22 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 1
Fri Jul 11 2025 05:16:22 GMT+0000 : Autosoft : filePath inpObj Error - QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip
Fri Jul 11 2025 05:16:22 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:15:03.002Z\",\"uniqueId\":\"rc20250711051503002053\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip\",\"endTime\":\"2025-07-11T05:15:03.080Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:16:22 GMT+0000 : Reynolds : doPayloadAction - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:15:03.002Z\",\"uniqueId\":\"rc20250711051503002053\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip\",\"endTime\":\"2025-07-11T05:15:03.080Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:16:22 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - {"inProjectId":[""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:15:03.002Z\",\"uniqueId\":\"rc20250711051503002053\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip\",\"endTime\":\"2025-07-11T05:15:03.080Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:16:22 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:16:22 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:16:24 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:16:24 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:16:24 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:17:53 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z"}]
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:17:53 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip
Fri Jul 11 2025 05:17:53 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:17:53 GMT+0000 : Reynolds : Extraction process for store QASREY523 exited code 0
Fri Jul 11 2025 05:18:24 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip to process now
Fri Jul 11 2025 05:18:24 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip
Fri Jul 11 2025 05:18:24 GMT+0000 : Groupname : QAREY52
Fri Jul 11 2025 05:18:24 GMT+0000 : Location Id: : 936351325615505
Fri Jul 11 2025 05:18:24 GMT+0000 : storeName : QASREY523
Fri Jul 11 2025 05:18:24 GMT+0000 : extractedFileTimeStamp : _01_01_20250711051753
Fri Jul 11 2025 05:18:24 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:18:24 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"68709e7feb5a72065669815e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:17:53.000Z","lastFinishedAt":"2025-07-11T05:17:53.060Z","type":"normal","nextRunAt":"2025-07-11T05:18:24.146Z"}
Fri Jul 11 2025 05:18:24 GMT+0000 : Location Id : 936351325615505
Fri Jul 11 2025 05:18:24 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:18:24 GMT+0000 : jobsTmp : [{"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-11T05:18:24.146Z"},{"_id":"68709e7feb5a72065669815e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:17:53.000Z","lastFinishedAt":"2025-07-11T05:17:53.060Z","type":"normal","nextRunAt":"2025-07-11T05:18:24.146Z"}]
Fri Jul 11 2025 05:18:24 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:18:24 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:18:24 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:18:24 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:18:24 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:18:24 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:18:24 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:18:24 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:18:24 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:18:24 GMT+0000 : fopcStore : null
Fri Jul 11 2025 05:18:24 GMT+0000 : extractionId : 68709e7feb5a72065669815e
Fri Jul 11 2025 05:18:24 GMT+0000 : buildProxies : true
Fri Jul 11 2025 05:18:24 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:18:24 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:18:24 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:18:24 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:18:24 GMT+0000 : customBranchName : null
Fri Jul 11 2025 05:18:24 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:18:24 GMT+0000 : uniqueId : rc20250711051753002067-1752211104147
Fri Jul 11 2025 05:18:24 GMT+0000 : companyIds : *********,
Fri Jul 11 2025 05:18:24 GMT+0000 : companyObj : [object Object]
Fri Jul 11 2025 05:18:24 GMT+0000 : testData : false
Fri Jul 11 2025 05:18:24 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:18:24 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:18:24 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip
Fri Jul 11 2025 05:18:24 GMT+0000 : stdout: UUID: rc20250711051753002067-1752211104147
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Fri Jul 11 2025 05:18:24 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:24 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:24 GMT+0000 : stdout: Processor status: Processing Started

Fri Jul 11 2025 05:18:25 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:18:25 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip[0m

Fri Jul 11 2025 05:18:25 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:25 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:25 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:18:26 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Fri Jul 11 2025 05:18:26 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:26 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:26 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:18:27 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:27 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:27 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:18:28 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:18:28 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:28 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:28 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:18:29 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:18:29 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:18:29 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:29 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:29 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:18:33 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: Fri Jul 11 05:18:33 UTC 2025 Found # 29

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: Fri Jul 11 05:18:33 UTC 2025 Transform Begin

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:18:33 GMT+0000 : stdout: Fri Jul 11 05:18:33 UTC 2025 Transform End

Fri Jul 11 2025 05:18:33 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:33 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:34 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:18:34 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:18:35 GMT+0000 : stdout: Fri Jul 11 05:18:35 UTC 2025

Fri Jul 11 2025 05:18:35 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:18:45 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:45 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:45 GMT+0000 : stdout: Fri Jul 11 05:18:45 UTC 2025

Fri Jul 11 2025 05:18:45 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:18:46 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:18:46 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:46 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:46 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:18:47 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:18:47 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:18:47 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:47 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:47 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:18:47 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mCreating exclusion reports
Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:18:48 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:18:48 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:18:48 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: im_count:     0
im_exception:     0

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: im_count less than zero

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: suffixed_invoices_count:1
punch time missing count :34.38 

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:18:48 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Fri Jul 11 2025 05:18:49 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:18:49 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:49 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:49 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:18:50 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting
Fri Jul 11 2025 05:18:50 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:18:50 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753-REPORT.zip-----

Fri Jul 11 2025 05:18:50 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:18:50 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:50 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:50 GMT+0000 : stdout:  (deflated 86%)
Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:18:51 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Fri Jul 11 2025 05:18:51 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:51 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:51 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:18:51 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:18:52 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:52 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:52 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:18:52 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:18:53 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:18:53 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:53 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:53 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: COMPANY_BRAND: GM

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: COPY 2

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: INSERT 0 49

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: COPY 63

Fri Jul 11 2025 05:18:54 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:54 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:18:54 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Completed

Fri Jul 11 2025 05:18:55 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:55 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:55 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Started

Fri Jul 11 2025 05:18:56 GMT+0000 : stdout: 
zip error: Nothing to do! (try: zip -q -jmr import-files.zip . -i ./import-files)

Fri Jul 11 2025 05:18:56 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:56 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:56 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Completed

Fri Jul 11 2025 05:18:57 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:57 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:57 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Started

Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: [1;36m05:18:58 : Ready to Generate Exception Analysis Report[0m

Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: CSV file '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/All_exception_details.csv' already exists.

Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: File copied to /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:18:58 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:58 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: [1;36m05:18:58 : Done Generating Exception Analysis Report[0m

Fri Jul 11 2025 05:18:58 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Completed

Fri Jul 11 2025 05:18:59 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:18:59 GMT+0000 : processorStatus
Fri Jul 11 2025 05:18:59 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Started

Fri Jul 11 2025 05:19:00 GMT+0000 : stdout: [1;36m05:19:00 : Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:19:00 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:00 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:00 GMT+0000 : stdout: [1;36m05:19:00 : Done Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:19:00 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Completed

Fri Jul 11 2025 05:19:01 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:01 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:01 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Started

Fri Jul 11 2025 05:19:02 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin: line 41: BASE_SRC_SCHEMA: unbound variable

Fri Jul 11 2025 05:19:02 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:02 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:02 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:02 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Completed

Fri Jul 11 2025 05:19:03 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:03 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:03 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Started

Fri Jul 11 2025 05:19:04 GMT+0000 : stdout: [1;36m05:19:04 : Ready to Extract Text ROs to TSV[0m

Fri Jul 11 2025 05:19:04 GMT+0000 : stderr: Traceback (most recent call last):
  File "/home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/src/extract/python/./extract-text-proxy-to-tsv.py", line 14, in <module>

Fri Jul 11 2025 05:19:04 GMT+0000 : stderr:     for ro_file in os.listdir(proxy_file_path):
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice/text'

Fri Jul 11 2025 05:19:04 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:04 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:04 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:04 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:04 GMT+0000 : stdout: [1;36m05:19:04 : Done Extracting Text ROs to TSV[0m

Fri Jul 11 2025 05:19:04 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Completed

Fri Jul 11 2025 05:19:05 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:05 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:05 GMT+0000 : stdout: Processor status: 14/16 Compressing Proxy Directory Started

Fri Jul 11 2025 05:19:06 GMT+0000 : stdout: [1;36mcompress /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice[0m

Fri Jul 11 2025 05:19:06 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice

Fri Jul 11 2025 05:19:06 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:06 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:06 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 151: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:19:06 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 181: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:19:06 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:06 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:06 GMT+0000 : stdout: Processor status: 14/16 Compressing Directory Completed

Fri Jul 11 2025 05:19:07 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:07 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:07 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Started

Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: [1;36m
Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: Generating Store Specific Rules[0m

Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: Paytype import halt checking

Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: Department import halt checking

Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: Inv Seq halt checking

Fri Jul 11 2025 05:19:08 GMT+0000 : stdout: Unassigned Make import halt checking

Fri Jul 11 2025 05:19:09 GMT+0000 : stdout: paytype_halt     true
inv_seq_halt     true
make_halt        false

Fri Jul 11 2025 05:19:09 GMT+0000 : stdout: deptment_halt    false

Fri Jul 11 2025 05:19:09 GMT+0000 : stderr: rm: cannot remove '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt'
Fri Jul 11 2025 05:19:09 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:19:09 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:09 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:09 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:19:09 GMT+0000 : stdout: [1;36mInserting Halt status to database[0m

Fri Jul 11 2025 05:19:09 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:09 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:09 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Completed

Fri Jul 11 2025 05:19:10 GMT+0000 : stdout: INPUT_TYPE ::: json
OLD_SCHEMA1 ::: du_dms_reynoldsrci_model

Fri Jul 11 2025 05:19:10 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:19:10 GMT+0000 : stdout: NEW_SCHEMA ::: du_dms_rc202507110517530020671752211104147_*********

Fri Jul 11 2025 05:19:10 GMT+0000 : stdout: OLD_SCHEMA ::: du_dms_reynoldsrci_model
NEW_SCHEMA ::: du_dms_rc202507110517530020671752211104147_*********
[1;36mProcessing schema duplication[0m

Fri Jul 11 2025 05:19:10 GMT+0000 : stderr: NOTICE:  schema "du_dms_rc202507110517530020671752211104147_*********" does not exist, skipping

Fri Jul 11 2025 05:19:10 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:19:25 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:25 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:25 GMT+0000 : stdout: Dump file created for new Schema: 'du_dms_rc202507110517530020671752211104147_*********'

Fri Jul 11 2025 05:19:25 GMT+0000 : stdout: OLD_SCHEMA2 ::: du_dms_rc202507110517530020671752211104147_*********
Processor status: 16/16 Moving Work to Bundle Directory Started

Fri Jul 11 2025 05:19:26 GMT+0000 : stdout: [1;36mMoving all input files under the store to archive[0m

Fri Jul 11 2025 05:19:26 GMT+0000 : stdout: replacedElement:*_199001288329074__01_01_1_*.gz
199001288329074__01_01_1

Fri Jul 11 2025 05:19:26 GMT+0000 : stdout: archiveFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/archive

Fri Jul 11 2025 05:19:26 GMT+0000 : stdout: inputFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/*_199001288329074__01_01_1_*.gz
The inputFilePath does not contain 'archive@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@'.
[1;36mMoving Work to Bundle Directory[0m

Fri Jul 11 2025 05:19:30 GMT+0000 : stdout: [1;36mDistribute Function Result: 0
Fri Jul 11 2025 05:19:30 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:19:30 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:30 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:30 GMT+0000 : stdout: Processor status: 16/16 Moving Work to Bundle Directory Completed

Fri Jul 11 2025 05:19:31 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:19:31 GMT+0000 : processorStatus
Fri Jul 11 2025 05:19:31 GMT+0000 : stdout: Processor status: Processing Completed

Fri Jul 11 2025 05:19:32 GMT+0000 : stdout: [1;36mClearing working directory and remove input if requested[0m

Fri Jul 11 2025 05:19:32 GMT+0000 : stdout: [1;32mZapping Input Zip File As Requested[0m

Fri Jul 11 2025 05:19:32 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:19:32 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:19:32 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:19:32 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:19:32 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:19:32 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:19:32 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:19:32 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 0
Fri Jul 11 2025 05:19:32 GMT+0000 : Job saved to DB {"_id":"68709ea0eb5a72065669816b","name":"REYNOLDSRCI-PROCESS-JSON","data":{"inputFile":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","createdAt":"_01_01_20250711051753","operation":"json-processing","storeID":"936351325615505","inputFilePath1":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","processorUniqueId":"rc20250711051753002067-1752211104147","outputFile":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip & /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/etl/PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753-ETL.zip","status":true,"message":"Success","warningMessage":{"scheduled_by":"<EMAIL>","invalidmiscpaytypeCount":17,"invalidmiscpaytypeFilePath":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv","estimateCount":94,"punchTimeMissingCount":"34.38\n","PUNCH_TIME_MISSING_FILEPATH":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv"},"invalidmiscpaytypeCount":17,"estimateCount":94,"punchTimeMissingCount":"34.38\n","suffixedInvoicesCsvData":""},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":"2025-07-11T05:19:32.292Z","type":"normal","nextRunAt":null,"lastRunAt":"2025-07-11T05:18:24.143Z"}
Fri Jul 11 2025 05:19:55 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:19:55 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:19:55 GMT+0000 : Reynolds:  processing distribution
Fri Jul 11 2025 05:19:55 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip[0m

Fri Jul 11 2025 05:19:55 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip[0m

Fri Jul 11 2025 05:19:56 GMT+0000 : stdout: This store has import halt exception

Fri Jul 11 2025 05:19:59 GMT+0000 : stdout: Zip file /etl/audit-import-halt/PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753_rc20250711051753002067-1752211104147_*********-ETL.zip copied to manual import dir /etl/scheduler-manual-import

Fri Jul 11 2025 05:19:59 GMT+0000 : stdout: [1;36mDistributing Full Bundle[0m

Fri Jul 11 2025 05:19:59 GMT+0000 : stdout: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip' -> '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip'

Fri Jul 11 2025 05:19:59 GMT+0000 : stdout: [1;32mBundle Distribution Completed[0m

Fri Jul 11 2025 05:19:59 GMT+0000 : stdout: [1;36mMoving Bundle to Archive[0m

Fri Jul 11 2025 05:19:59 GMT+0000 : close: Success
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:22:34 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z"}]
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:22:34 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:22:34 GMT+0000 : Reynolds : Extraction process for store QASREY523 exited code 0
Fri Jul 11 2025 05:22:59 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip to process now
Fri Jul 11 2025 05:22:59 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:59 GMT+0000 : Groupname : QAREY52
Fri Jul 11 2025 05:22:59 GMT+0000 : Location Id: : 936351325615505
Fri Jul 11 2025 05:22:59 GMT+0000 : storeName : QASREY523
Fri Jul 11 2025 05:22:59 GMT+0000 : extractedFileTimeStamp : _01_01_20250711052234
Fri Jul 11 2025 05:22:59 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:22:59 GMT+0000 : jobsTmp : [{"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-11T05:22:59.623Z"},{"_id":"68709e7feb5a72065669815e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:17:53.000Z","lastFinishedAt":"2025-07-11T05:17:53.060Z","type":"normal","nextRunAt":"2025-07-11T05:22:59.623Z"},{"_id":"68709f98eb5a720656698197","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:22:34.001Z","lastFinishedAt":"2025-07-11T05:22:34.087Z","type":"normal","nextRunAt":"2025-07-11T05:22:59.623Z"}]
Fri Jul 11 2025 05:22:59 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"68709f98eb5a720656698197","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:22:34.001Z","lastFinishedAt":"2025-07-11T05:22:34.087Z","type":"normal","nextRunAt":"2025-07-11T05:22:59.623Z"}
Fri Jul 11 2025 05:22:59 GMT+0000 : Location Id : 936351325615505
Fri Jul 11 2025 05:22:59 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:22:59 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:22:59 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:22:59 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:22:59 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:22:59 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:22:59 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:22:59 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:22:59 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:22:59 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:22:59 GMT+0000 : buildProxies : true
Fri Jul 11 2025 05:22:59 GMT+0000 : fopcStore : null
Fri Jul 11 2025 05:22:59 GMT+0000 : extractionId : 68709f98eb5a720656698197
Fri Jul 11 2025 05:22:59 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:22:59 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:22:59 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:22:59 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:22:59 GMT+0000 : customBranchName : null
Fri Jul 11 2025 05:22:59 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:22:59 GMT+0000 : companyIds : *********,*********,
Fri Jul 11 2025 05:22:59 GMT+0000 : companyObj : [object Object],[object Object]
Fri Jul 11 2025 05:22:59 GMT+0000 : uniqueId : rc20250711052234003879-1752211379624
Fri Jul 11 2025 05:22:59 GMT+0000 : testData : false
Fri Jul 11 2025 05:22:59 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:22:59 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:22:59 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:59 GMT+0000 : stdout: UUID: rc20250711052234003879-1752211379624
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Fri Jul 11 2025 05:22:59 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:22:59 GMT+0000 : processorStatus
Fri Jul 11 2025 05:22:59 GMT+0000 : stdout: Processor status: Processing Started

Fri Jul 11 2025 05:23:00 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:23:00 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip[0m

Fri Jul 11 2025 05:23:00 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:00 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:00 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:23:01 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Fri Jul 11 2025 05:23:01 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:01 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:01 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:23:02 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:02 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:02 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:23:03 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:23:03 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:03 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:03 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:23:04 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:23:04 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:23:05 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:05 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:05 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:23:09 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml
Fri Jul 11 2025 05:23:09 GMT+0000 : stderr: [0m

Fri Jul 11 2025 05:23:09 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: Fri Jul 11 05:23:09 UTC 2025 Found # 29

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: Fri Jul 11 05:23:09 UTC 2025 Transform Begin

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: Fri Jul 11 05:23:09 UTC 2025 Transform End

Fri Jul 11 2025 05:23:09 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:09 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:09 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:23:10 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:23:10 GMT+0000 : stdout: Fri Jul 11 05:23:10 UTC 2025

Fri Jul 11 2025 05:23:10 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:23:20 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:20 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:20 GMT+0000 : stdout: Fri Jul 11 05:23:20 UTC 2025

Fri Jul 11 2025 05:23:20 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:23:21 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:23:21 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:21 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:21 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:23:22 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:23:22 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:23:22 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:22 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:22 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:23:22 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:23:23 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:23:23 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:23:23 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:23:23 GMT+0000 : stdout: [1;36mCreating exclusion reports
Fri Jul 11 2025 05:23:23 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:23:24 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:23:24 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:23:24 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: im_count:     0
im_exception:     0

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: im_count less than zero

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117
suffixed_invoices_count:1
punch time missing count :34.38 

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:23:24 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Fri Jul 11 2025 05:23:25 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:23:25 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:25 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:25 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:23:26 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting
Fri Jul 11 2025 05:23:26 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:23:26 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234-REPORT.zip-----

Fri Jul 11 2025 05:23:26 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:23:26 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:26 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:26 GMT+0000 : stdout:  (deflated 86%)

Fri Jul 11 2025 05:23:26 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:23:27 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Fri Jul 11 2025 05:23:27 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:27 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:27 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:23:27 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:23:28 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:23:28 GMT+0000 : processorStatus
Fri Jul 11 2025 05:23:28 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:23:28 GMT+0000 : stdout: COMPANY_ID: *********
Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:23:29 GMT+0000 : stderr: [1;31mCOMPANY_BRAND is empty or does not exist[0m

Fri Jul 11 2025 05:23:29 GMT+0000 : stdout: [1;33mMoving input to dead-letter bin: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed[0m

Fri Jul 11 2025 05:23:29 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : stderr: cp: cannot stat '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip'
Fri Jul 11 2025 05:23:29 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:23:29 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:23:29 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:23:29 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:23:29 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:23:29 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:23:29 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:23:29 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:23:29 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 1
Fri Jul 11 2025 05:23:29 GMT+0000 : Autosoft : filePath inpObj Error - QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip
Fri Jul 11 2025 05:23:29 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:22:34.003Z\",\"uniqueId\":\"rc20250711052234003879\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip\",\"endTime\":\"2025-07-11T05:22:34.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:23:29 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - {"inProjectId":[""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:22:34.003Z\",\"uniqueId\":\"rc20250711052234003879\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip\",\"endTime\":\"2025-07-11T05:22:34.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:23:29 GMT+0000 : Reynolds : doPayloadAction - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:22:34.003Z\",\"uniqueId\":\"rc20250711052234003879\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip\",\"endTime\":\"2025-07-11T05:22:34.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:23:29 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:23:29 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:23:31 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:23:31 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:23:31 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:24:50 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z"}]
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:24:50 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip
Fri Jul 11 2025 05:24:50 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:24:50 GMT+0000 : Reynolds : Extraction process for store QASREY521 exited code 0
Fri Jul 11 2025 05:25:31 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip to process now
Fri Jul 11 2025 05:25:31 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip
Fri Jul 11 2025 05:25:31 GMT+0000 : Groupname : QAREY52
Fri Jul 11 2025 05:25:31 GMT+0000 : Location Id: : 936351325615505
Fri Jul 11 2025 05:25:31 GMT+0000 : storeName : QASREY521
Fri Jul 11 2025 05:25:31 GMT+0000 : extractedFileTimeStamp : _01_01_20250711052450
Fri Jul 11 2025 05:25:31 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:25:31 GMT+0000 : jobsTmp : [{"_id":"6870a021eb5a7206566981bb","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:24:50.001Z","lastFinishedAt":"2025-07-11T05:24:50.069Z","type":"normal","nextRunAt":"2025-07-11T05:25:31.812Z"}]
Fri Jul 11 2025 05:25:31 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"6870a021eb5a7206566981bb","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:24:50.001Z","lastFinishedAt":"2025-07-11T05:24:50.069Z","type":"normal","nextRunAt":"2025-07-11T05:25:31.812Z"}
Fri Jul 11 2025 05:25:31 GMT+0000 : Location Id : 936351325615505
Fri Jul 11 2025 05:25:31 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:25:31 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:25:31 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:25:31 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:25:31 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [936351325615505,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:24:50.002Z","uniqueId":"rc20250711052450003761","processFileName":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","endTime":"2025-07-11T05:24:50.067Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:25:31 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:25:31 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:25:31 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:25:31 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:25:31 GMT+0000 : buildProxies : true
Fri Jul 11 2025 05:25:31 GMT+0000 : fopcStore : null
Fri Jul 11 2025 05:25:31 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:25:31 GMT+0000 : extractionId : 6870a021eb5a7206566981bb
Fri Jul 11 2025 05:25:31 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:25:31 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:25:31 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:25:31 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:25:31 GMT+0000 : customBranchName : null
Fri Jul 11 2025 05:25:31 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:25:31 GMT+0000 : uniqueId : rc20250711052450003761-1752211531813
Fri Jul 11 2025 05:25:31 GMT+0000 : companyIds : *********,
Fri Jul 11 2025 05:25:31 GMT+0000 : testData : false
Fri Jul 11 2025 05:25:31 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:25:31 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:25:31 GMT+0000 : companyObj : [object Object]
Fri Jul 11 2025 05:25:31 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip
Fri Jul 11 2025 05:25:31 GMT+0000 : stdout: UUID: rc20250711052450003761-1752211531813
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Fri Jul 11 2025 05:25:31 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:31 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:31 GMT+0000 : stdout: Processor status: Processing Started

Fri Jul 11 2025 05:25:32 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:25:32 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip[0m

Fri Jul 11 2025 05:25:32 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:32 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:32 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:25:33 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp
Fri Jul 11 2025 05:25:33 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:25:33 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:33 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:33 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:25:34 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:34 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:34 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:25:35 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:25:36 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:36 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:36 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:25:37 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:25:37 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:25:37 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:37 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:37 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:25:41 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml[0m

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: Fri Jul 11 05:25:41 UTC 2025 Found # 29

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: Fri Jul 11 05:25:41 UTC 2025 Transform Begin

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: Fri Jul 11 05:25:41 UTC 2025 Transform End

Fri Jul 11 2025 05:25:41 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:41 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:41 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:25:42 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:25:42 GMT+0000 : stdout: Fri Jul 11 05:25:42 UTC 2025

Fri Jul 11 2025 05:25:42 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:25:52 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:52 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:52 GMT+0000 : stdout: Fri Jul 11 05:25:52 UTC 2025

Fri Jul 11 2025 05:25:52 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:25:53 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:25:53 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:53 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:53 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:25:54 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:25:54 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:25:54 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:54 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:54 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:25:54 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:25:55 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:25:55 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: [1;36m
Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: Creating exclusion reports[0m

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:25:56 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:25:56 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:25:56 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: im_count:     0
im_exception:     0

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: im_count less than zero

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117
suffixed_invoices_count:1

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: punch time missing count :34.38 

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:25:56 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Fri Jul 11 2025 05:25:57 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:25:57 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:57 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:57 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:25:58 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting
Fri Jul 11 2025 05:25:58 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:25:58 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450-REPORT.zip-----

Fri Jul 11 2025 05:25:58 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:25:58 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:58 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:58 GMT+0000 : stdout:  (deflated 86%)

Fri Jul 11 2025 05:25:58 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:25:59 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data
Fri Jul 11 2025 05:25:59 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:25:59 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:25:59 GMT+0000 : processorStatus
Fri Jul 11 2025 05:25:59 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:25:59 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:26:00 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:00 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:00 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:26:00 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COMPANY_BRAND: GM

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COPY 2

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: INSERT 0 1

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: COPY 63

Fri Jul 11 2025 05:26:01 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:01 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:26:01 GMT+0000 : stdout: Processor status: 8/16 Load Fron Scheduler DB Completed

Fri Jul 11 2025 05:26:02 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:02 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:02 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Started

Fri Jul 11 2025 05:26:04 GMT+0000 : stdout: 
zip error: Nothing to do! (try: zip -q -jmr import-files.zip . -i ./import-files)

Fri Jul 11 2025 05:26:04 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:04 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:04 GMT+0000 : stdout: Processor status: 9/16 Compressing Directory Completed

Fri Jul 11 2025 05:26:05 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:05 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:05 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Started

Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: [1;36m05:26:06 : Ready to Generate Exception Analysis Report[0m

Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: CSV file '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/processing-result/All_exception_details.csv' already exists.

Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: File copied to /home/<USER>/tmp/du-etl-dms-automate-extractor-work/exception_tag/All_exception_details.csv

Fri Jul 11 2025 05:26:06 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:06 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: [1;36m05:26:06 : Done Generating Exception Analysis Report[0m

Fri Jul 11 2025 05:26:06 GMT+0000 : stdout: Processor status: 10/16 Generate Exception Analysis Completed

Fri Jul 11 2025 05:26:07 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:07 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:07 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Started

Fri Jul 11 2025 05:26:08 GMT+0000 : stdout: [1;36m05:26:08 : Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:26:08 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:08 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:08 GMT+0000 : stdout: [1;36m05:26:08 : Done Loading Exception Analysis Report[0m

Fri Jul 11 2025 05:26:08 GMT+0000 : stdout: Processor status: 11/16 Loading Exception Analysis Completed

Fri Jul 11 2025 05:26:09 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:09 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:09 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Started

Fri Jul 11 2025 05:26:10 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:10 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:10 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin: line 41: BASE_SRC_SCHEMA: unbound variable

Fri Jul 11 2025 05:26:10 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:10 GMT+0000 : stdout: Processor status: 12/16 Generating Proxy Repair Orders per Request Completed

Fri Jul 11 2025 05:26:11 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:11 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:11 GMT+0000 : stdout: Processor status: 13/16 Extracting Text ROs to TSV Started

Fri Jul 11 2025 05:26:12 GMT+0000 : stdout: [1;36m05:26:12 : Ready to Extract Text ROs to TSV[0m

Fri Jul 11 2025 05:26:12 GMT+0000 : stderr: Traceback (most recent call last):
  File "/home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/src/extract/python/./extract-text-proxy-to-tsv.py", line 14, in <module>

Fri Jul 11 2025 05:26:12 GMT+0000 : stderr:     for ro_file in os.listdir(proxy_file_path):
FileNotFoundError: [Errno 2] No such file or directory: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice/text'

Fri Jul 11 2025 05:26:12 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:12 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:12 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:12 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:12 GMT+0000 : stdout: [1;36m05:26:12 : Done Extracting Text ROs to TSV
Fri Jul 11 2025 05:26:12 GMT+0000 : stdout: [0m
Processor status: 13/16 Extracting Text ROs to TSV Completed

Fri Jul 11 2025 05:26:13 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:13 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:13 GMT+0000 : stdout: Processor status: 14/16 Compressing Proxy Directory Started

Fri Jul 11 2025 05:26:14 GMT+0000 : stdout: [1;36mcompress /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice
Fri Jul 11 2025 05:26:14 GMT+0000 : stdout: [0m
/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice

Fri Jul 11 2025 05:26:14 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 151: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:26:14 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:14 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:14 GMT+0000 : stderr: /home/<USER>/DU-ETL/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-proxyinvoice.bash-mixin: line 181: cd: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/proxy-invoice: No such file or directory

Fri Jul 11 2025 05:26:14 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:14 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:14 GMT+0000 : stdout: Processor status: 14/16 Compressing Directory Completed

Fri Jul 11 2025 05:26:15 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:15 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:15 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Started

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: [1;36mGenerating Store Specific Rules
Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: Paytype import halt checking

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: Department import halt checking

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: Inv Seq halt checking

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: Unassigned Make import halt checking

Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: paytype_halt     true
inv_seq_halt     true
make_halt        false
deptment_halt    false

Fri Jul 11 2025 05:26:16 GMT+0000 : stderr: rm: cannot remove '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/halt-import.txt'
Fri Jul 11 2025 05:26:16 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:26:16 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:16 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: COMPANY_ID: *********
[1;36mInserting Halt status to database[0m

Fri Jul 11 2025 05:26:16 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:16 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:16 GMT+0000 : stdout: Processor status: 15/16 Pre-import Halt Detection Completed

Fri Jul 11 2025 05:26:17 GMT+0000 : stdout: INPUT_TYPE ::: json
OLD_SCHEMA1 ::: du_dms_reynoldsrci_model
COMPANY_ID: *********

Fri Jul 11 2025 05:26:17 GMT+0000 : stdout: NEW_SCHEMA ::: du_dms_rc202507110524500037611752211531813_*********

Fri Jul 11 2025 05:26:17 GMT+0000 : stdout: OLD_SCHEMA ::: du_dms_reynoldsrci_model
NEW_SCHEMA ::: du_dms_rc202507110524500037611752211531813_*********
[1;36mProcessing schema duplication[0m

Fri Jul 11 2025 05:26:17 GMT+0000 : stderr: NOTICE:  schema "du_dms_rc202507110524500037611752211531813_*********" does not exist, skipping

Fri Jul 11 2025 05:26:17 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:26:33 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:33 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:33 GMT+0000 : stdout: Dump file created for new Schema: 'du_dms_rc202507110524500037611752211531813_*********'

Fri Jul 11 2025 05:26:33 GMT+0000 : stdout: OLD_SCHEMA2 ::: du_dms_rc202507110524500037611752211531813_*********
Processor status: 16/16 Moving Work to Bundle Directory Started

Fri Jul 11 2025 05:26:34 GMT+0000 : stdout: [1;36mMoving all input files under the store to archive[0m

Fri Jul 11 2025 05:26:34 GMT+0000 : stdout: replacedElement:*_199001288329074__01_01_1_*.gz
199001288329074__01_01_1

Fri Jul 11 2025 05:26:34 GMT+0000 : stdout: archiveFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/archive

Fri Jul 11 2025 05:26:34 GMT+0000 : stdout: inputFilePath:/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/*_199001288329074__01_01_1_*.gz
The inputFilePath does not contain 'archive@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@'.
[1;36mMoving Work to Bundle Directory[0m

Fri Jul 11 2025 05:26:37 GMT+0000 : stdout: [1;36mDistribute Function Result: 0[0m

Fri Jul 11 2025 05:26:37 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:37 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:37 GMT+0000 : stdout: Processor status: 16/16 Moving Work to Bundle Directory Completed

Fri Jul 11 2025 05:26:38 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:26:38 GMT+0000 : processorStatus
Fri Jul 11 2025 05:26:38 GMT+0000 : stdout: Processor status: Processing Completed

Fri Jul 11 2025 05:26:39 GMT+0000 : stdout: [1;36mClearing working directory and remove input if requested[0m

Fri Jul 11 2025 05:26:39 GMT+0000 : stdout: [1;32mZapping Input Zip File As Requested[0m

Fri Jul 11 2025 05:26:39 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:26:39 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:26:39 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:26:39 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:26:39 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:26:39 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:26:39 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:26:39 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 0
Fri Jul 11 2025 05:26:39 GMT+0000 : Job saved to DB {"_id":"6870a04beb5a7206566981c0","name":"REYNOLDSRCI-PROCESS-JSON","data":{"inputFile":"QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip","createdAt":"_01_01_20250711052450","operation":"json-processing","storeID":"936351325615505","inputFilePath1":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","processorUniqueId":"rc20250711052450003761-1752211531813","outputFile":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip & /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/etl/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450-ETL.zip","status":true,"message":"Success","warningMessage":{"scheduled_by":"<EMAIL>","invalidmiscpaytypeCount":17,"invalidmiscpaytypeFilePath":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv","estimateCount":94,"punchTimeMissingCount":"34.38\n","PUNCH_TIME_MISSING_FILEPATH":"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv"},"invalidmiscpaytypeCount":17,"estimateCount":94,"punchTimeMissingCount":"34.38\n","suffixedInvoicesCsvData":""},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":"2025-07-11T05:26:39.957Z","type":"normal","nextRunAt":null,"lastRunAt":"2025-07-11T05:25:31.809Z"}
Fri Jul 11 2025 05:27:03 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:27:03 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:27:03 GMT+0000 : Reynolds:  processing distribution
Fri Jul 11 2025 05:27:03 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip[0m

Fri Jul 11 2025 05:27:03 GMT+0000 : stdout: [1;36mDistributing ETL Data: PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip[0m

Fri Jul 11 2025 05:27:03 GMT+0000 : stdout: This store has import halt exception

Fri Jul 11 2025 05:27:07 GMT+0000 : stdout: Zip file /etl/audit-import-halt/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450_rc20250711052450003761-1752211531813_*********-ETL.zip copied to manual import dir /etl/scheduler-manual-import

Fri Jul 11 2025 05:27:07 GMT+0000 : stdout: [1;36mDistributing Full Bundle[0m

Fri Jul 11 2025 05:27:07 GMT+0000 : stdout: '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip' -> '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-936351325615505-_01_01_20250711052450.zip'

Fri Jul 11 2025 05:27:07 GMT+0000 : stdout: [1;32mBundle Distribution Completed[0m

Fri Jul 11 2025 05:27:07 GMT+0000 : stdout: [1;36mMoving Bundle to Archive[0m

Fri Jul 11 2025 05:27:07 GMT+0000 : close: Success
Fri Jul 11 2025 05:39:34 GMT+0000 : Reynolds-Extract job started: JobName: REYNOLDS, priority:highest, concurrency:20
Fri Jul 11 2025 05:39:34 GMT+0000 : Reynolds : Process JSON job started: JobName: REYNOLDSRCI-PROCESS-JSON, priority:highest, concurrency:1
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds : Check time frame and start extraction {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11"}
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds: Extraction Job Started: {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":""}
Fri Jul 11 2025 05:44:25 GMT+0000 : Extraction Job Data: [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z"}]
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:storeIdentification : 199001288329074__01_01_1
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:filePath : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:file name: : RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:gunzip done! : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/RRO_D_RO_ArmatusDealerUplift_199001288329074__01_01_1_2d50bdff-f433-4c55-9fb6-66066bb4d4e2_20250128092738.gz
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:Unzip Completed done!
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds:unzip webhook input files completed
Fri Jul 11 2025 05:44:25 GMT+0000 : processCompanyData ...999 job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zipdestinationFile : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : Job close: n/a
Fri Jul 11 2025 05:44:25 GMT+0000 : Reynolds : Extraction process for store QASREY523 exited code 0
Fri Jul 11 2025 05:44:37 GMT+0000 : Reynolds : Found one Store extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip to process now
Fri Jul 11 2025 05:44:37 GMT+0000 : Reynolds : Process JSON schedule started with file > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:37 GMT+0000 : Groupname : QAREY52
Fri Jul 11 2025 05:44:37 GMT+0000 : Location Id: : 936351325615505
Fri Jul 11 2025 05:44:37 GMT+0000 : storeName : QASREY523
Fri Jul 11 2025 05:44:37 GMT+0000 : extractedFileTimeStamp : _01_01_20250711054425
Fri Jul 11 2025 05:44:37 GMT+0000 : extractedFileCreationDate : Invalid date
Fri Jul 11 2025 05:44:37 GMT+0000 : jobsTmp : [{"_id":"68709dd5eb5a72065669813e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:15:03.002Z","uniqueId":"rc20250711051503002053","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051503.zip","endTime":"2025-07-11T05:15:03.080Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:15:03.000Z","lastFinishedAt":"2025-07-11T05:15:03.085Z","type":"normal","nextRunAt":"2025-07-11T05:44:37.898Z"},{"_id":"68709e7feb5a72065669815e","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:17:53.002Z","uniqueId":"rc20250711051753002067","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711051753.zip","endTime":"2025-07-11T05:17:53.059Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:17:53.000Z","lastFinishedAt":"2025-07-11T05:17:53.060Z","type":"normal","nextRunAt":"2025-07-11T05:44:37.898Z"},{"_id":"68709f98eb5a720656698197","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:22:34.003Z","uniqueId":"rc20250711052234003879","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711052234.zip","endTime":"2025-07-11T05:22:34.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:22:34.001Z","lastFinishedAt":"2025-07-11T05:22:34.087Z","type":"normal","nextRunAt":"2025-07-11T05:44:37.898Z"},{"_id":"6870a4b75d85ea3b100554fd","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:44:25.001Z","lastFinishedAt":"2025-07-11T05:44:25.087Z","type":"normal","nextRunAt":"2025-07-11T05:44:37.898Z"}]
Fri Jul 11 2025 05:44:37 GMT+0000 : jobsTmp[jobsTmp.length-1] : {"_id":"6870a4b75d85ea3b100554fd","name":"REYNOLDS","data":{"groupName":"QAREY52","storeDataArray":[{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]},"priority":20,"shouldSaveResult":false,"lastModifiedBy":null,"lockedAt":null,"lastRunAt":"2025-07-11T05:44:25.001Z","lastFinishedAt":"2025-07-11T05:44:25.087Z","type":"normal","nextRunAt":"2025-07-11T05:44:37.898Z"}
Fri Jul 11 2025 05:44:37 GMT+0000 : Location Id : 936351325615505
Fri Jul 11 2025 05:44:37 GMT+0000 : jobsTmp[jobsTmp.length-1].attrs.data.storeDataArray : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:44:37 GMT+0000 : agendaObject : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:44:37 GMT+0000 : Sorted agenda object : [{"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}]
Fri Jul 11 2025 05:44:37 GMT+0000 : extractedObjectIndex : 0
Fri Jul 11 2025 05:44:37 GMT+0000 : updateSolve360Data : [object Object]
Fri Jul 11 2025 05:44:37 GMT+0000 : projectId : *********
Fri Jul 11 2025 05:44:37 GMT+0000 : secondProjectId : 
Fri Jul 11 2025 05:44:37 GMT+0000 : Extracted agenda object : {"locationId":"936351325615505","sourceId":"936351325615505","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [936351325615505,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"936351325615505","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*","inProjectId":"*********","in_is_update_retrieve_ro":false,"in_data_pulled_via":"Scheduler","in_retrive_ro_request_on":"2025-07-11","invoiceMasterCSVFilePath":"","startTime":"2025-07-11T05:44:25.003Z","uniqueId":"rc20250711054425004693","processFileName":"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip","endTime":"2025-07-11T05:44:25.086Z","status":true,"message":"n/a"}
Fri Jul 11 2025 05:44:37 GMT+0000 : userName : <EMAIL>
Fri Jul 11 2025 05:44:37 GMT+0000 : solve360Update : false
Fri Jul 11 2025 05:44:37 GMT+0000 : buildProxies : false
Fri Jul 11 2025 05:44:37 GMT+0000 : extractionId : 6870a4b75d85ea3b100554fd
Fri Jul 11 2025 05:44:37 GMT+0000 : fopcStore : null
Fri Jul 11 2025 05:44:37 GMT+0000 : invoiceMasterCSVFilePath : 
Fri Jul 11 2025 05:44:37 GMT+0000 : inputStoreName : /etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1
Fri Jul 11 2025 05:44:37 GMT+0000 : etlDMSType : ReynoldsRCI
Fri Jul 11 2025 05:44:37 GMT+0000 : customBranchName : null
Fri Jul 11 2025 05:44:37 GMT+0000 : switchBranch : true
Fri Jul 11 2025 05:44:37 GMT+0000 : uniqueId : rc20250711054425004693-1752212677899
Fri Jul 11 2025 05:44:37 GMT+0000 : companyIds : *********,*********,
Fri Jul 11 2025 05:44:37 GMT+0000 : companyObj : [object Object],[object Object]
Fri Jul 11 2025 05:44:37 GMT+0000 : haltOverRide : false
Fri Jul 11 2025 05:44:37 GMT+0000 : testData : false
Fri Jul 11 2025 05:44:37 GMT+0000 : mageManufacturer : GM
Fri Jul 11 2025 05:44:37 GMT+0000 : isPorscheStore : undefined
Fri Jul 11 2025 05:44:37 GMT+0000 : Reynolds : Start processing of extraction > QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:37 GMT+0000 : stdout: UUID: rc20250711054425004693-1752212677899
PERFORMED_BY: <EMAIL>
EXCEPTION_REPORT: true

Fri Jul 11 2025 05:44:37 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:37 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:37 GMT+0000 : stdout: Processor status: Processing Started

Fri Jul 11 2025 05:44:38 GMT+0000 : stdout: HALT_OVER_RIDE:false

Fri Jul 11 2025 05:44:38 GMT+0000 : stdout: [1;32mProcessing Input Zip Archive: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip[0m

Fri Jul 11 2025 05:44:38 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:38 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:38 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Started

Fri Jul 11 2025 05:44:39 GMT+0000 : stdout: [1;36mUnzipping Input to Work /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp[0m

Fri Jul 11 2025 05:44:39 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:39 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:39 GMT+0000 : stdout: Processor status: 1/16 Unzipping Input to Work Completed

Fri Jul 11 2025 05:44:40 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:40 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:40 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Started

Fri Jul 11 2025 05:44:41 GMT+0000 : stdout: [1;36mCreating Schema from Models[0m

Fri Jul 11 2025 05:44:42 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:42 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:42 GMT+0000 : stdout: Processor status: 2/16 Creating Schema from Model Completed

Fri Jul 11 2025 05:44:43 GMT+0000 : stdout: [1;36mSaving UUID to model[0m

Fri Jul 11 2025 05:44:43 GMT+0000 : stdout: [1;36mGenerating JQ Transforms[0m

Fri Jul 11 2025 05:44:43 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:43 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:43 GMT+0000 : stdout: Processor status: 3/16 Iterating Over Zip File Contents Started

Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: [1;36mIterating Over Zip File Contents[0m

Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: [1;36mClosed RO JSON[0m

Fri Jul 11 2025 05:44:47 GMT+0000 : stderr: [1;36mBeginning zip processing in /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/process-json-temp/zip-temp/jsonconversions ./reynolds0.xml
Fri Jul 11 2025 05:44:47 GMT+0000 : stderr: [0m

Fri Jul 11 2025 05:44:47 GMT+0000 : /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip was copied to /home/<USER>/tmp/du-etl-dms-dealertrack-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: [1;36mConvert ./reynolds0.xml to json file[0m

Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: Fri Jul 11 05:44:47 UTC 2025 Found # 29

Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: Fri Jul 11 05:44:47 UTC 2025 Transform Begin

Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: RO_COUNT is greater than 1

Fri Jul 11 2025 05:44:47 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: Fri Jul 11 05:44:47 UTC 2025 Transform End

Fri Jul 11 2025 05:44:47 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:47 GMT+0000 : stdout: Processor status: 4/16 Loading Individual ROs Started

Fri Jul 11 2025 05:44:48 GMT+0000 : stdout: [1;36mLoading Individual ROs (can take a while)[0m

Fri Jul 11 2025 05:44:48 GMT+0000 : stdout: Fri Jul 11 05:44:48 UTC 2025

Fri Jul 11 2025 05:44:48 GMT+0000 : stdout: [1;36mReady to Load remaining ROs[0m

Fri Jul 11 2025 05:44:58 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:58 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:58 GMT+0000 : stdout: Fri Jul 11 05:44:58 UTC 2025

Fri Jul 11 2025 05:44:58 GMT+0000 : stdout: [1;36mIndividual ROs Imported[0m
Processor status: 4/16 Loading Individual ROs Completed

Fri Jul 11 2025 05:44:59 GMT+0000 : stdout: [93m
[1mNo CCC Exception

Fri Jul 11 2025 05:44:59 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:44:59 GMT+0000 : processorStatus
Fri Jul 11 2025 05:44:59 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Started

Fri Jul 11 2025 05:45:00 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:45:00 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:45:01 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:45:01 GMT+0000 : processorStatus
Fri Jul 11 2025 05:45:01 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:45:01 GMT+0000 : stdout: Processor status: 5/16 Detecting Problematic ROs Completed

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mDetecting Problematic ROs[0m

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout:  roNumber | comment 
----------+---------
(0 rows)


Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: ROs:

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mCreating exclusion reports[0m

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mGenerating exception report[0m

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mEnumerating ROs[0m

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout:  count_all | count_non_numeric 
-----------+-------------------
        29 |                 0
(1 row)


Fri Jul 11 2025 05:45:02 GMT+0000 : Total Ros Count55555555555,Total Ros Count:-    29

Fri Jul 11 2025 05:45:02 GMT+0000 : Total Ro90999999999,-    29

Fri Jul 11 2025 05:45:02 GMT+0000 : totalRoCount666666,    29

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: Total Ros Count:-    29

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: im_count:     0
im_exception:     0

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: im_count less than zero

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: misc_ro_count:    117
estimate:    117
suffixed_invoices_count:1

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: punch time missing count :34.38 

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: misc_ro_count_numeric: 117
misc_ro_count:   117
misc_ro_count_numeric:117

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: suffixed_invoices_count_numeric:0

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mFinding Missing RO  with respective to Invoice master CSV file[0m

Fri Jul 11 2025 05:45:02 GMT+0000 : stdout: [1;36mPersisting and Exporting Data and Schema[0m

Fri Jul 11 2025 05:45:03 GMT+0000 : stdout: [1;36mCreating New Status File![0m

Fri Jul 11 2025 05:45:03 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:45:03 GMT+0000 : processorStatus
Fri Jul 11 2025 05:45:03 GMT+0000 : stdout: Processor status: 6/16 Detecting Open/Void RO data and reporting Started

Fri Jul 11 2025 05:45:04 GMT+0000 : stdout: [1;36mDetecting Open/Void RO data and reporting[0m

Fri Jul 11 2025 05:45:04 GMT+0000 : stdout: -----COPY RCI REPORT FILE TO AUDIT DIRECTORY----------PROC-QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425-REPORT.zip-----

Fri Jul 11 2025 05:45:04 GMT+0000 : stdout:   adding: invoice-master.csv
Fri Jul 11 2025 05:45:04 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:45:04 GMT+0000 : processorStatus
Fri Jul 11 2025 05:45:04 GMT+0000 : stdout:  (deflated 86%)
Processor status: 6/16 Detecting Open/Void RO data and reporting Completed

Fri Jul 11 2025 05:45:05 GMT+0000 : stdout: [1;36mChecking for Missing ROs in Original Raw Data[0m

Fri Jul 11 2025 05:45:05 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:45:05 GMT+0000 : processorStatus
Fri Jul 11 2025 05:45:05 GMT+0000 : stdout: [1;36mNo missing RO#s found in extraction data[0m

Fri Jul 11 2025 05:45:05 GMT+0000 : stdout: Processor status: 7/16 Generate Config File Started

Fri Jul 11 2025 05:45:06 GMT+0000 : Processor statu for UI
Fri Jul 11 2025 05:45:06 GMT+0000 : processorStatus
Fri Jul 11 2025 05:45:06 GMT+0000 : stdout: COMPANY_ID: *********

Fri Jul 11 2025 05:45:06 GMT+0000 : stdout: COMPANY_ID: *********
Processor status: 8/16 Load Fron Scheduler DB Started

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: [1;36mLoad data in Scheduler import database[0m

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: COPY 49

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: COPY 182

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: CREATE TABLE

Fri Jul 11 2025 05:45:07 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:45:08 GMT+0000 : stdout: TRUNCATE TABLE

Fri Jul 11 2025 05:45:08 GMT+0000 : stderr: [1;31mCOMPANY_BRAND is empty or does not exist[0m

Fri Jul 11 2025 05:45:08 GMT+0000 : stdout: [1;33mMoving input to dead-letter bin: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/dead-letter-processed
Fri Jul 11 2025 05:45:08 GMT+0000 : stdout: [0m

Fri Jul 11 2025 05:45:08 GMT+0000 : stderr: cp: cannot stat '/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip'
Fri Jul 11 2025 05:45:08 GMT+0000 : Error: File not found at path /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception_tag/All_exception_details.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : The invalid Core Cost Sale Mismatch File Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : stderr: : No such file or directory

Fri Jul 11 2025 05:45:08 GMT+0000 : invalidmiscpaytypeArray.length: 17
Fri Jul 11 2025 05:45:08 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:45:08 GMT+0000 : The estimate Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/estimate.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : estimateArray.length: 94
Fri Jul 11 2025 05:45:08 GMT+0000 : The Punch Time Missing Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing_percentage.txt
Fri Jul 11 2025 05:45:08 GMT+0000 : invalidmiscpaytypeCount: 17
Fri Jul 11 2025 05:45:08 GMT+0000 : punchTimeMissingCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The suffixedInvoices Csv File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/suffixed-invoices.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : suffixedInvoicesCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/exception-closed-invoices.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : exceptionClosedInvoicesCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The Exception Closed Invoices File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The Extra Ro in Xml Exception File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/extraros_in_xml.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : extraRoInXmlExceptionCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The imOpendedClosedRciRos File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/im_opened_closed_rci_ros.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : imOpenedClosedRciRosCount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : The deletedRosFilepath File exists: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/removed_ros.csv
Fri Jul 11 2025 05:45:08 GMT+0000 : deletedRoscount: undefined
Fri Jul 11 2025 05:45:08 GMT+0000 : Reynolds : JSON processing job for Store undefined exited with code 1
Fri Jul 11 2025 05:45:08 GMT+0000 : Autosoft : filePath inpObj Error - QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip
Fri Jul 11 2025 05:45:08 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:44:25.003Z\",\"uniqueId\":\"rc20250711054425004693\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip\",\"endTime\":\"2025-07-11T05:44:25.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:45:08 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - {"inProjectId":[""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:44:25.003Z\",\"uniqueId\":\"rc20250711054425004693\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip\",\"endTime\":\"2025-07-11T05:44:25.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:45:08 GMT+0000 : Reynolds : doPayloadAction - {"inProjectId":["*********","*********",""],"inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"936351325615505\",\"sourceId\":\"936351325615505\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [936351325615505,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"936351325615505\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:44:25.003Z\",\"uniqueId\":\"rc20250711054425004693\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-936351325615505-_01_01_20250711054425.zip\",\"endTime\":\"2025-07-11T05:44:25.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:45:08 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:45:08 GMT+0000 : REYNOLDS Schedule portal call with Project Id FAILURE*********
Fri Jul 11 2025 05:45:10 GMT+0000 : Call method for SharePoint data upload
Fri Jul 11 2025 05:45:10 GMT+0000 : Call for next job selection
Fri Jul 11 2025 05:45:10 GMT+0000 : Call for next job selection
