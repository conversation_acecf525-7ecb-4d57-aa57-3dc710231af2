Fri Jul 11 2025 03:47:57 GMT+0000 : CDK : Process XML schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : CDKFLEX : Process XML schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Automate :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Fortellis :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : DealerBuilt :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Adam :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Reynolds :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Autosoft :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Pbs :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable
Fri Jul 11 2025 03:47:57 GMT+0000 : Tekion :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : <PERSON><PERSON><PERSON><PERSON> :  Process JSON schedule started
Fri Jul 11 2025 03:47:57 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 03:48:00 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 03:48:00 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 03:48:07 GMT+0000 : CDK : Process XML schedule started
Fri Jul 11 2025 03:48:07 GMT+0000 : CDKFLEX : Process XML schedule started
Fri Jul 11 2025 03:48:07 GMT+0000 : Automate :  Process JSON schedule started
Fri Jul 11 2025 03:48:07 GMT+0000 : Fortellis :  Process JSON schedule started
Fri Jul 11 2025 03:48:07 GMT+0000 : DealerBuilt :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Adam :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Reynolds :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Autosoft :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Pbs :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable
Fri Jul 11 2025 03:48:09 GMT+0000 : Tekion :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Tekionapi :  Process JSON schedule started
Fri Jul 11 2025 03:48:09 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 03:55:59 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:jul2-jul2-St-INITIAL-3PA71024-20250711035457.zipfilePath:/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/jul2-jul2-St-INITIAL-3PA71024-20250711035457.zipJOB_TYPE:CDK3PA
Fri Jul 11 2025 03:55:59 GMT+0000 : createConfigFile inputData: {"dms":"CDK3PA","mageGrpData":{"state":"St","mageGroupCode":"","mageGroupName":"jul2","mageStoreName":"","mageStoreCode":"jul2","mageProjectId":"","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"","mageProjectType":"","secondaryProjectType":"","secondaryProjectName":"","companyId":"0","userName":"<EMAIL>","schedulerId":"cd20250711035456463323","sourceFile":"jul2-jul2-St-INITIAL-3PA71024-20250711035457.zip"}}
Fri Jul 11 2025 03:55:59 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/jul2-jul2-St-INITIAL-3PA71024-20250711035457.zip
Fri Jul 11 2025 03:55:59 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 03:55:59 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='CDK3PA'
source "$DU_ETL_HOME/DU-DMS/DMS-CDK3PA/CDK3PA.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDK3PA}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDK3PA
#Base DMS: CDK3PA (optional)

DMS="CDK3PA"
DATE_PART='202507'

GROUP_CODE=""
GROUPNAME="jul2"
STORENAME=""
STORE_PART="jul2"
MFG="GM"
STATE='St'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="cd20250711035456463323"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="jul2-jul2-St-INITIAL-3PA71024-20250711035457.zip"
Fri Jul 11 2025 03:56:00 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/jul2-jul2-St-INITIAL-3PA71024-20250711035457.zip
Fri Jul 11 2025 03:56:00 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 03:56:01 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/jul2-jul2-St-INITIAL-3PA71024-20250711035457.zip
Fri Jul 11 2025 03:56:01 GMT+0000 : Inside send mail method
Fri Jul 11 2025 03:56:01 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 03:57:16 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:july3-july3-St-INITIAL-3PA71024-20250711035621.zipfilePath:/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july3-july3-St-INITIAL-3PA71024-20250711035621.zipJOB_TYPE:CDK3PA
Fri Jul 11 2025 03:57:16 GMT+0000 : createConfigFile inputData: {"dms":"CDK3PA","mageGrpData":{"state":"St","mageGroupCode":"","mageGroupName":"july3","mageStoreName":"","mageStoreCode":"july3","mageProjectId":"","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"","mageProjectType":"","secondaryProjectType":"","secondaryProjectName":"","companyId":"0","userName":"<EMAIL>","schedulerId":"cd20250711035621525295","sourceFile":"july3-july3-St-INITIAL-3PA71024-20250711035621.zip"}}
Fri Jul 11 2025 03:57:16 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july3-july3-St-INITIAL-3PA71024-20250711035621.zip
Fri Jul 11 2025 03:57:16 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 03:57:16 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='CDK3PA'
source "$DU_ETL_HOME/DU-DMS/DMS-CDK3PA/CDK3PA.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDK3PA}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDK3PA
#Base DMS: CDK3PA (optional)

DMS="CDK3PA"
DATE_PART='202507'

GROUP_CODE=""
GROUPNAME="july3"
STORENAME=""
STORE_PART="july3"
MFG="GM"
STATE='St'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="cd20250711035621525295"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="july3-july3-St-INITIAL-3PA71024-20250711035621.zip"
Fri Jul 11 2025 03:57:16 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july3-july3-St-INITIAL-3PA71024-20250711035621.zip
Fri Jul 11 2025 03:57:16 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 03:57:17 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july3-july3-St-INITIAL-3PA71024-20250711035621.zip
Fri Jul 11 2025 03:57:17 GMT+0000 : Inside send mail method
Fri Jul 11 2025 03:57:17 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 03:58:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 03:58:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:01:04 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:july4-july4-St-INITIAL-3PA71024-20250711040008.zipfilePath:/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july4-july4-St-INITIAL-3PA71024-20250711040008.zipJOB_TYPE:CDK3PA
Fri Jul 11 2025 04:01:04 GMT+0000 : createConfigFile inputData: {"dms":"CDK3PA","mageGrpData":{"state":"St","mageGroupCode":"","mageGroupName":"july4","mageStoreName":"","mageStoreCode":"july4","mageProjectId":"","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"","mageProjectType":"","secondaryProjectType":"","secondaryProjectName":"","companyId":"0","userName":"<EMAIL>","schedulerId":"cd20250711040008189051","sourceFile":"july4-july4-St-INITIAL-3PA71024-20250711040008.zip"}}
Fri Jul 11 2025 04:01:04 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july4-july4-St-INITIAL-3PA71024-20250711040008.zip
Fri Jul 11 2025 04:01:04 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 04:01:04 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='CDK3PA'
source "$DU_ETL_HOME/DU-DMS/DMS-CDK3PA/CDK3PA.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDK3PA}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDK3PA
#Base DMS: CDK3PA (optional)

DMS="CDK3PA"
DATE_PART='202507'

GROUP_CODE=""
GROUPNAME="july4"
STORENAME=""
STORE_PART="july4"
MFG="GM"
STATE='St'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="cd20250711040008189051"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="july4-july4-St-INITIAL-3PA71024-20250711040008.zip"
Fri Jul 11 2025 04:01:05 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july4-july4-St-INITIAL-3PA71024-20250711040008.zip
Fri Jul 11 2025 04:01:05 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 04:01:05 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july4-july4-St-INITIAL-3PA71024-20250711040008.zip
Fri Jul 11 2025 04:01:05 GMT+0000 : Inside send mail method
Fri Jul 11 2025 04:01:05 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 04:04:26 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:july10-july10-St-INITIAL-3PA71024-20250711040231.zipfilePath:/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july10-july10-St-INITIAL-3PA71024-20250711040231.zipJOB_TYPE:CDK3PA
Fri Jul 11 2025 04:04:26 GMT+0000 : createConfigFile inputData: {"dms":"CDK3PA","mageGrpData":{"state":"St","mageGroupCode":"","mageGroupName":"july10","mageStoreName":"","mageStoreCode":"july10","mageProjectId":"","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"","mageProjectType":"","secondaryProjectType":"","secondaryProjectName":"","companyId":"0","userName":"<EMAIL>","schedulerId":"cd20250711040231514878","sourceFile":"july10-july10-St-INITIAL-3PA71024-20250711040231.zip"}}
Fri Jul 11 2025 04:04:26 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july10-july10-St-INITIAL-3PA71024-20250711040231.zip
Fri Jul 11 2025 04:04:26 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 04:04:26 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='CDK3PA'
source "$DU_ETL_HOME/DU-DMS/DMS-CDK3PA/CDK3PA.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDK3PA}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDK3PA
#Base DMS: CDK3PA (optional)

DMS="CDK3PA"
DATE_PART='202507'

GROUP_CODE=""
GROUPNAME="july10"
STORENAME=""
STORE_PART="july10"
MFG="GM"
STATE='St'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="cd20250711040231514878"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="july10-july10-St-INITIAL-3PA71024-20250711040231.zip"
Fri Jul 11 2025 04:04:27 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july10-july10-St-INITIAL-3PA71024-20250711040231.zip
Fri Jul 11 2025 04:04:27 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 04:04:28 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july10-july10-St-INITIAL-3PA71024-20250711040231.zip
Fri Jul 11 2025 04:04:28 GMT+0000 : Inside send mail method
Fri Jul 11 2025 04:04:28 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 04:08:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:08:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:18:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:18:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:28:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:28:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:29:48 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:july11-july11-St-INITIAL-3PA71024-20250711042856.zipfilePath:/home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july11-july11-St-INITIAL-3PA71024-20250711042856.zipJOB_TYPE:CDK3PA
Fri Jul 11 2025 04:29:48 GMT+0000 : createConfigFile inputData: {"dms":"CDK3PA","mageGrpData":{"state":"St","mageGroupCode":"","mageGroupName":"july11","mageStoreName":"","mageStoreCode":"july11","mageProjectId":"","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"","mageProjectType":"","secondaryProjectType":"","secondaryProjectName":"","companyId":"0","userName":"<EMAIL>","schedulerId":"cd20250711042856166411","sourceFile":"july11-july11-St-INITIAL-3PA71024-20250711042856.zip"}}
Fri Jul 11 2025 04:29:48 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july11-july11-St-INITIAL-3PA71024-20250711042856.zip
Fri Jul 11 2025 04:29:48 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 04:29:48 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='CDK3PA'
source "$DU_ETL_HOME/DU-DMS/DMS-CDK3PA/CDK3PA.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_CDK3PA}"
PROMPT_FOR_FILE='true'

#ETL  DMS: CDK3PA
#Base DMS: CDK3PA (optional)

DMS="CDK3PA"
DATE_PART='202507'

GROUP_CODE=""
GROUPNAME="july11"
STORENAME=""
STORE_PART="july11"
MFG="GM"
STATE='St'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="cd20250711042856166411"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="july11-july11-St-INITIAL-3PA71024-20250711042856.zip"
Fri Jul 11 2025 04:29:49 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july11-july11-St-INITIAL-3PA71024-20250711042856.zip
Fri Jul 11 2025 04:29:49 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 04:29:49 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-cdk3pa-extractor-work/scheduler-temp/cdk-zip-eti/july11-july11-St-INITIAL-3PA71024-20250711042856.zip
Fri Jul 11 2025 04:29:49 GMT+0000 : Inside send mail method
Fri Jul 11 2025 04:29:49 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 04:38:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:38:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:48:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:48:02 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 04:58:03 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 04:58:03 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 05:08:03 GMT+0000 : Application crashed!!!!!!!!!!!!!!!Error: incorrect header check
Fri Jul 11 2025 05:08:03 GMT+0000 : Application crashed!!!!!!!!!!!!!!!{"errno":-3,"code":"Z_DATA_ERROR"}
Fri Jul 11 2025 05:09:52 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:09:52.000Z","jobData":{"groupName":"QA1023","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"01","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QA1023","mageStoreCode":"QAS10232","stateCode":"IN","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QAS1023 [***************,01,01]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QA10231","mageStoreName":"QAS1023","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Team","brands":"GM*"}]}}
Fri Jul 11 2025 05:09:52 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"01\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QA1023\",\"mageStoreCode\":\"QAS10232\",\"stateCode\":\"IN\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QAS1023 [***************,01,01]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QAS1023\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAP1023\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QA10231\",\"mageStoreName\":\"QAS1023\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Team\",\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:09:52 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:09:52 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"01\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QA1023\",\"mageStoreCode\":\"QAS10232\",\"stateCode\":\"IN\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QAS1023 [***************,01,01]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QAS1023\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAP1023\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QA10231\",\"mageStoreName\":\"QAS1023\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Team\",\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:09:52 GMT+0000 : projectIdList ELSE*********,
Fri Jul 11 2025 05:09:52 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:09:52 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:09:52 GMT+0000 : id=>*********
Fri Jul 11 2025 05:09:52 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:09:52 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"01\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QA1023\",\"mageStoreCode\":\"QAS10232\",\"stateCode\":\"IN\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QAS1023 [***************,01,01]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QAS1023\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAP1023\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QA10231\",\"mageStoreName\":\"QAS1023\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Team\",\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:09:52 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:09:52 GMT+0000 : id=>
Fri Jul 11 2025 05:09:52 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:09:52 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:09:54 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:09:54 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"IN","mageGroupCode":"QA10231","mageGroupName":"QA1023","mageStoreName":"QAS1023","mageStoreCode":"QAS10232","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAP1023","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711050954002601","sourceFile":"QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip"}}
Fri Jul 11 2025 05:09:54 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:09:54 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:09:54 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QA10231"
GROUPNAME="QA1023"
STORENAME="QAS1023"
STORE_PART="QAS10232"
MFG="GM"
STATE='IN'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAP1023"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711050954002601"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip"
Fri Jul 11 2025 05:09:54 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:09:54 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:09:54 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:11:32 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:11:32 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:11:51 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:11:51 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:11:51 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:11:51 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:11:51 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:11:51 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:11:51 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:11:51 GMT+0000 : filePath
Fri Jul 11 2025 05:11:59 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:11:59 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:11:59 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:11:59 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:11:59 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:11:59 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:11:59 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:11:59 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:11:59 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:11:59 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:11:59 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:11:59 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:11:59 GMT+0000 : inSchedulerId: rc20250711050954002601-*************
Fri Jul 11 2025 05:11:59 GMT+0000 : testData: 
Fri Jul 11 2025 05:11:59 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:11:59 GMT+0000 : processFileName: PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:11:59 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:11:59 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:11:59 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:11:59 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:11:59 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:11:59 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:11:59 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:11:59 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:11:59 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:12:04 GMT+0000 : inSchedulerId=rc20250711050954002601-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}importFileExisttrue
Fri Jul 11 2025 05:12:04 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:12:04 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:12:04 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:04 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:12:04 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:12:04 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:12:04 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:04 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:04 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:12:04 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:12:04 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:12:04 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:12:04 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:04 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:04 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:12:04 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:12:04 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:12:04 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:12:04 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:12:04 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:04 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:12:04 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:12:04 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:12:06 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:12:06 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:12:06 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:12:06 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:06 GMT+0000 : exceptionTypeCounts4%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:12:06 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:12:06 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:12:06 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:12:06 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:12:06 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:12:06 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:12:06 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:12:06 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:12:06 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:12:06 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:12:06 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:12:06 GMT+0000 : paytypeHalt0 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:12:06 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: Filename : QAS10232
Fri Jul 11 2025 05:12:06 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAS10232":{"PROXY":false}}
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:12:06 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":0,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:12:06 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:12:06 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:12:06 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:12:06 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:12:51 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:12:51 GMT+0000 : Share point file upload retry attempt : 0, DMS : REYNOLDSRCI, file name:PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"secretKey\":\"bca5d14176897abccc0b0d5813026b20a00ca42a9cfdc7cc3016eaecf714ab0c78c7251c27d8\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QAS10232\",\"groupCode\":\"QA1023\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711050954002601-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:12:51 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1507}}}}
Fri Jul 11 2025 05:12:51 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:12:51 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:12:51 GMT+0000 : Failed to upload file to SharePoint {PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:12:51 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:12:51 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:12:51 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:12:51 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:12:51 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:12:51 GMT+0000 : filePath
Fri Jul 11 2025 05:12:58 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:12:58 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:12:58 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:12:58 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:12:58 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:12:58 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:12:58 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:12:58 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:12:58 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:12:58 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:12:58 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:12:58 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:12:58 GMT+0000 : inSchedulerId: rc20250711050954002601-*************
Fri Jul 11 2025 05:12:58 GMT+0000 : testData: 
Fri Jul 11 2025 05:12:58 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:12:58 GMT+0000 : processFileName: PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:12:58 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:12:58 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:12:58 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:12:58 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:12:58 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:12:58 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:12:58 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:12:58 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:12:58 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:13:03 GMT+0000 : inSchedulerId=rc20250711050954002601-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:13:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:13:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:13:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:13:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:13:03 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:13:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:13:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:13:03 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:13:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:13:03 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:13:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:13:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:13:03 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:13:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:13:03 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:13:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:13:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:13:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:13:03 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:13:03 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:13:03 GMT+0000 : paytypeHalt0 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:13:03 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: Filename : QAS10232
Fri Jul 11 2025 05:13:03 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAS10232":{"PROXY":false}}
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:13:03 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":0,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:13:03 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:13:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:13:03 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:13:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:13:51 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:13:51 GMT+0000 : Share point file upload retry attempt : 1, DMS : REYNOLDSRCI, file name:PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"secretKey\":\"2ff6dbd7fee2f09af322f899c816945872a706b59444f441352d384655fca890ab04ff71ee92\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QAS10232\",\"groupCode\":\"QA1023\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711050954002601-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:13:51 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1507}}}}
Fri Jul 11 2025 05:13:51 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:13:51 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:13:51 GMT+0000 : Failed to upload file to SharePoint {PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:13:51 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:13:51 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:13:51 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:13:51 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:13:51 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:13:51 GMT+0000 : filePath
Fri Jul 11 2025 05:13:58 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:13:58 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:13:58 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:13:58 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:13:58 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:13:58 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:13:58 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:13:58 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:13:58 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:13:58 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:13:58 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:13:58 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:13:58 GMT+0000 : inSchedulerId: rc20250711050954002601-*************
Fri Jul 11 2025 05:13:58 GMT+0000 : testData: 
Fri Jul 11 2025 05:13:58 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:13:58 GMT+0000 : processFileName: PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:13:58 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:13:58 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:13:58 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:13:58 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:13:58 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:13:58 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:13:58 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:13:58 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:13:58 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:14:03 GMT+0000 : inSchedulerId=rc20250711050954002601-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:14:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:14:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:14:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:14:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:14:03 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:14:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:14:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:14:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:14:03 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:14:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:14:03 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:14:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:14:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:14:03 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:14:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:14:03 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:14:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:14:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:14:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:14:03 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:14:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:14:03 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:14:03 GMT+0000 : paytypeHalt0 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:14:03 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: Filename : QAS10232
Fri Jul 11 2025 05:14:03 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAS10232":{"PROXY":false}}
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:14:03 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":0,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:14:03 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:14:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:14:03 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:14:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:04 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:14:51 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:14:51 GMT+0000 : Share point file upload retry attempt : 2, DMS : REYNOLDSRCI, file name:PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"secretKey\":\"aef08d4e7b39e4d98072bdada313918b1909ba8e3476c7b3d4dabf6f44c6abb29e1aedbd35e7\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QAS10232\",\"groupCode\":\"QA1023\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711050954002601-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:14:51 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1507}}}}
Fri Jul 11 2025 05:14:51 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:14:51 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:14:51 GMT+0000 : Failed to upload file to SharePoint {PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:14:51 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:14:51 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:14:51 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:14:51 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:14:51 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:14:51 GMT+0000 : filePath
Fri Jul 11 2025 05:14:58 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:14:58 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:14:58 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:14:58 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:14:58 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:14:58 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:14:58 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:14:58 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:14:58 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:14:58 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:14:58 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:14:58 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:14:58 GMT+0000 : inSchedulerId: rc20250711050954002601-*************
Fri Jul 11 2025 05:14:58 GMT+0000 : testData: 
Fri Jul 11 2025 05:14:58 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:14:58 GMT+0000 : processFileName: PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:14:58 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:14:58 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:14:58 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:14:58 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:14:58 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:14:58 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:14:58 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:14:58 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:14:58 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:15:01.000Z","jobData":{"groupName":"QAREY52","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":"","etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [***************,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Sales Team","brands":"GM*GM*"}]}}
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:15:01 GMT+0000 : projectIdList ELSE*********,*********,
Fri Jul 11 2025 05:15:01 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:15:01 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:15:01 GMT+0000 : id=>*********
Fri Jul 11 2025 05:15:01 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:15:01 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:15:01 GMT+0000 : id=>*********
Fri Jul 11 2025 05:15:01 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:15:01 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:15:01 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:15:01 GMT+0000 : id=>
Fri Jul 11 2025 05:15:01 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:15:01 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:15:01 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:15:01 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:15:03 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY522","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711051503002053","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip"}}
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711051503002053"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip"
Fri Jul 11 2025 05:15:03 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:15:03 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY52Du","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711051503002053","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip"}}
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711051503002053"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip"
Fri Jul 11 2025 05:15:03 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:15:03 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : inSchedulerId=rc20250711050954002601-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711050954002601-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":false,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:15:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:15:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:15:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:15:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:15:03 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:15:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:15:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:15:03 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:15:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:15:03 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:15:03 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:15:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711050954002601-*************
Fri Jul 11 2025 05:15:03 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:15:03 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711050954002601-*************"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:15:03 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:15:03 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:15:03 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:15:03 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:15:03 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:15:03 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:15:03 GMT+0000 : paytypeHalt0 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:15:03 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: Filename : QAS10232
Fri Jul 11 2025 05:15:03 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAS10232":{"PROXY":false}}
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:15:03 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":0,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:15:03 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:15:03 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:15:03 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:15:51 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:15:51 GMT+0000 : Share point file upload retry attempt : 3, DMS : REYNOLDSRCI, file name:PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip\",\"secretKey\":\"0ad2ee7b88ba86d0c0341efd2f770ee5bdc33acd20b4400ced6e3de48d8ee30eee2575c75e89\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QAS10232\",\"groupCode\":\"QA1023\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QAS1023\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAP1023\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711050954002601-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:15:51 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1507}}}}
Fri Jul 11 2025 05:15:51 GMT+0000 : File uploaded failed to SharePoint {PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:15:51 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:15:51 GMT+0000 : paytypeHalt0 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:15:51 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:15:51 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: Filename : QAS10232
Fri Jul 11 2025 05:15:51 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QAS10232":{"PROXY":false}}
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip
Fri Jul 11 2025 05:15:51 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Failed","proxy_file_name":"PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954.zip","proxy_url":"","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QA1023-QAS10232-IN-WEBHOOK-***************-_01_01_20250711050954-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":0,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:15:51 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:16:22 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:15:03.002Z\",\"uniqueId\":\"rc20250711051503002053\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip\",\"endTime\":\"2025-07-11T05:15:03.080Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:16:22 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":\"\",\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:15:03.002Z\",\"uniqueId\":\"rc20250711051503002053\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051503.zip\",\"endTime\":\"2025-07-11T05:15:03.080Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:16:22 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:16:22 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:16:22 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:16:22 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:16:24 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:16:24 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:17:51 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:17:51.000Z","jobData":{"groupName":"QAREY52","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY522 [***************,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*"}]}}
Fri Jul 11 2025 05:17:51 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:17:51 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:17:51 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:17:51 GMT+0000 : projectIdList ELSE*********,
Fri Jul 11 2025 05:17:51 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:17:51 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:17:51 GMT+0000 : id=>*********
Fri Jul 11 2025 05:17:51 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:17:51 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:17:51 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:17:51 GMT+0000 : id=>
Fri Jul 11 2025 05:17:51 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:17:51 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:17:53 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:17:53 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY522","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711051753002067","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip"}}
Fri Jul 11 2025 05:17:53 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:17:53 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:17:53 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711051753002067"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip"
Fri Jul 11 2025 05:17:53 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:17:53 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:17:53 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:19:40 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:19:40 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:19:59 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:19:59 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:19:59 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:19:59 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:19:59 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:19:59 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:19:59 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:19:59 GMT+0000 : filePath
Fri Jul 11 2025 05:20:05 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:20:05 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:20:05 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:20:05 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:20:05 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:20:05 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:20:05 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:20:05 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:20:05 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:20:05 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:20:05 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:20:05 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:20:05 GMT+0000 : inSchedulerId: rc20250711051753002067-*************
Fri Jul 11 2025 05:20:05 GMT+0000 : testData: 
Fri Jul 11 2025 05:20:05 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:20:05 GMT+0000 : processFileName: PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:20:05 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:20:05 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:20:05 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:20:05 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:20:05 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:20:05 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:20:05 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:20:05 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:20:05 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:20:10 GMT+0000 : inSchedulerId=rc20250711051753002067-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExisttrue
Fri Jul 11 2025 05:20:10 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:20:10 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:20:10 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:10 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:20:10 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:20:10 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:20:10 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:10 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:10 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:20:10 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:20:10 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:20:10 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:20:10 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:10 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:10 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:20:10 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:20:10 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:20:10 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:20:10 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:20:10 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:10 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:20:10 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:20:10 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:20:12 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:20:12 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:20:12 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:20:12 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:12 GMT+0000 : exceptionTypeCounts4%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:20:12 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:20:12 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:20:12 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:20:12 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:20:12 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:20:12 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:20:12 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:20:12 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:20:12 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:20:12 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:20:12 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:20:12 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:20:12 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY523
Fri Jul 11 2025 05:20:12 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY523":{"PROXY":false}}
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:20:12 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:20:12 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:20:12 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:20:12 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:20:12 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:20:59 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:20:59 GMT+0000 : Share point file upload retry attempt : 0, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"secretKey\":\"065b4c03cb8c1651d55efbc4e16ad0559cc543e9ce0840b368a103cf745f0dd7aa40a865f628\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY523\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711051753002067-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:20:59 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1517}}}}
Fri Jul 11 2025 05:20:59 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:20:59 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:20:59 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:20:59 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:20:59 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:20:59 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:20:59 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:20:59 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:20:59 GMT+0000 : filePath
Fri Jul 11 2025 05:21:06 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:21:06 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:21:06 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:21:06 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:21:06 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:21:06 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:21:06 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:21:06 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:21:06 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:21:06 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:21:06 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:21:06 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:21:06 GMT+0000 : inSchedulerId: rc20250711051753002067-*************
Fri Jul 11 2025 05:21:06 GMT+0000 : testData: 
Fri Jul 11 2025 05:21:06 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:21:06 GMT+0000 : processFileName: PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:21:06 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:21:06 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:21:06 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:21:06 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:21:06 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:21:06 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:21:06 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:21:06 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:21:06 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:21:11 GMT+0000 : inSchedulerId=rc20250711051753002067-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:21:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:21:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:21:11 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:21:11 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:21:11 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:21:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:21:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:21:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:21:11 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:21:11 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:21:11 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:21:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:21:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:21:11 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:21:11 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:21:11 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:21:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:21:11 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:21:11 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:21:11 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:21:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:21:11 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:21:11 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:21:11 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY523
Fri Jul 11 2025 05:21:11 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY523":{"PROXY":false}}
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:21:11 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:21:11 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:21:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:21:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:21:11 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:21:31 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:21:59 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:21:59 GMT+0000 : Share point file upload retry attempt : 1, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"secretKey\":\"2a43d312b19c8d50c296aea467d9e66dbf16f8ab770fe4388bf9dc06525e4544a747f8aa5fcc\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY523\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711051753002067-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:21:59 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1517}}}}
Fri Jul 11 2025 05:21:59 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:21:59 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:21:59 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:21:59 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:21:59 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:21:59 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:21:59 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:21:59 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:21:59 GMT+0000 : filePath
Fri Jul 11 2025 05:22:06 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:22:06 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:22:06 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:22:06 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:22:06 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:22:06 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:22:06 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:22:06 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:22:06 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:22:06 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:22:06 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:22:06 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:22:06 GMT+0000 : inSchedulerId: rc20250711051753002067-*************
Fri Jul 11 2025 05:22:06 GMT+0000 : testData: 
Fri Jul 11 2025 05:22:06 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:22:06 GMT+0000 : processFileName: PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:22:06 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:22:06 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:22:06 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:22:06 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:22:06 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:22:06 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:22:06 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:22:06 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:22:06 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:22:11 GMT+0000 : inSchedulerId=rc20250711051753002067-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:22:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:22:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:22:11 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:22:11 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:22:11 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:22:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:22:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:22:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:22:11 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:22:11 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:22:11 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:22:11 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:22:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:22:11 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:22:11 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:22:11 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:22:11 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:22:11 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:22:11 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:22:11 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:22:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:22:11 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:22:11 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:22:11 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY523
Fri Jul 11 2025 05:22:11 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY523":{"PROXY":false}}
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:22:11 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:22:11 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:22:11 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:22:11 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:22:11 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:31 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:22:32.000Z","jobData":{"groupName":"QAREY52","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [***************,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*"}]}}
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:22:32 GMT+0000 : projectIdList ELSE*********,*********,
Fri Jul 11 2025 05:22:32 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:22:32 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:22:32 GMT+0000 : id=>*********
Fri Jul 11 2025 05:22:32 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:22:32 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:22:32 GMT+0000 : id=>*********
Fri Jul 11 2025 05:22:32 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:22:32 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:22:32 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:22:32 GMT+0000 : id=>
Fri Jul 11 2025 05:22:32 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:22:32 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:22:32 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:22:32 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:22:34 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY522","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711052234003879","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip"}}
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711052234003879"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip"
Fri Jul 11 2025 05:22:34 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:22:34 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY52Du","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711052234003879","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip"}}
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:22:34 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711052234003879"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip"
Fri Jul 11 2025 05:22:34 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:34 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:22:34 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip
Fri Jul 11 2025 05:22:59 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:22:59 GMT+0000 : Share point file upload retry attempt : 2, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"secretKey\":\"8deb62fdaf8d0c55486b31cf5cd6b41c8a9e95a5638b310c51b348b7cdaf3a8d8089af5c019a\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY523\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711051753002067-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:22:59 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1517}}}}
Fri Jul 11 2025 05:22:59 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:22:59 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:22:59 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:22:59 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:22:59 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:22:59 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:22:59 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:22:59 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:22:59 GMT+0000 : filePath
Fri Jul 11 2025 05:23:04 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:23:04 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:23:04 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:23:04 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:23:04 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:23:04 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:23:04 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:23:04 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:23:04 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:23:04 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:23:04 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:23:04 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:23:04 GMT+0000 : inSchedulerId: rc20250711051753002067-*************
Fri Jul 11 2025 05:23:04 GMT+0000 : testData: 
Fri Jul 11 2025 05:23:04 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:23:04 GMT+0000 : processFileName: PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:23:04 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:23:04 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:23:04 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:23:04 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:23:04 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:23:04 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:23:04 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:23:04 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:23:04 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:23:09 GMT+0000 : inSchedulerId=rc20250711051753002067-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711051753002067-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:23:09 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:23:09 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:23:09 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:23:09 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:23:09 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:23:09 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:23:09 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:23:09 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:23:09 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:23:09 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:23:09 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:23:09 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:23:09 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711051753002067-*************
Fri Jul 11 2025 05:23:09 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:23:09 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711051753002067-*************"}
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:23:09 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:23:09 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:23:09 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:23:09 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:23:09 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:23:09 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:23:09 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:23:09 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:23:09 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY523
Fri Jul 11 2025 05:23:09 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY523":{"PROXY":false}}
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:23:09 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:23:09 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:23:09 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:23:09 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:09 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:23:29 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:22:34.003Z\",\"uniqueId\":\"rc20250711052234003879\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip\",\"endTime\":\"2025-07-11T05:22:34.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:23:29 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:22:34.003Z\",\"uniqueId\":\"rc20250711052234003879\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711052234.zip\",\"endTime\":\"2025-07-11T05:22:34.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:23:29 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:23:29 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:23:29 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:23:29 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:23:30 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:23:31 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:23:31 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:23:59 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:23:59 GMT+0000 : Share point file upload retry attempt : 3, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip\",\"secretKey\":\"6a1db184ae90d81ff6fbdd78dbc7b8d552c718d6f1d7fb64301bc6e2ea480c7e3b777b08f95b\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY523\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711051753002067-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:23:59 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1517}}}}
Fri Jul 11 2025 05:23:59 GMT+0000 : File uploaded failed to SharePoint {PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:23:59 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:23:59 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:23:59 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:23:59 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY523
Fri Jul 11 2025 05:23:59 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY523":{"PROXY":false}}
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip
Fri Jul 11 2025 05:23:59 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Failed","proxy_file_name":"PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753.zip","proxy_url":"","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711051753-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:23:59 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:24:49 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:24:48.000Z","jobData":{"groupName":"QAREY52","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"04","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":true,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY521","stateCode":"OH","projectIds":"**********","secondProjectIdList":"","companyIds":"**********","parentName":"QASREY52Du [***************,null,04]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY52","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*"}]}}
Fri Jul 11 2025 05:24:49 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"04\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY521\",\"stateCode\":\"OH\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY52Du [***************,null,04]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY52\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:24:49 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:24:49 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"**********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"04\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY521\",\"stateCode\":\"OH\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY52Du [***************,null,04]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY52\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:24:49 GMT+0000 : projectIdList ELSE*********,
Fri Jul 11 2025 05:24:49 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:24:49 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:24:49 GMT+0000 : id=>*********
Fri Jul 11 2025 05:24:49 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:24:49 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"04\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":true,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY521\",\"stateCode\":\"OH\",\"projectIds\":\"**********\",\"secondProjectIdList\":\"\",\"companyIds\":\"**********\",\"parentName\":\"QASREY52Du [***************,null,04]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY52\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:24:49 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:24:49 GMT+0000 : id=>
Fri Jul 11 2025 05:24:49 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:24:49 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:24:50 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:24:50 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"OH","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY52","mageStoreCode":"QASREY521","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY52Du","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711052450003761","sourceFile":"QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip"}}
Fri Jul 11 2025 05:24:50 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:24:50 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:24:50 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY52"
STORE_PART="QASREY521"
MFG="GM"
STATE='OH'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711052450003761"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip"
Fri Jul 11 2025 05:24:50 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:24:50 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:24:50 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:26:47 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:26:48 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:27:07 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:27:07 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:27:07 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:27:07 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:27:07 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:27:07 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:27:07 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:27:07 GMT+0000 : filePath
Fri Jul 11 2025 05:27:15 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:27:15 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:27:15 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:27:15 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:27:15 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:27:15 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:27:15 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:27:15 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:27:15 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:27:15 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:27:15 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:27:15 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:27:15 GMT+0000 : inSchedulerId: rc20250711052450003761-*************
Fri Jul 11 2025 05:27:15 GMT+0000 : testData: 
Fri Jul 11 2025 05:27:15 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:27:15 GMT+0000 : processFileName: PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:27:15 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:27:15 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:27:15 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:27:15 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:27:15 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:27:15 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:27:15 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:27:15 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:27:15 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:27:20 GMT+0000 : inSchedulerId=rc20250711052450003761-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExisttrue
Fri Jul 11 2025 05:27:20 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:27:20 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:27:20 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:20 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:27:20 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:27:20 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:27:20 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:27:20 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:27:20 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:27:20 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:27:20 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:27:20 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:27:20 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:20 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:20 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:27:20 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:27:20 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:27:20 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:27:20 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:27:20 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:20 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:27:20 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:27:22 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:27:22 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:27:22 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:27:22 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:22 GMT+0000 : exceptionTypeCounts4%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:27:22 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:27:22 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadActionComplete(exceptions) ; 
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:27:22 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:27:22 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":\"\"}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:27:22 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:27:22 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:27:22 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:27:22 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:27:22 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:27:22 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:27:22 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:27:22 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:27:22 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY521
Fri Jul 11 2025 05:27:22 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY521":{"PROXY":false}}
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:27:22 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:27:22 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:27:22 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:27:22 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:27:40 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:07 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:28:07 GMT+0000 : Share point file upload retry attempt : 0, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"secretKey\":\"a8f9f9c14c557291b2a887f30fa48d3dbd208150653d1d67d3059f855b110755dafeb82e82d6\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY521\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711052450003761-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:28:07 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1519}}}}
Fri Jul 11 2025 05:28:07 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:28:07 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:28:07 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:28:07 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:28:07 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:28:07 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:28:07 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:28:07 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:28:07 GMT+0000 : filePath
Fri Jul 11 2025 05:28:14 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:28:14 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:28:14 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:28:14 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:28:14 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:28:14 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:28:14 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:28:14 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:28:14 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:28:14 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:28:14 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:28:14 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:28:14 GMT+0000 : inSchedulerId: rc20250711052450003761-*************
Fri Jul 11 2025 05:28:14 GMT+0000 : testData: 
Fri Jul 11 2025 05:28:14 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:28:14 GMT+0000 : processFileName: PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:28:14 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:28:14 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:28:14 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:28:14 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:28:14 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:28:14 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:28:14 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:28:14 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:28:14 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:28:19 GMT+0000 : inSchedulerId=rc20250711052450003761-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:28:19 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:28:19 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:28:19 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:28:19 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:28:19 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:28:19 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:28:19 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:28:19 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:28:19 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:28:19 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:28:19 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:28:19 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:28:19 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:28:19 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:28:19 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:28:19 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:28:19 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:28:19 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:28:19 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:28:19 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:28:19 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:28:19 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:28:19 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:28:19 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY521
Fri Jul 11 2025 05:28:19 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY521":{"PROXY":false}}
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:28:19 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:28:19 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:28:19 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:28:19 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:28:19 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:28:39 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:07 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:29:07 GMT+0000 : Share point file upload retry attempt : 1, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"secretKey\":\"7df33611f09dba55617d6d61ce3e967aa8beff0a132f1436dd72d4ba3d9408c2e1d5aa84695a\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY521\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711052450003761-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:29:07 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1519}}}}
Fri Jul 11 2025 05:29:07 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:29:07 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:29:07 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:29:07 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:29:07 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:29:07 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:29:07 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:29:07 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:29:07 GMT+0000 : filePath
Fri Jul 11 2025 05:29:12 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:29:12 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:29:12 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:29:12 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:29:12 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:29:12 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:29:12 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:29:12 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:29:12 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:29:12 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:29:12 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:29:12 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:29:12 GMT+0000 : inSchedulerId: rc20250711052450003761-*************
Fri Jul 11 2025 05:29:12 GMT+0000 : testData: 
Fri Jul 11 2025 05:29:12 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:29:12 GMT+0000 : processFileName: PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:29:12 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:29:12 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:29:12 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:29:12 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:29:12 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:29:12 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:29:12 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:29:12 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:29:12 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:29:17 GMT+0000 : inSchedulerId=rc20250711052450003761-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:29:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:29:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:29:17 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:29:17 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:29:17 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:29:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:29:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:29:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:29:17 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:29:17 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:29:17 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:29:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:29:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:29:17 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:29:17 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:29:17 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:29:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:29:17 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:29:17 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:29:17 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:29:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:29:17 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:29:17 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:29:17 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY521
Fri Jul 11 2025 05:29:17 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY521":{"PROXY":false}}
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:29:17 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:29:17 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:29:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:29:17 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:29:17 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:29:37 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:07 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:30:07 GMT+0000 : Share point file upload retry attempt : 2, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"secretKey\":\"ff9943794c6e20ce843b4684e7cca88b0b7d7f322250c622193ffb04156cf1188c764ce3ad8a\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY521\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711052450003761-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:30:07 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1519}}}}
Fri Jul 11 2025 05:30:07 GMT+0000 : updateSolve360Data [object Object]
Fri Jul 11 2025 05:30:07 GMT+0000 : Started SharePoint file upload functionality 
Fri Jul 11 2025 05:30:07 GMT+0000 : Failed to upload file to SharePoint {PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip} StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:30:07 GMT+0000 : req.body>>>>>>>>>>>>>?>>>>>>>>>>>>>>>>>>>>>>>>>>[object Object]
Fri Jul 11 2025 05:30:07 GMT+0000 : Dist file exist for sharepoint upload
Fri Jul 11 2025 05:30:07 GMT+0000 : decryptedKeyValue:Azure007SkeySharePoint and share point public secret key: Azure007SkeySharePoint
Fri Jul 11 2025 05:30:07 GMT+0000 : decryptedKeyValue and share point public secret key are equal
Fri Jul 11 2025 05:30:07 GMT+0000 : sharedFolderPath
Fri Jul 11 2025 05:30:07 GMT+0000 : filePath
Fri Jul 11 2025 05:30:12 GMT+0000 : Upload result is: true
Fri Jul 11 2025 05:30:12 GMT+0000 : Sharepoint upload completed
Fri Jul 11 2025 05:30:12 GMT+0000 : projectId: *********
Fri Jul 11 2025 05:30:12 GMT+0000 : secondProjectId: 
Fri Jul 11 2025 05:30:12 GMT+0000 : userName: <EMAIL>
Fri Jul 11 2025 05:30:12 GMT+0000 : isRerun: null
Fri Jul 11 2025 05:30:12 GMT+0000 : UPDATE_SOLVE360: false
Fri Jul 11 2025 05:30:12 GMT+0000 : solve360Update: [object Object]
Fri Jul 11 2025 05:30:12 GMT+0000 : projectIds: *********,
Fri Jul 11 2025 05:30:12 GMT+0000 : secondProjectIdList: 
Fri Jul 11 2025 05:30:12 GMT+0000 : dmsType: REYNOLDSRCI
Fri Jul 11 2025 05:30:12 GMT+0000 : exceptions: 
Fri Jul 11 2025 05:30:12 GMT+0000 : inSchedulerId: rc20250711052450003761-*************
Fri Jul 11 2025 05:30:12 GMT+0000 : testData: 
Fri Jul 11 2025 05:30:12 GMT+0000 : companyObj: [object Object]
Fri Jul 11 2025 05:30:12 GMT+0000 : processFileName: PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:30:12 GMT+0000 : totalRoCount:     29

Fri Jul 11 2025 05:30:12 GMT+0000 : exceptionTypeCounts: [object Object]
Fri Jul 11 2025 05:30:12 GMT+0000 : 'Import Halt status response!!!{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:30:12 GMT+0000 : ImportHaltStatus:{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}
Fri Jul 11 2025 05:30:12 GMT+0000 : ✅ Parsed GraphQL data: [{"exceptionKey":"Accounting_coa_exception_report","exceptionName":"COA Data is Missing","exceptionDescription":"The API returns an error that the COA (Chart of Accounts) is not available. ","isRelevant":true},{"exceptionKey":"company_no_not_matching_count","exceptionName":"Company Number Not matching Count","exceptionDescription":" As a precaution, we are ensuring that we have retrieved data from the correct company. ","isRelevant":true},{"exceptionKey":"corecharge_with_nosale","exceptionName":"Core Charge with no Sale","exceptionDescription":"List of ROs that have a core charge with no sale amount.","isRelevant":true},{"exceptionKey":"corecharge_without_corereturn","exceptionName":"Core Charge without Core Return","exceptionDescription":"List of ROs that have a core charge, with no core return.","isRelevant":true},{"exceptionKey":"corereturn_without_corecharge","exceptionName":"Core Return without Core Charge","exceptionDescription":"List of ROs that have a core return, but no core charge.","isRelevant":true},{"exceptionKey":"coupon_and_discount_exception","exceptionName":"CouponDiscountBasis Amount Mismatch Exception","exceptionDescription":"The CouponDiscountBasis expects two-line items, where one is the exact opposite of the other (One with a positive amount and the other with a negative amount so it balances out the calculation). ","isRelevant":true},{"exceptionKey":"coupon_discount_basis_amount_mismatch_exception_filepath","exceptionName":"Coupons/discounts Exception Count","exceptionDescription":"Coupon numbers present in the RO raw data are not found in the Coupon and Discount API. ","isRelevant":true},{"exceptionKey":"customer_ro_exception","exceptionName":"Customer Name is Missing Count","exceptionDescription":"List of ROs that are missing the Customer Name in the Header. We occasionally encounter cases where some ROs don't have customer details, or the customer details API fails intermittently.","isRelevant":true},{"exceptionKey":"Exception-closed-invoices","exceptionName":"ROs closed on the client-provided invoice master, but are open/missing in the RCI raw data","exceptionDescription":"List of ROs that are closed on the client-provided invoice master, but open or missing in the RCI raw data.","isRelevant":true},{"exceptionKey":"exception_discount_gl_mismatch","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"extraros_in_xml","exceptionName":"Extra ROs present in the RCI raw data","exceptionDescription":"List of ROs that are not present on the client-provided invoice master, but are present in the RCI raw data.","isRelevant":true},{"exceptionKey":"failed_jobs","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"gl_missing_ro_list","exceptionName":"GL Missing Total RO count","exceptionDescription":"List of ROs with missing GL data.","isRelevant":false},{"exceptionKey":"gl_missing_ros","exceptionName":"ROs that are Missing GL Data","exceptionDescription":"List of ROs where GL entries are missing or contain errors.","isRelevant":true},{"exceptionKey":"grouped-team-work","exceptionName":"Grouped Team Work Count ","exceptionDescription":"We have rarely seen instances where some of the labor entries are grouped in the work. In such situations, we have both a grouped labor line item and individual line items, creating a potential for doubling the labor amount.","isRelevant":true},{"exceptionKey":"im_opened_closed_rci_ros","exceptionName":"ROs open on the client-provided invoice master, but closed in the RCI data","exceptionDescription":"List of ROs that are closed in the RCI raw data but are open or missing on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"labor_with_no_paytype","exceptionName":"Labor with no paytype","exceptionDescription":"Labor with a Null paytype. ","isRelevant":true},{"exceptionKey":"labor_with_zero_sale_nonzero_cost","exceptionName":"Labor with Zero Sale, Nonzero Cost","exceptionDescription":"If Labor with Zero Sale and Nonzero Cost appears in the raw data, we require the actual RO to validate the correctness of the proxy. This halt is temporary, and we can resume once we have the actual RO for confirmation.","isRelevant":true},{"exceptionKey":"less_special_discount_data","exceptionName":"Less Special Discount Count","exceptionDescription":"RO with Less Special Discounts. Parts and Labor discounts gets in labor line","isRelevant":true},{"exceptionKey":"list-price-zero","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"lost_sale_parts","exceptionName":"Lost Parts Sale","exceptionDescription":"Quoted Parts that comes under Lost Sale category. This parts does not prints in actual RO. We need to verify this","isRelevant":true},{"exceptionKey":"Misc.-exception","exceptionName":"Misc. exception mismatch","exceptionDescription":"Mismatch with the MISC. amount in line items and in the total section","isRelevant":true},{"exceptionKey":"missing-invoices","exceptionName":"Invoice Missing Count","exceptionDescription":"List of missing ROs within the RCI raw data, but are present on the client-provided invoice master.","isRelevant":true},{"exceptionKey":"multi-labor-exception","exceptionName":"Multiple Labor in Job","exceptionDescription":"Multiple labor detected in the job. Need to confirm the RO with actual RO","isRelevant":true},{"exceptionKey":"negative_coupon","exceptionName":"Negative Coupon Count","exceptionDescription":"List of ROs where the Coupon (Q) is a negative amount. This is a temporary halt and will be removed once we get enough actual RO to clarify our doubts.","isRelevant":true},{"exceptionKey":"New-line-type","exceptionName":"Total Count of a new 'Line Type' Detected","exceptionDescription":"DealerTrack is giving each item like MISC, SUBLET, GOG etc. as labor items and we differentiate it with the element â€˜lineTypeâ€™. We have already recognized some and some are still not invented. So, we have put a halt if any unknown type comes.","isRelevant":true},{"exceptionKey":"part_description_exception","exceptionName":"Part Description Array Length > 2 ","exceptionDescription":"Parts having multiple descriptions as array ","isRelevant":true},{"exceptionKey":"part_details_null","exceptionName":"Part Details Missing","exceptionDescription":"Parts that do not have a part number or description.","isRelevant":true},{"exceptionKey":"parts_excluded_from_history","exceptionName":"Parts Excluded From History Exception Count","exceptionDescription":"Obtaining a label/key 'PartsExcludedFromHistory' appears to be included in recent stores. This report contains a list of ROs with the value 'X' in the 'PartsExcludedFromHistory'. We can resume this, but we need the actual RO(s) to confirm.","isRelevant":true},{"exceptionKey":"punch_time_missing","exceptionName":"% of ROs with techincian punch time missing ","exceptionDescription":"The percentage of ROs with no tech punch time present.","isRelevant":true},{"exceptionKey":"ro_accounting_desc_exception","exceptionName":"Customer Name showing as \"acc description\"","exceptionDescription":"There is no matching account number in the COA. This occurs occasionally or in cases of the COA being missing. ","isRelevant":true},{"exceptionKey":"sale_zero_cost_nonzero_parts","exceptionName":"Parts Sale is Zero or Cost Non-Zero Parts","exceptionDescription":"Parts with zero sale, non-zero cost ","isRelevant":true},{"exceptionKey":"split_job_exception","exceptionName":"Split Job Exception Count","exceptionDescription":"Labor with split jobs","isRelevant":true},{"exceptionKey":"Suffixed-invoices","exceptionName":"Suffixed Invoices Count","exceptionDescription":"List of ROs that have a suffix (i.e., 'SX') on the RO number.","isRelevant":true}]
Fri Jul 11 2025 05:30:12 GMT+0000 : ✅ Input exceptionTypeCounts: {}
Fri Jul 11 2025 05:30:12 GMT+0000 : ✅ Transformed Object: {}
Fri Jul 11 2025 05:30:12 GMT+0000 : ✅ isAnyRelevant: false
Fri Jul 11 2025 05:30:12 GMT+0000 : ✅ exceptionTypeCounts: {}
Fri Jul 11 2025 05:30:17 GMT+0000 : inSchedulerId=rc20250711052450003761-*************projectIds*********,secondProjectIdListimportHaltStatus{"data":{"getSchedulerPreImportStatusBySchedulerId":"[{\"scheduler_id\":\"rc20250711052450003761-*************\",\"is_make_halt\":false,\"is_dept_halt\":false,\"is_paytype_halt\":true,\"inv_seq_halt\":true}]"}}importFileExistfalse
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:30:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:30:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:30:17 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:30:17 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:30:17 GMT+0000 : transformedObj of project ID ********* : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:30:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:30:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:30:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:30:17 GMT+0000 : importHaltStatus(inSchedulerId) ; false
Fri Jul 11 2025 05:30:17 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"complete","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:30:17 GMT+0000 : exceptionTypeCounts3%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%[object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside doPayloadActionComplete in solve360
Fri Jul 11 2025 05:30:17 GMT+0000 : ProjectID ; *********
Fri Jul 11 2025 05:30:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadActionComplete(exceptions) ; null
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadActionComplete(inSchedulerId) ; rc20250711052450003761-*************
Fri Jul 11 2025 05:30:17 GMT+0000 : importHaltStatus(inSchedulerId) ; true
Fri Jul 11 2025 05:30:17 GMT+0000 : exceptionTypeCounts(inSchedulerId) ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction complete  call - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"SchedulerImport","inAction":"halt","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"in_is_update_retrieve_ro\":false,\"exceptions\":null}","inCreatedBy":"<EMAIL>","inSchedulerId":"rc20250711052450003761-*************"}
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside updateDate in solve360
Fri Jul 11 2025 05:30:17 GMT+0000 : ProjectID ; 
Fri Jul 11 2025 05:30:17 GMT+0000 : userName ; <EMAIL>
Fri Jul 11 2025 05:30:17 GMT+0000 : solve360Update flag ; false
Fri Jul 11 2025 05:30:17 GMT+0000 : isRerun flag ; null
Fri Jul 11 2025 05:30:17 GMT+0000 : transformedObj of project ID  : {"custom9163777":"2025-07-11","custom19370039":"Scheduler"}
Fri Jul 11 2025 05:30:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : dmsType@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@REYNOLDSRCI
Fri Jul 11 2025 05:30:17 GMT+0000 : companyObj111:[object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : ImportHaltStatus OPTIONS:[object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:30:17 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:30:17 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY521
Fri Jul 11 2025 05:30:17 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY521":{"PROXY":false}}
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:30:17 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Success","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyMailSubject":"ReynoldsRCI Proxy Zip Success"}
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Success","proxy_file_name":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxy_url":"https://dealeruplift.sharepoint.com/sites/datamarketplace/Documents/production/Scheduler/REYNOLDSRCI/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:30:17 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:30:17 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:30:17 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:30:17 GMT+0000 : ImportHaltStatus:[object Object]
Fri Jul 11 2025 05:30:37 GMT+0000 : optionsSolveCall ; [object Object]
Fri Jul 11 2025 05:31:07 GMT+0000 : File uploaded failed to SharePoint {undefined},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:31:07 GMT+0000 : Share point file upload retry attempt : 3, DMS : REYNOLDSRCI, file name:PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip ,error:{"name":"StatusCodeError","statusCode":504,"message":"504 - \"<html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>nginx</center>\\r\\n</body>\\r\\n</html>\\r\\n\"","error":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","options":{"method":"POST","uri":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint","headers":{"Content-Type":"application/json"},"body":"{\"fileName\":\"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"filePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip\",\"secretKey\":\"3528497e7b3ac73d3cdff83b49e496941c58c4cedbf183981a80d9d7653efb59eb817413b33a\",\"dmsType\":\"REYNOLDSRCI\",\"isRerun\":null,\"projectId\":\"*********\",\"secondProjectId\":\"\",\"solve360Update\":false,\"userName\":\"<EMAIL>\",\"warningObj\":{\"scheduled_by\":\"<EMAIL>\",\"invalidmiscpaytypeCount\":17,\"invalidmiscpaytypeFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/misc_paytype_exception.csv\",\"estimateCount\":94,\"punchTimeMissingCount\":\"34.38\\n\",\"PUNCH_TIME_MISSING_FILEPATH\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/exception/punch_time_missing.csv\",\"invoice_missing_count\":0,\"missingInvoiceFilePath\":\"/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/bundle/temp1/processing-result/missing-invoices.csv\"},\"thirdPartyUsername\":\"***************\",\"storeCode\":\"QASREY521\",\"groupCode\":\"QAREY52\",\"projectIds\":[\"*********\",\"\"],\"companyObj\":[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}],\"secondProjectIdList\":[\"\"],\"totalRoCount\":\"    29\\n\",\"exceptionTypeCounts\":{},\"exceptions\":\"\",\"inSchedulerId\":\"rc20250711052450003761-*************\",\"testData\":\"\"}","simple":true,"resolveWithFullResponse":false,"transform2xxOnly":false},"response":{"statusCode":504,"body":"<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n","headers":{"date":"Fri, 11 Jul 2025 05:31:07 GMT","content-type":"text/html","content-length":"160","connection":"keep-alive","server":"nginx","strict-transport-security":"max-age=31536000; includeSubDomains"},"request":{"uri":{"protocol":"https:","slashes":true,"auth":null,"host":"new-devl-scheduler.sandbox.dealeruplift.net","port":443,"hostname":"new-devl-scheduler.sandbox.dealeruplift.net","hash":null,"search":null,"query":null,"pathname":"/scheduler/publicFileUploadToSharePoint","path":"/scheduler/publicFileUploadToSharePoint","href":"https://new-devl-scheduler.sandbox.dealeruplift.net/scheduler/publicFileUploadToSharePoint"},"method":"POST","headers":{"Content-Type":"application/json","content-length":1519}}}}
Fri Jul 11 2025 05:31:07 GMT+0000 : File uploaded failed to SharePoint {PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip},error:StatusCodeError: 504 - "<html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>nginx</center>\r\n</body>\r\n</html>\r\n"
Fri Jul 11 2025 05:31:07 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:31:07 GMT+0000 : paytypeHalt1 invSequenceHalt2 makeHalt0 0 
Fri Jul 11 2025 05:31:07 GMT+0000 : isPreImportExist : [object Object]
Fri Jul 11 2025 05:31:07 GMT+0000 : Inside sharepoint file upload completion mail
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: Filename : QASREY521
Fri Jul 11 2025 05:31:07 GMT+0000 : Setup notifyObj !notifyObj && !notifyObj.storeName : {"QASREY521":{"PROXY":false}}
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: notifyObj : {"PROXY":false}
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: parsedBody.fileName : PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip
Fri Jul 11 2025 05:31:07 GMT+0000 : Setup notifyObj inside condition else part: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: notifyObj after checking: {"PROXY":true,"proxyUploadStatus":"Failed","proxyFileName":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxyFilePath":"","proxyMailSubject":"ReynoldsRCI Proxy Zip Failed"}
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: Proxy status: true
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: replacement obj: {"proxy_file_upload_status":"Failed","proxy_file_name":"PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450.zip","proxy_url":"","efs_path":"/etl/etl-vagrant/etl-REYNOLDSRCI/REYNOLDSRCI-zip/PROC-QAREY52-QASREY521-OH-WEBHOOK-***************-_01_01_20250711052450-ETL.zip","isRerun":true,"warning_message":"","closed_rodetail_warning_message":"","vehicle_warning_message":"","customer_warning_message":"","gldetail_warning_message":"","coupon_and_discount_warning_message":"","ro_account_description_warning_message":"","skip_error_count":"","core_return_exception_count":"","core_charge_exception_count":"","core_return_not_equal_core_charge_exception_count":"","core_charge_with_no_sale_count":"","resume_user_message":"","dealer_not_subscribed_message":"","invalid_core_cost_sale_mismatch_count":"","invalid_misc_paytype_count":17,"punch_time_missing_count":"34.38\n","inventory_ro_count":"","invalid_core_amount_mismatch_count":"","coa_exception_warning_message":"","customer_exception_warning_message":"","misc_exception_count":"","gl_ro_not_found_count":"","invoice_missing_count":0,"suffixed_invoices_count":"","company_no_not_matching_count":"","grouped_team_work_count":"","dealerAddress":"","split_job_exception_count":"","new_line_type_count":"","chart_of_accounts_file_path":"","exception_closed_invoices_count":"","extra_ro_in_xml_exception_count":"","im_Opened_Closed_Rci_Ros_Count":"","Sale_Zero_Cost_NonZero_Parts_Count":"","gl_Deatil_Extraction_Error_Count":"","less_special_discount_count":"","negative_coupon_count":"","labor_with_zero_sale_nonzero_cost_count":"","gl_missing_ros_count":"","part_description_exception_count":"","coupon_discount_basis_amount_mismatch_exception_count":"","labor_with_no_paytype_exception_count":"","parts_excluded_from_history_exception_count":"","lost_sale_parts_exception_count":"","part_details_null_exception_count":"","multiLaborExceptionCount":"","scheduled_by":"<EMAIL>","testData":false,"paytype_halt":1,"inv_sequence_halt":2,"make_halt":0,"department_halt":0,"pre_import_halt_exist":true,"paytypeHalt_exist_flag":true,"invSequenceHalt_exist_flag":true,"warning_message_exist_flag":false,"dealer_not_subscribed_message_exist_flag":false,"closed_rodetail_warning_message_exist_flag":false,"vehicle_warning_message_exist_flag":false,"customer_warning_message_exist_flag":false,"gldetail_warning_message_exist_flag":false,"coupon_and_discount_message_exist_flag":false,"ro_account_description_message_exist_flag":false,"chart_of_accounts_file_path_exist_flag":false,"coa_exception_warning_message_exist_flag":false,"customer_exception_warning_message_exist_flag":false,"skip_error_count_message_exist_flag":false,"core_return_exception_exist_flag":false,"core_charge_exception_exist_flag":false,"core_return_not_equal_to_core_charge_exception_exist_flag":false,"core_charge_with_no_sale_exception_exist_flag":false,"gl_ro_not_found_exception_exist_flag":false,"invalid_core_cost_sale_mismatch_exist_flag":false,"invalid_misc_paytype_flag":true,"exception_closed_invoices_flag":false,"extra_ro_in_xml_exception_flag":false,"im_opened_closed_rci_ros_flag":false,"Sale_Zero_Cost_NonZero_Parts_flag":false,"gl_Deatil_Extraction_Error_Count_flag":false,"split_job_exception_flag":false,"less_special_discount_exception_flag":false,"negative_coupon_exception_flag":false,"labor_with_zero_sale_nonzero_cost_exception_flag":false,"suffixed_invoices_flag":false,"company_no_not_matching_flag":false,"grouped_team_work_flag":false,"inventory_ro_flag":false,"invalid_core_amount_mismatch_exist_flag":false,"misc_exception_exist_flag":false,"multi_labor_exception_exist_flag":false,"scheduled_by_exist_flag":true,"test_data_flag":false,"new_line_type_exception_exist_flag":false,"dealertrack_identify_flag":false}
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: Sharepoint: notification status: true
Fri Jul 11 2025 05:31:07 GMT+0000 : Send Mail: notification status > true
Fri Jul 11 2025 05:39:32 GMT+0000 : CDK : Process XML schedule started
Fri Jul 11 2025 05:39:32 GMT+0000 : CDKFLEX : Process XML schedule started
Fri Jul 11 2025 05:39:32 GMT+0000 : Automate :  Process JSON schedule started
Fri Jul 11 2025 05:39:34 GMT+0000 : Fortellis :  Process JSON schedule started
Fri Jul 11 2025 05:39:34 GMT+0000 : DealerBuilt :  Process JSON schedule started
Fri Jul 11 2025 05:39:34 GMT+0000 : Adam :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Reynolds :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Autosoft :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Pbs :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Mpk Extraction Processing Not Enabled - Pass Job Type mpk-process-json To Enable
Fri Jul 11 2025 05:39:35 GMT+0000 : Tekion :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Tekionapi :  Process JSON schedule started
Fri Jul 11 2025 05:39:35 GMT+0000 : Quorum :  Process JSON schedule started
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : Schedule Reynolds Extract Job {"jobSchedule":"2025-07-11T05:44:23.000Z","jobData":{"groupName":"QAREY52","storeDataArray":[{"locationId":"***************","sourceId":"***************","activityStoreId":"03","projectId":"*********","secondProjectId":"","mageManufacturer":"GM","solve360Update":false,"buildProxies":false,"userName":"<EMAIL>","inputFilePath":"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1","fileDate":"01/28/2025","switchBranch":true,"customBranchName":null,"etlDMSType":"ReynoldsRCI","startDate":"01/01/2025","endDate":"07/11/2025","closedROOption":"monthly","jobType":"initial","mageGroupCode":"QAREY52","mageStoreCode":"QASREY523","stateCode":"AR","projectIds":"********************","secondProjectIdList":"","companyIds":"********************","parentName":"QASREY522 [***************,null,03]","testData":false,"companyObj":"[{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY522\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY522\",\"secondaryProjectName\":null},{\"companyId\":\"*********\",\"projectId\":\"*********\",\"companyName\":\"QASREY52Du\",\"secondProjectId\":null,\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"projectName\":\"QAPREY52Du\",\"secondaryProjectName\":null}]","projectType":"Parts UL","secondaryProjectType":null,"groupCode":"QAREY521","mageStoreName":"QASREY522","errors":"","thirdPartyUsername":"***************","assignedtoCn":"Netspective Sales Team","isCombinedAll":null,"brands":"GM*GM*"}]}}
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : doPayloadAction inpObjProject - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : doPayloadAction inpObjSecondProject - undefined
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : doPayloadAction for project 1 - {"inProjectId":"********************","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:44:23 GMT+0000 : projectIdList ELSE*********,*********,
Fri Jul 11 2025 05:44:23 GMT+0000 : testData ELSEfalse
Fri Jul 11 2025 05:44:23 GMT+0000 : testData ELSe test1
Fri Jul 11 2025 05:44:23 GMT+0000 : id=>*********
Fri Jul 11 2025 05:44:23 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:44:23 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:44:23 GMT+0000 : id=>*********
Fri Jul 11 2025 05:44:23 GMT+0000 : id test2=>*********
Fri Jul 11 2025 05:44:23 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"assign","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:44:23 GMT+0000 : Reynolds : doPayloadAction(While Scheduling) Project Id -********* 
Fri Jul 11 2025 05:44:23 GMT+0000 : id=>
Fri Jul 11 2025 05:44:23 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:44:23 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:44:23 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:44:23 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:44:25 GMT+0000 : processCompanyData ..333.  job:[object Object]userName:<EMAIL>:QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zipfilePath:/home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zipJOB_TYPE:ReynoldsRCI
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY522","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711054425004693","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip"}}
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711054425004693"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip"
Fri Jul 11 2025 05:44:25 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:44:25 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile inputData: {"dms":"ReynoldsRCI","mageGrpData":{"state":"AR","mageGroupCode":"QAREY521","mageGroupName":"QAREY52","mageStoreName":"QASREY522","mageStoreCode":"QASREY523","mageProjectId":"*********","mageSecondaryId":"","mageManufacturer":"GM","mageProjectName":"QAPREY52Du","mageProjectType":"Parts UL","secondaryProjectType":"","secondaryProjectName":"","companyId":"*********","userName":"<EMAIL>","schedulerId":"rc20250711054425004693","sourceFile":"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip"}}
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile filePath: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile: inputData - [object Object]
Fri Jul 11 2025 05:44:25 GMT+0000 : createConfigFile: configContent - # Bash Source Test Config File

export DMS_BASE='ReynoldsRCI'
source "$DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/ReynoldsRCI.env"

DMS_BASE_DIR='/etl/home/<USER>/DU-ETL'
BASE_DIR=$HOME/tmp

# Defining ETI here precludes
# the run-template from computing
# it from BASE_DIR
ETI="${DU_ETL_ETI_DIR_ReynoldsRCI}"
PROMPT_FOR_FILE='true'

#ETL  DMS: ReynoldsRCI
#Base DMS: ReynoldsRCI (optional)

DMS="ReynoldsRCI"
DATE_PART='202507'

GROUP_CODE="QAREY521"
GROUPNAME="QAREY52"
STORENAME="QASREY522"
STORE_PART="QASREY523"
MFG="GM"
STATE='AR'

if [[ "$DISTRIBUTE_ONLY" = 'true' ]]; then
    GROUP_CODE="$GROUP_CODE"
    SERVICE_NAME=${GGS_PROD_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_PROD_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
else
    GROUP_CODE=LOADTEST
    SERVICE_NAME=${GGS_LOCAL_PG_SERVICE}
    AUDIT_SERVICE_NAME=${AUDIT_LOCAL_PG_SERVICE}
    ETL_DIST="${DU_ETL_DIST_DIR}"
fi

SCENARIOKEY="[$GROUP_CODE]${STORE_PART}+${DATE_PART}_${MFG}"

COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
SECONDARY_PROJECT_ID=""
SECONDARY_PROJECT_TYPE=""
SECONDARY_PROJECT_NAME=""
IMPORTED_BY='<EMAIL>'
SCHEDULER_ID="rc20250711054425004693"
MACHINE=etl3.aws.production.dealeruplift.net
SOURCE_FILE="QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip"
Fri Jul 11 2025 05:44:25 GMT+0000 : Config file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:44:25 GMT+0000 : Generating job.txt file
Fri Jul 11 2025 05:44:25 GMT+0000 : Job file successfully added to zip: /home/<USER>/tmp/du-etl-dms-reynoldsrci-extractor-work/scheduler-temp/reynoldsrci-zip-eti/QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip
Fri Jul 11 2025 05:45:08 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:44:25.003Z\",\"uniqueId\":\"rc20250711054425004693\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip\",\"endTime\":\"2025-07-11T05:44:25.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:45:08 GMT+0000 : Inside doPayloadAction - : {"inProjectId":"*********","inSource":"Scheduler","inAction":"fail","inAssignee":"<EMAIL>","inPerformedOn":"2025-07-11","inPerformedBy":"<EMAIL>","inData":"{\"locationId\":\"***************\",\"sourceId\":\"***************\",\"activityStoreId\":\"03\",\"projectId\":\"*********\",\"secondProjectId\":\"\",\"mageManufacturer\":\"GM\",\"solve360Update\":false,\"buildProxies\":false,\"userName\":\"<EMAIL>\",\"inputFilePath\":\"/etl/etl-vagrant/etl-reynolds/webhook-reynolds/archive/199001288329074__01_01_1\",\"fileDate\":\"01/28/2025\",\"switchBranch\":true,\"customBranchName\":null,\"etlDMSType\":\"ReynoldsRCI\",\"startDate\":\"01/01/2025\",\"endDate\":\"07/11/2025\",\"closedROOption\":\"monthly\",\"jobType\":\"initial\",\"mageGroupCode\":\"QAREY52\",\"mageStoreCode\":\"QASREY523\",\"stateCode\":\"AR\",\"projectIds\":\"********************\",\"secondProjectIdList\":\"\",\"companyIds\":\"********************\",\"parentName\":\"QASREY522 [***************,null,03]\",\"testData\":false,\"companyObj\":\"[{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY522\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY522\\\",\\\"secondaryProjectName\\\":null},{\\\"companyId\\\":\\\"*********\\\",\\\"projectId\\\":\\\"*********\\\",\\\"companyName\\\":\\\"QASREY52Du\\\",\\\"secondProjectId\\\":null,\\\"projectType\\\":\\\"Parts UL\\\",\\\"secondaryProjectType\\\":null,\\\"projectName\\\":\\\"QAPREY52Du\\\",\\\"secondaryProjectName\\\":null}]\",\"projectType\":\"Parts UL\",\"secondaryProjectType\":null,\"groupCode\":\"QAREY521\",\"mageStoreName\":\"QASREY522\",\"errors\":\"\",\"thirdPartyUsername\":\"***************\",\"assignedtoCn\":\"Netspective Sales Team\",\"isCombinedAll\":null,\"brands\":\"GM*GM*\",\"inProjectId\":\"*********\",\"in_is_update_retrieve_ro\":false,\"in_data_pulled_via\":\"Scheduler\",\"in_retrive_ro_request_on\":\"2025-07-11\",\"invoiceMasterCSVFilePath\":\"\",\"startTime\":\"2025-07-11T05:44:25.003Z\",\"uniqueId\":\"rc20250711054425004693\",\"processFileName\":\"QAREY52-QASREY523-AR-WEBHOOK-***************-_01_01_20250711054425.zip\",\"endTime\":\"2025-07-11T05:44:25.086Z\",\"status\":true,\"message\":\"n/a\"}","inCreatedBy":"<EMAIL>"}
Fri Jul 11 2025 05:45:08 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:45:08 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:45:08 GMT+0000 : doPayloadAction - Status: 200
Fri Jul 11 2025 05:45:08 GMT+0000 : doPayloadAction - body: {"status":"success","message":"Added to Process Queue"}
Fri Jul 11 2025 05:45:10 GMT+0000 : Inside send mail method
Fri Jul 11 2025 05:45:10 GMT+0000 : Send Mail: notification status > true
