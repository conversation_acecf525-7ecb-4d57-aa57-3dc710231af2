#!/usr/bin/env bash
Test message
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

BUNDLE_TYPE='initial' #initial or refresh

# MM/DD/YYYY usually 6 months ago or so for initial and 31 days for refresh
day_of_month=$(date +%-d)
if ((day_of_month < 16)); then
     START_DATE=$(date --date '6 months ago' +%m)/01/$(date --date '6 months ago' +%Y)
else
     START_DATE=$(date --date '5 months ago' +%m)/01/$(date --date '5 months ago' +%Y)
fi
# MM/DD/YYYY usually today
START_DATE='03/15/2013'
END_DATE=$(date +%m/%d/%Y)

WORK_DIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work
BUNDLE_DIR="$WORK_DIR"/run-live-temp/reynoldsrci-zip-eti
DEADLETTER_DIR="$WORK_DIR"/run-live-temp/dead-letter-extracted

mkdir -p "$BUNDLE_DIR" "$DEADLETTER_DIR"

function do_pull() {

STORE_ID="$1"
FRANCHISE="$2"
USERNAME="$3"
PASSWORD="$4"
GROUP_CD="$5"
STORE_CD="$6"
STATE_CD="$7"

reynoldsrci-requestor pull  --storeID "$STORE_ID" \
                    --franchise "$FRANCHISE" \
                    --storeUsername "$USERNAME" \
                    --storePassword "$PASSWORD" \
                    --startDate "$START_DATE" \
                    --endDate "$END_DATE" \
                    --bundle "$BUNDLE_TYPE" \
                    --zipPath "$BUNDLE_DIR" \
                    --mageGroupCode "$GROUP_CD" \
                    --mageStoreCode "$STORE_CD" \
                    --stateCode "$STATE_CD" \
                    --deadLetter "$DEADLETTER_DIR"

}

echo "Start: $START_DATE"
echo "End  : $END_DATE"

prompt_continuation 'Confirm dates and continue.'

do_pull '12695' 'DU' 'APIUSER' 'TestP4' 'ReynoldsRCI' 'Store' 'State'
