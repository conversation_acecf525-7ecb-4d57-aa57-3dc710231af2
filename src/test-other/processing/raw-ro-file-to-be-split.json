{"rey_ArmatusDealerUpliftRepairOrder": {"xsi:noNamespaceSchemaLocation": "rey_ArmatusDealerUpliftRepairOrder.xsd", "xmlns": "http://www.starstandards.org/STAR", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "ApplicationArea": {"Sender": {"Component": "ERA", "Task": "SRO", "DealerNumber": "539101325714467", "StoreNumber": "01", "AreaNumber": "02"}, "CreationDateTime": "2021-04-21T00:00:39", "BODId": "e87847fa-07eb-4e9a-9bbc-277abc396ec2", "Destination": {"DestinationNameCode": "RCI"}}, "RepairOrder": [{"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/10/2021", "WarrPostDate": "02/10/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "16:08:16", "RoCreateDate": "02/09/2021", "MileageOut": "109130", "MileageIn": "109128", "CarlineDesc": "ACURA", "Vin": "2HNYD18973H515037", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "JOSHUA D STRONG", "AdvNo": "1629", "CustName": "MINH TRAN", "CustNo": "606774", "RoNo": "783404", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "27.00", "NTxblAmt": "27.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "10ACZ", "OpCodeDesc": "ENGINE", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "1.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "TECHNICIAN REPLACED THE SUCTION TUBE AND SUCTION O-RING AS", "Cause": "POWER STEERING PUMP IS CAVITATING", "Complaint": "CHECK FOR NOISE NEAR POWER STEERING PUMP WHEN ENGINE IS COLD"}, {"Correction": "WELL AS THE PRESSURE O-RING AND REFILLED SYSTEM. PUMP IS NOW"}, {"Correction": "QUIET AT THIS TIME."}], "RoAmts": {"DlrCost": "27.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "27.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "PERFORM MULTI POINT INSPECTION REPORT AT NO CHARGE TO THE"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS", "Complaint": "TO THE CUSTOMER"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "00ACZCOURWASH", "OpCodeDesc": "ACURA COMP WASH", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "THANK YOU FOR BEING OUR CUSTOMER AND FOR CHOOSING US TO", "Cause": "VALUE ADDED CUSTOMER APPRECIATION", "Complaint": "ACURA COMPLIMENTARY WASH TO INCLUDE - TOUCHLESS CAR WASH -"}, {"Correction": "SERVICE YOUR VEHICLE TODAY! IF YOU SHOULD HAVE ANY CONCERNS", "Complaint": "INTERIOR FRONT CAB AREA VACUUM - INTERIOR DASH BOARD DUSTED"}, {"Correction": "WITH YOUR SERVICE WORK TODAY - PLEASE DO NOT HESITATE IN", "Complaint": "TIRE DRESSING APPLIED"}, {"Correction": "REACHING OUT TO ANY OF OUR TEAM MEMBERS!", "Complaint": "NOTE: NOT RES<PERSON><PERSON><PERSON><PERSON> FOR CURRENT WINDSHIELD CHIPS - STARS -"}, {"Correction": "SEE YOU ON YOUR NEXT SERVICE VISIT!", "Complaint": "CRACKS - THAT MAY EXPAND/GROW AFTER COMPLIMENTARY CAR WASH!"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "1", "OpCode": "10ACZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "8.18", "NTxblAmt": "15.14", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "O-RING (14.4X1.9)", "PartNo": "AC91370-SV4-000", "RoAmts": {"DlrCost": "0.88", "PayType": "Intr", "CustPrice": "1.63"}}, {"SeqNo": "2", "PartNoDesc": "TUBE, SUCTION", "PartNo": "AC53731-S3V-A00", "RoAmts": {"DlrCost": "6.74", "PayType": "Intr", "CustPrice": "12.47"}}, {"SeqNo": "3", "PartNoDesc": "O-RING (13.0X1.9)", "PartNo": "AC91345-RDA-A01", "RoAmts": {"DlrCost": "0.56", "PayType": "Intr", "CustPrice": "1.04"}}]}, {"JobNo": "2", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "3", "OpCode": "00ACZCOURWASH", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "-42.14", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}, {"CodeAmt": "-42.14", "MiscCodeDesc": "SERVICE POLICY", "MiscCode": "SP"}]}, {"JobNo": "2"}, {"JobNo": "3"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "1", "OpCode": "10ACZ"}, {"JobNo": "2", "OpCode": "00ACZINSP"}, {"JobNo": "3", "OpCode": "00ACZCOURWASH"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "SILVER", "Carline": "MDX", "ModelDesc": "4DR SUV AT TOUR R/NA", "VehicleYr": "2003", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"FirstName": "MINH", "LastName": "TRAN", "NameRecId": "606774", "IBFlag": "I", "Address": {"Zip": "800214070", "State": "CO", "City": "WESTMINSTER", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "1155.83", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/17/2021", "WarrPostDate": "02/17/2021", "CustInvoiceDate": "02/17/2021", "RoCreateTime": "16:21:08", "RoCreateDate": "02/09/2021", "MileageOut": "36590", "MileageIn": "36584", "CarlineDesc": "HONDA", "Vin": "2HKRW2H51JH630955", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "MACLOVIA R CARRILLO", "AdvNo": "1413", "CustName": "FISHER HONDA", "CustNo": "1500", "RoNo": "783408", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "159.60", "NTxblAmt": "548.77", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "6", "OpCode": "27CVZ071", "OpCodeDesc": "MNT 4 NEW TIRES", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "15.00", "TechHrs": "0.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "15.00", "TechHrs": "1.20", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"IntrTechRate": "15.00", "TechHrs": "0.40", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}], "BillTimeRateHrs": {"BillRate": "60.00"}, "CCCStmts": [{"Correction": "COMPLETED 4 NEW TIRE INSTALL", "Cause": "MAINTENANCE ITEM", "Complaint": "INSTALL 4 NEW TIRES"}, {"Complaint": "AND REMOVE WHEEL LOCKS"}], "RoAmts": {"DlrCost": "24.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "84.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "7", "OpCode": "34CVZ2", "OpCodeDesc": "REPL ENG/CAB FILTERS", "TechInfo": {"IntrTechRate": "33.50", "TechHrs": "0.30", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "RoAmts": {"DlrCost": "10.05", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "28.05"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "22CVZ014", "OpCodeDesc": "USED VEHICLE INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "1.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"IntrTechRate": "33.50", "TechHrs": "0.70", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"IntrTechRate": "33.50", "TechHrs": "-0.70", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED INSPECTION", "Cause": ".", "Complaint": "PERFORM NON-ACURA USED VEHICLE INSPECTION"}, {"Correction": ".", "Complaint": "AND INSTALL FRONT AND REAR FISHER LICENSE PLATE INSERTS"}], "RoAmts": {"DlrCost": "33.50", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "125.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34CVZ", "OpCodeDesc": "OIL CHANGE-NON ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.30", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED OIL AND FILTER CHANGE SERVICE", "Cause": ".", "Complaint": "PLEASE PERFORM OIL AND FILTER CHANGE SERVICE"}, "RoAmts": {"DlrCost": "10.05", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "09CVZ010", "OpCodeDesc": "FULL U/C DETAIL", "TechInfo": [{"IntrTechRate": "20.00", "TechHrs": "4.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED", "Complaint": "PERFORM DETAIL"}, "RoAmts": {"DlrCost": "80.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "275.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "09CVZ044", "OpCodeDesc": "M/TECH WINDSHIELD", "TechInfo": [{"IntrTechRate": "20.00", "TechHrs": "0.10", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED APPLICATION", "Cause": "ANCILLARY PRODUCT INSTALLATION", "Complaint": "APPLY THE MASTER TECH WINDSHIELD TREATMENT"}, "RoAmts": {"DlrCost": "2.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "09CVZ045", "OpCodeDesc": "INSTALL ETCH", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED THE INSTALL OF ETCH", "Cause": "PROTECTION", "Complaint": "INSTALL ETCH"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "6", "OpCode": "27CVZ071", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "395.60", "NTxblAmt": "498.84", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "7", "PartNoDesc": "TIRE (235/60R18)", "PartNo": "AC42751-BRI-125", "RoAmts": {"DlrCost": "95.50", "PayType": "Intr", "CustPrice": "118.42"}}, {"SeqNo": "6", "PartNoDesc": "NUT, WHEEL", "PartNo": "AC90381-S87-A01", "RoAmts": {"DlrCost": "3.40", "PayType": "Intr", "CustPrice": "6.29"}}]}, {"JobNo": "7", "OpCode": "34CVZ2", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "32.85", "NTxblAmt": "60.78", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "5", "PartNoDesc": "ELEMENT, FILTER", "PartNo": "AC80291-T5R-A01", "RoAmts": {"DlrCost": "18.16", "PayType": "Intr", "CustPrice": "33.60"}}, {"SeqNo": "8", "PartNoDesc": "AIR FILTER", "PartNo": "GM17220-5AA-A00", "RoAmts": {"DlrCost": "14.69", "PayType": "Intr", "CustPrice": "27.18"}}]}, {"JobNo": "1", "OpCode": "22CVZ014", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "34CVZ", "RoAmts": [{"DlrCost": "17.80", "NTxblAmt": "26.44", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Intr"}}, {"SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Intr", "CustPrice": "0.59"}}, {"SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Intr", "CustPrice": "8.10"}}, {"SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Intr", "CustPrice": "3.55"}}]}, {"JobNo": "3", "OpCode": "09CVZ010", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "4", "OpCode": "09CVZ044", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "5", "OpCode": "09CVZ045", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Gog", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Paint", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "ShopSuppl", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Freight", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}]}, "Romisc": {"IntrLbrTot": "21.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "21.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "1155.83"}], "MiscOpCodeInfo": [{"JobNo": "6"}, {"JobNo": "7"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "21.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "6", "OpCode": "27CVZ071"}, {"JobNo": "7", "OpCode": "34CVZ2"}, {"JobNo": "1", "OpCode": "22CVZ014"}, {"JobNo": "2", "OpCode": "34CVZ"}, {"JobNo": "3", "OpCode": "09CVZ010"}, {"JobNo": "4", "OpCode": "09CVZ044"}, {"JobNo": "5", "OpCode": "09CVZ045"}]}}, "ServVehicle": {"Vehicle": {"IntClrDesc": "BLACK", "ExtClrDesc": "WHITE", "Carline": "CR-V", "ModelDesc": "UP", "VehicleYr": "2018", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER HONDA", "NameRecId": "1500", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "1221.21", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/18/2021", "WarrPostDate": "02/18/2021", "CustInvoiceDate": "02/18/2021", "RoCreateTime": "16:30:25", "RoCreateDate": "02/09/2021", "MileageOut": "52065", "MileageIn": "52051", "CarlineDesc": "HONDA", "Vin": "JHMGK5H55HS015341", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "CLAUDIO D QUADRINI MUNOZ", "AdvNo": "1126", "CustName": "FISHER HONDA", "CustNo": "1500", "RoNo": "783409", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "167.90", "NTxblAmt": "584.77", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "7", "OpCode": "15CVZ018", "OpCodeDesc": "WIPER BLADES REPLACE", "TechInfo": {"IntrTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "8", "OpCode": "34CVZ2", "OpCodeDesc": "REPL ENG/CAB FILTERS", "TechInfo": {"IntrTechRate": "27.00", "TechHrs": "0.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED THE REPLACEMENT OF THE ENGINE AND CABIN", "Cause": ".", "Complaint": "REPLACE THE ENGINE AND CABIN AIR FILTERS"}, {"Correction": "AIR FILTERS"}], "RoAmts": {"DlrCost": "8.10", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "28.05"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "9", "OpCode": "15CVZ009", "OpCodeDesc": "BATTERY REPLACEMENT", "TechInfo": {"IntrTechRate": "27.00", "TechHrs": "1.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED INSTALLATION", "Cause": "MAINTENANCE ITEM", "Complaint": "REPLACE BATTERY ASSEMBLY PER ED-18 TESTER RESULTS"}, "RoAmts": {"DlrCost": "27.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "60.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "22CVZ014", "OpCodeDesc": "USED VEHICLE INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "1.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"IntrTechRate": "27.00", "TechHrs": "1.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"IntrTechRate": "27.00", "TechHrs": "-1.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED INSPECTION", "Cause": ".", "Complaint": "PERFORM NON-ACURA USED VEHICLE INSPECTION"}, {"Correction": ".", "Complaint": "AND INSTALL FRONT AND REAR FISHER LICENSE PLATE INSERTS"}], "RoAmts": {"DlrCost": "27.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "125.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34CVZ", "OpCodeDesc": "OIL CHANGE-NON ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED OIL AND FILTER CHANGE SERVICE", "Cause": ".", "Complaint": "PLEASE PERFORM OIL AND FILTER CHANGE SERVICE"}, "RoAmts": {"DlrCost": "8.10", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "09CVZ010", "OpCodeDesc": "FULL U/C DETAIL", "TechInfo": [{"IntrTechRate": "17.00", "TechHrs": "4.00", "TechName": "JACE MARTINEZ", "TechNo": "1469"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"IntrTechRate": "17.00", "TechHrs": "-3.00", "TechName": "JACE MARTINEZ", "TechNo": "1469"}, {"IntrTechRate": "20.00", "TechHrs": "2.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"IntrTechRate": "15.00", "TechHrs": "1.00", "TechName": "FRANK CORALLO", "TechNo": "1706"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED", "Complaint": "PERFORM DETAIL"}, "RoAmts": {"DlrCost": "72.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "275.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "09CVZ044", "OpCodeDesc": "M/TECH WINDSHIELD", "TechInfo": [{"IntrTechRate": "17.00", "TechHrs": "0.10", "TechName": "JACE MARTINEZ", "TechNo": "1469"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "17.00", "TechHrs": "0.00", "TechName": "JACE MARTINEZ", "TechNo": "1469"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED APPLICATION", "Cause": "ANCILLARY PRODUCT INSTALLATION", "Complaint": "APPLY THE MASTER TECH WINDSHIELD TREATMENT"}, "RoAmts": {"DlrCost": "1.70", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "09CVZ045", "OpCodeDesc": "INSTALL ETCH", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "17.00", "TechHrs": "0.00", "TechName": "JACE MARTINEZ", "TechNo": "1469"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "17.00", "TechHrs": "0.00", "TechName": "JACE MARTINEZ", "TechNo": "1469"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED THE INSTALL OF ETCH", "Cause": "PROTECTION", "Complaint": "INSTALL ETCH"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "6", "OpCode": "15CVZ012", "OpCodeDesc": "MNT 4 NEW TIRES", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "1.20", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "60.00"}, "RoAmts": {"DlrCost": "24.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "60.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "7", "OpCode": "15CVZ018", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "32.12", "NTxblAmt": "59.42", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "7", "PartNoDesc": "BLADE", "PartNo": "GM60281", "RoAmts": {"DlrCost": "14.54", "PayType": "Intr", "CustPrice": "26.90"}}, {"SeqNo": "8", "PartNoDesc": "BLADE", "PartNo": "GM60141", "RoAmts": {"DlrCost": "8.20", "PayType": "Intr", "CustPrice": "15.17"}}, {"SeqNo": "9", "PartNoDesc": "BLADE", "PartNo": "GM6014B", "RoAmts": {"DlrCost": "9.38", "PayType": "Intr", "CustPrice": "17.35"}}]}, {"JobNo": "8", "OpCode": "34CVZ2", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "30.09", "NTxblAmt": "55.67", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "6", "PartNoDesc": "AIR CLIP", "PartNo": "GM17219P65000", "RoAmts": {"DlrCost": "3.48", "PayType": "Intr", "CustPrice": "6.44"}}, {"SeqNo": "10", "PartNoDesc": "AIR FILTER", "PartNo": "*********", "RoAmts": {"DlrCost": "12.46", "PayType": "Intr", "CustPrice": "23.05"}}, {"SeqNo": "11", "PartNoDesc": "CABIN AIR", "PartNo": "GM229101", "RoAmts": {"DlrCost": "14.15", "PayType": "Intr", "CustPrice": "26.18"}}]}, {"JobNo": "9", "OpCode": "15CVZ009", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "82.62", "NTxblAmt": "137.70", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "12", "PartNoDesc": "BATTERY (44B19L-S", "PartNo": "AC31500-SNC-00100M", "RoAmts": {"DlrCost": "98.62", "PayType": "Intr", "CustPrice": "153.70"}}, {"SeqNo": "13", "PartNoDesc": "CORE RETURN", "PartNo": "AC31500-SNC-00100M", "RoAmts": {"DlrCost": "16.00", "PayType": "Intr", "CustPrice": "16.00"}}]}, {"JobNo": "1", "OpCode": "22CVZ014", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "34CVZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "15.18", "NTxblAmt": "22.89", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Intr"}}, {"SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Intr", "CustPrice": "0.59"}}, {"SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Intr", "CustPrice": "8.10"}}, {"SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Intr", "CustPrice": "3.55"}}]}, {"JobNo": "3", "OpCode": "09CVZ010", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "4", "OpCode": "09CVZ044", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "5", "OpCode": "09CVZ045", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "6", "OpCode": "15CVZ012", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "274.00", "NTxblAmt": "339.76", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": {"SeqNo": "5", "PartNoDesc": "TIRE 185/60R15", "PartNo": "AC42751-GEN-591", "RoAmts": {"DlrCost": "68.50", "PayType": "Intr", "CustPrice": "84.94"}}}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Gog", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Paint", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "ShopSuppl", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Freight", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}]}, "Romisc": {"IntrLbrTot": "21.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "21.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "1221.21"}], "MiscOpCodeInfo": [{"JobNo": "7"}, {"JobNo": "8"}, {"JobNo": "9"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "21.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}, {"JobNo": "6"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "7", "OpCode": "15CVZ018"}, {"JobNo": "8", "OpCode": "34CVZ2"}, {"JobNo": "9", "OpCode": "15CVZ009"}, {"JobNo": "1", "OpCode": "22CVZ014"}, {"JobNo": "2", "OpCode": "34CVZ"}, {"JobNo": "3", "OpCode": "09CVZ010"}, {"JobNo": "4", "OpCode": "09CVZ044"}, {"JobNo": "5", "OpCode": "09CVZ045"}, {"JobNo": "6", "OpCode": "15CVZ012"}]}}, "ServVehicle": {"Vehicle": {"IntClrDesc": "BLACK", "ExtClrDesc": "GRAY", "Carline": "FIT", "ModelDesc": "WG", "VehicleYr": "2017", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER HONDA", "NameRecId": "1500", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/17/2021", "WarrPostDate": "02/17/2021", "CustInvoiceDate": "02/17/2021", "RoCreateTime": "16:33:01", "RoCreateDate": "02/09/2021", "MileageOut": "69810", "MileageIn": "69810", "CarlineDesc": "ACURA", "Vin": "5FRYD4H94HB003318", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "CLAUDIO D QUADRINI MUNOZ", "AdvNo": "1126", "CustName": "FISHER ACURA", "CustNo": "2503", "RoNo": "783410", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "30.00", "NTxblAmt": "30.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "05ACZ", "OpCodeDesc": "BODY AND TRIM", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "15.00", "TechHrs": "0.00", "TechName": "FRANK CORALLO", "TechNo": "1706"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "15.00", "TechHrs": "2.00", "TechName": "FRANK CORALLO", "TechNo": "1706"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "DAMAGED WH<PERSON><PERSON> IN SERVICE FOR RECONN", "Cause": "AND REDETAIL", "Complaint": "PLEASE REPLACE DRIVERS SIDE DOOR GLASS (BROKEN)"}, {"Correction": "APPROVED BY RB"}], "RoAmts": {"DlrCost": "30.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "30.00"}}}, "Ropart": {"PartInfoByJob": {"JobNo": "1", "OpCode": "05ACZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "-325.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}, {"CodeAmt": "-325.00", "MiscCodeDesc": "SERVICE POLICY", "MiscCode": "SP"}]}}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All", "RoAmts": [{"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "295.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Labor", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "295.00", "NTxblAmt": "295.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Parts", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}], "SubInfoByJob": {"JobNo": "1", "OpCode": "05ACZ", "SubDetail": [{"SubletPoNo": "15485", "SubletDesc": "REPLACE LF DOOR GLASS"}, {"SubletPoNo": "15486", "SubletDesc": "TINT LF DOOR GLASS"}]}}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "BLUE", "Carline": "MDX", "ModelDesc": "UP", "VehicleYr": "2017", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER ACURA", "NameRecId": "2503", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "1446.16", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/11/2021", "RoCreateTime": "17:05:52", "RoCreateDate": "02/09/2021", "MileageOut": "18472", "MileageIn": "18470", "CarlineDesc": "ACURA", "Vin": "19UDE2F31HA010012", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "DALE R VILLEGAS", "AdvNo": "1034", "CustName": "DAVID REIS", "CustNo": "217013", "RoNo": "783412", "RoCommentInfo": [{"RoComment": "### Created By: dale.ville<PERSON>@fisherauto.com, Created On: 02-09-202"}, {"RoComment": "1, Status: Scheduled, Transport Type: DROP OFF"}], "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "60000 MILE SERVICE", "RecSvcOpCode": "17ACZ080"}}, "Rolabor": {"RoAmts": [{"DlrCost": "113.30", "NTxblAmt": "426.85", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "4", "OpCode": "15ACZ022", "OpCodeDesc": "BATTERY REPLACEMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.50", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": {"Correction": "COMPLETED INSTALLATION", "Cause": "MAINTENANCE ITEM", "Complaint": "REPLACE BATTERY ASSEMBLY PER ED-18 TESTER RESULTS"}, "RoAmts": {"DlrCost": "11.75", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "30.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "5", "OpCode": "34ACZ7", "OpCodeDesc": "FLUSH BRK SYSTEM", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "1.10", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED BRAKE SYSTEM FLUSH", "Cause": ".", "Complaint": "FLUSH BRAKE FLUID SYSTEM"}, "RoAmts": {"DlrCost": "25.85", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "119.90"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "6", "OpCode": "34ACZALI", "OpCodeDesc": "ALIGNMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "1.20", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED ALIGNMENT", "Cause": ".", "Complaint": "PERFORM ALIGNMENT"}, "RoAmts": {"DlrCost": "28.20", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "119.95"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "7", "OpCode": "15ACZ110", "OpCodeDesc": "INSTALL 4 NEW TIRES", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "20.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "20.00", "TechHrs": "1.20", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED INSTALLATION. NE<PERSON> TIRES PURCHASED HERE COME WITH", "Complaint": "MOUNT AND <PERSON>LANCE FOUR NEW TIRES"}, {"Correction": "FREE LIFETIME ROTATION AND TIRE REPAIR"}], "RoAmts": {"DlrCost": "24.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "60.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "00ACZCOURWASH", "OpCodeDesc": "ACURA COMP WASH", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "THANK YOU FOR BEING OUR CUSTOMER AND FOR CHOOSING US TO", "Cause": "VALUE ADDED CUSTOMER APPRECIATION", "Complaint": "ACURA COMPLIMENTARY WASH TO INCLUDE - TOUCHLESS CAR WASH -"}, {"Correction": "SERVICE YOUR VEHICLE TODAY! IF YOU SHOULD HAVE ANY CONCERNS", "Complaint": "INTERIOR FRONT CAB AREA VACUUM - INTERIOR DASH BOARD DUSTED"}, {"Correction": "WITH YOUR SERVICE WORK TODAY - PLEASE DO NOT HESITATE IN", "Complaint": "TIRE DRESSING APPLIED"}, {"Correction": "REACHING OUT TO ANY OF OUR TEAM MEMBERS!", "Complaint": "NOTE: NOT RES<PERSON><PERSON><PERSON><PERSON> FOR CURRENT WINDSHIELD CHIPS - STARS -"}, {"Correction": "SEE YOU ON YOUR NEXT SERVICE VISIT!", "Complaint": "CRACKS - THAT MAY EXPAND/GROW AFTER COMPLIMENTARY CAR WASH!"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "BATTERY,<PERSON>NOW TIRES INSPECTION"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "23ACZ", "OpCodeDesc": "ELECTRICAL", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "23.50", "TechHrs": "1.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": [{"Correction": "TECHNICIAN DUPLICATED CUSTOMER CONCERN. TESTED BATTERY,", "Complaint": "CUSTOMER STATES THAT VEHICLE WILL NOT START"}, {"Correction": "BATTERY FAILED TEST."}], "RoAmts": {"DlrCost": "23.50", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "97.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "4", "OpCode": "15ACZ022", "RoAmts": [{"DlrCost": "83.88", "NTxblAmt": "0.00", "TxblAmt": "147.16", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "1", "PartNoDesc": "*31500-SR1-100MS", "PartNo": "AC31500-SR1-100M", "RoAmts": {"DlrCost": "99.88", "PayType": "Cust", "CustPrice": "163.16"}}, {"CustQtyShip": "-1", "SeqNo": "2", "PartNoDesc": "CORE RETURN", "PartNo": "AC31500-SR1-100M", "RoAmts": {"DlrCost": "16.00", "PayType": "Cust", "CustPrice": "16.00"}}]}, {"JobNo": "5", "OpCode": "34ACZ7", "RoAmts": [{"DlrCost": "15.75", "NTxblAmt": "0.00", "TxblAmt": "26.98", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": {"CustQtyShip": "1", "SeqNo": "3", "PartNoDesc": "BG DOT 4", "PartNo": "GM84032", "RoAmts": {"DlrCost": "15.75", "PayType": "Cust", "CustPrice": "26.98"}}}, {"JobNo": "6", "OpCode": "34ACZALI", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "7", "OpCode": "15ACZ110", "RoAmts": [{"DlrCost": "606.72", "NTxblAmt": "0.00", "TxblAmt": "722.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": {"CustQtyShip": "4", "SeqNo": "4", "PartNoDesc": "215/45R17 GY ASSU", "PartNo": "AC7675-68537", "RoAmts": {"DlrCost": "151.68", "PayType": "Cust", "CustPrice": "180.50"}}}, {"JobNo": "1", "OpCode": "00ACZCOURWASH", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "3", "OpCode": "23ACZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All", "RoAmts": [{"DlrCost": "6.00", "NTxblAmt": "0.00", "TxblAmt": "19.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Gog", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Paint", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "ShopSuppl", "RoAmts": [{"DlrCost": "6.00", "TxblAmt": "19.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "19.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}, {"AllTotAmtType": "Freight", "RoAmts": [{"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "CustPrice": "0.00"}, {"DlrCost": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "CustPrice": "0.00"}]}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "-91.52", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "1446.16"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "4"}, {"JobNo": "5"}, {"JobNo": "6"}, {"JobNo": "7"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}]}, {"JobNo": "2"}, {"JobNo": "3", "MiscLineItmInfo": {"CodeAmt": "-97.00", "MiscCodeDesc": "LABOR DISCOUNT", "MiscCode": "LD"}}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All", "RoAmts": [{"DlrCost": "85.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Labor", "RoAmts": [{"DlrCost": "85.00", "NTxblAmt": "114.75", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}, {"AllTotAmtType": "Parts", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}]}], "SubInfoByJob": [{"JobNo": "4", "OpCode": "15ACZ022"}, {"JobNo": "5", "OpCode": "34ACZ7"}, {"JobNo": "6", "OpCode": "34ACZALI"}, {"JobNo": "7", "OpCode": "15ACZ110"}, {"JobNo": "1", "OpCode": "00ACZCOURWASH"}, {"JobNo": "2", "OpCode": "00ACZINSP"}, {"JobNo": "3", "OpCode": "23ACZ", "SubDetail": {"SubletPoNo": "15472", "SubletDesc": "TOW"}}]}}, "ServVehicle": {"Vehicle": {"Carline": "ILX", "ModelDesc": "4DR SDN", "VehicleYr": "2017", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"FirstName": "DAVID", "LastName": "REIS", "NameRecId": "217013", "IBFlag": "I", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "P"}, "Email": {"MailTo": ""}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "35.20", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/11/2021", "RoCreateTime": "17:14:30", "RoCreateDate": "02/09/2021", "MileageOut": "57", "MileageIn": "57", "CarlineDesc": "HONDA", "Vin": "3CZRU6H12LM712789", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "MACLOVIA R CARRILLO", "AdvNo": "1413", "CustName": "ANN ELIZABETH DESTITO", "CustNo": "217003", "RoNo": "783413", "RecommendedServc": [{"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "HONDA EXP (BETTER)", "RecSvcOpCode": "15HOZ010"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "HONDA EXP (BETTER)", "RecSvcOpCode": "27HOZ010"}]}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "12.00", "NTxblAmt": "32.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "09CVZ060", "OpCodeDesc": "CLEAN FOR DELIVERY", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"IntrTechRate": "15.00", "TechHrs": "0.80", "TechName": "FRANK CORALLO", "TechNo": "1706"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED DELIVERY CLEAN UP", "Complaint": "WASH AND CLEAN FOR DELIVERY"}, "RoAmts": {"DlrCost": "12.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "32.00"}}}, "Ropart": {"PartInfoByJob": {"JobNo": "1", "OpCode": "09CVZ060"}}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "3.20", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "3.20", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "35.20"}], "MiscOpCodeInfo": {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "3.20", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": {"JobNo": "1", "OpCode": "09CVZ060"}}}, "ServVehicle": {"Vehicle": {"IntClrDesc": "SPORT", "ExtClrDesc": "RED", "Carline": "HR-V", "ModelDesc": "UP", "VehicleYr": "2020", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"MidName": "ELIZABETH", "FirstName": "ANN", "LastName": "DESTITO", "NameRecId": "217003", "IBFlag": "I", "Address": {"Zip": "80020", "State": "CO", "City": "BROOMFIELD", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "35.20", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/11/2021", "RoCreateTime": "17:17:14", "RoCreateDate": "02/09/2021", "MileageOut": "41148", "MileageIn": "41148", "CarlineDesc": "ACURA", "Vin": "5J8TB4H77JL014799", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "MACLOVIA R CARRILLO", "AdvNo": "1413", "CustName": "FISHER ACURA", "CustNo": "2503", "RoNo": "783414", "RecommendedServc": {"RecSvcOpCdDesc": "45000 MILE SERVICE", "RecSvcOpCode": "17ACZ060"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "12.00", "NTxblAmt": "32.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "09ACZ110", "OpCodeDesc": "CLEAN FOR DELIVERY", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"IntrTechRate": "15.00", "TechHrs": "0.80", "TechName": "FRANK CORALLO", "TechNo": "1706"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "WASHED AND <PERSON><PERSON><PERSON>ED VEHICLE FOR DELIVERY TO THE CUSTOMER", "Complaint": "WASH AND CLEAN FOR DELIVERY"}, "RoAmts": {"DlrCost": "12.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "32.00"}}}, "Ropart": {"PartInfoByJob": {"JobNo": "1", "OpCode": "09ACZ110"}}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "3.20", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "3.20", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "35.20"}], "MiscOpCodeInfo": {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "3.20", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": {"JobNo": "1", "OpCode": "09ACZ110"}}}, "ServVehicle": {"Vehicle": {"IntClrDesc": "BLACK", "ExtClrDesc": "GRAY", "Carline": "RDX", "ModelDesc": "UP", "VehicleYr": "2018", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER ACURA", "NameRecId": "2503", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "35.20", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/11/2021", "RoCreateTime": "17:20:04", "RoCreateDate": "02/09/2021", "MileageOut": "184631", "MileageIn": "184631", "CarlineDesc": "HONDA", "Vin": "JHLRE48798C023694", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "MACLOVIA R CARRILLO", "AdvNo": "1413", "CustName": "ROBERT WILLIAM DIXON", "CustNo": "217002", "RoNo": "783415", "RecommendedServc": {"RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "12.00", "NTxblAmt": "32.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "09CVZ060", "OpCodeDesc": "CLEAN FOR DELIVERY", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"IntrTechRate": "15.00", "TechHrs": "0.80", "TechName": "FRANK CORALLO", "TechNo": "1706"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED DELIVERY CLEAN UP", "Complaint": "WASH AND CLEAN FOR DELIVERY"}, "RoAmts": {"DlrCost": "12.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "32.00"}}}, "Ropart": {"PartInfoByJob": {"JobNo": "1", "OpCode": "09CVZ060"}}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "3.20", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "3.20", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "35.20"}], "MiscOpCodeInfo": {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "3.20", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": {"JobNo": "1", "OpCode": "09CVZ060"}}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "BLUE", "Carline": "CR-V", "ModelDesc": "UP", "VehicleYr": "2008", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"MidName": "WILLIAM", "FirstName": "ROBERT", "LastName": "DIXON", "NameRecId": "217002", "IBFlag": "I", "Address": {"Zip": "80503", "State": "CO", "City": "LONGMONT", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "640.50", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/12/2021", "WarrPostDate": "02/12/2021", "CustInvoiceDate": "02/12/2021", "RoCreateTime": "07:36:43", "RoCreateDate": "02/10/2021", "MileageOut": "17330", "MileageIn": "17324", "CarlineDesc": "HONDA", "Vin": "5FPYK3F77LB010041", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "CLAUDIO D QUADRINI MUNOZ", "AdvNo": "1126", "CustName": "FISHER HONDA", "CustNo": "1500", "RoNo": "783418", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "159.05", "NTxblAmt": "534.77", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "6", "OpCode": "05CVZ", "OpCodeDesc": "BODY & TRIM", "TechInfo": {"IntrTechRate": "33.50", "TechHrs": "0.70", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Complaint": "REMOVE WHEEL LOCKS AND INSTALL FRT PLATE BRACKET"}, "RoAmts": {"DlrCost": "23.45", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "70.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "7", "OpCode": "34CVZ2", "OpCodeDesc": "REPL ENG/CAB FILTERS", "TechInfo": {"IntrTechRate": "33.50", "TechHrs": "0.30", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED THE REPLACEMENT OF THE ENGINE AND CABIN", "Cause": ".", "Complaint": "REPLACE THE ENGINE AND CABIN AIR FILTERS"}, {"Correction": "AIR FILTERS"}], "RoAmts": {"DlrCost": "10.05", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "28.05"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "22CVZ014", "OpCodeDesc": "USED VEHICLE INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "1.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED INSPECTION", "Cause": ".", "Complaint": "PERFORM NON-ACURA USED VEHICLE INSPECTION"}, {"Correction": ".", "Complaint": "AND INSTALL FRONT AND REAR FISHER LICENSE PLATE INSERTS"}], "RoAmts": {"DlrCost": "33.50", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "125.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34CVZ", "OpCodeDesc": "OIL CHANGE-NON ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.30", "TechName": "CHRIS WAYLAND", "TechNo": "1642"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED OIL AND FILTER CHANGE SERVICE", "Cause": ".", "Complaint": "PLEASE PERFORM OIL AND FILTER CHANGE SERVICE"}, "RoAmts": {"DlrCost": "10.05", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "09CVZ010", "OpCodeDesc": "FULL U/C DETAIL", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "4.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED", "Complaint": "PERFORM DETAIL"}, "RoAmts": {"DlrCost": "80.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "275.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "09CVZ044", "OpCodeDesc": "M/TECH WINDSHIELD", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.10", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED APPLICATION", "Cause": "ANCILLARY PRODUCT INSTALLATION", "Complaint": "APPLY THE MASTER TECH WINDSHIELD TREATMENT"}, "RoAmts": {"DlrCost": "2.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "09CVZ045", "OpCodeDesc": "INSTALL ETCH", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED THE INSTALL OF ETCH", "Cause": "PROTECTION", "Complaint": "INSTALL ETCH"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "6", "OpCode": "05CVZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "7", "OpCode": "34CVZ2", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "29.59", "NTxblAmt": "54.74", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "5", "PartNoDesc": "ELEMENT, AIR CLEA", "PartNo": "AC17220-5J6-A10", "RoAmts": {"DlrCost": "16.60", "PayType": "Intr", "CustPrice": "30.71"}}, {"SeqNo": "6", "PartNoDesc": "ELEMENT", "PartNo": "AC80292-SDA-407", "RoAmts": {"DlrCost": "12.99", "PayType": "Intr", "CustPrice": "24.03"}}]}, {"JobNo": "1", "OpCode": "22CVZ014", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "34CVZ", "RoAmts": [{"DlrCost": "20.42", "NTxblAmt": "29.99", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Intr"}}, {"SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Intr", "CustPrice": "0.59"}}, {"SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Intr", "CustPrice": "8.10"}}, {"SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Intr", "CustPrice": "3.55"}}]}, {"JobNo": "3", "OpCode": "09CVZ010", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "4", "OpCode": "09CVZ044", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "5", "OpCode": "09CVZ045", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "21.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "21.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "640.50"}], "MiscOpCodeInfo": [{"JobNo": "6"}, {"JobNo": "7"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "21.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "6", "OpCode": "05CVZ"}, {"JobNo": "7", "OpCode": "34CVZ2"}, {"JobNo": "1", "OpCode": "22CVZ014"}, {"JobNo": "2", "OpCode": "34CVZ"}, {"JobNo": "3", "OpCode": "09CVZ010"}, {"JobNo": "4", "OpCode": "09CVZ044"}, {"JobNo": "5", "OpCode": "09CVZ045"}]}}, "ServVehicle": {"Vehicle": {"IntClrDesc": "GRAY", "ExtClrDesc": "SILVER", "Carline": "RIDGELINE", "ModelDesc": "PK", "VehicleYr": "2020", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER HONDA", "NameRecId": "1500", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "1184.02", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "0.00", "FinalPostDate": "02/17/2021", "WarrPostDate": "02/17/2021", "CustInvoiceDate": "02/17/2021", "RoCreateTime": "07:39:15", "RoCreateDate": "02/10/2021", "MileageOut": "176889", "MileageIn": "176889", "CarlineDesc": "HONDA", "Vin": "SHSRD77884U242017", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "CLAUDIO D QUADRINI MUNOZ", "AdvNo": "1126", "CustName": "FISHER HONDA", "CustNo": "1500", "RoNo": "783422", "RecommendedServc": [{"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "HONDA EXP (BETTER)", "RecSvcOpCode": "15HOZ010"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "HONDA EXP (BETTER)", "RecSvcOpCode": "27HOZ010"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}]}, "Rolabor": {"RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "207.30", "NTxblAmt": "701.72", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "6", "OpCode": "11CVZ", "OpCodeDesc": "ENGINE ELECTRICAL", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.50", "TechName": "DAMON MARES", "TechNo": "1633"}, {"IntrTechRate": "33.50", "TechHrs": "-0.50", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "ATTEMPT TO <PERSON>AR<PERSON> BATTERY. BATTERY STILL FAILED.", "Complaint": "BATTERY HAS LOW CHARGE"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "7", "OpCode": "15CVZ009", "OpCodeDesc": "BATTERY REPLACEMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.50", "TechName": "DAMON MARES", "TechNo": "1633"}, {"IntrTechRate": "33.50", "TechHrs": "0.50", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "REPLACE BATTERY AS NEEDED", "Cause": "BATTERY STILL FAILED", "Complaint": "CHARGE AND RETEST BATTERY"}, "RoAmts": {"DlrCost": "33.50", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "60.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "22CVZAIUVI", "OpCodeDesc": "AS-IS USED VEH INSP", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "1.00", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED AS-IS USED VEHICLE INSPECTION", "Cause": ".", "Complaint": "AS-IS USED VEHICLE INSPECTION"}, {"Complaint": "AND INSTALL FRONT AND REAR FISHER LICENSE PLATE INSERTS"}], "RoAmts": {"DlrCost": "33.50", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "125.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34CVZ", "OpCodeDesc": "OIL CHANGE-NON ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "0.30", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED OIL AND FILTER CHANGE SERVICE", "Cause": ".", "Complaint": "PLEASE PERFORM OIL AND FILTER CHANGE SERVICE"}, "RoAmts": {"DlrCost": "10.05", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "10CVZ", "OpCodeDesc": "ENGINE", "TechInfo": [{"ActualHrsWorked": "0.20", "IntrTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "33.50", "TechHrs": "1.50", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "140.00", "BillTime": "1.50"}, "CCCStmts": [{"Correction": "TECHNICIAN FOUND MULT<PERSON><PERSON> CODE STORED IN COMPUTER. P0135 AND", "Complaint": "CHECK ENGINE LIGHT IS ON.CHECK AND ADVICE."}, {"Correction": "P0134. <PERSON><PERSON><PERSON> RELATED TO O2 SENSOR #1. ATTEMPT TO RESET. CODE"}, {"Correction": "P0135 WILL NOT CLEAR. PERFORM GUIDED DIAGNOSTICS AND FOUND"}, {"Correction": "INTERNAL FAILURE IN SENSOR. REPLACE. NOW OPERATING AS"}, {"Correction": "DESIGNED"}], "RoAmts": {"DlrCost": "50.25", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "210.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "09CVZ010", "OpCodeDesc": "FULL U/C DETAIL", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "4.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED", "Complaint": "PERFORM DETAIL"}, "RoAmts": {"DlrCost": "80.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "275.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "09CVZ045", "OpCodeDesc": "INSTALL ETCH", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "20.00", "TechHrs": "0.00", "TechName": "TONY DELGADO", "TechNo": "1750"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": {"Correction": "COMPLETED THE INSTALL OF ETCH", "Cause": "PROTECTION", "Complaint": "INSTALL ETCH"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "6", "OpCode": "11CVZ", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "7", "OpCode": "15CVZ009", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "83.88", "NTxblAmt": "139.80", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "5", "PartNoDesc": "*31500-SR1-100MS", "PartNo": "AC31500-SR1-100M", "RoAmts": {"DlrCost": "99.88", "PayType": "Intr", "CustPrice": "155.80"}}, {"SeqNo": "6", "PartNoDesc": "CORE RETURN", "PartNo": "AC31500-SR1-100M", "RoAmts": {"DlrCost": "16.00", "PayType": "Intr", "CustPrice": "16.00"}}]}, {"JobNo": "1", "OpCode": "22CVZAIUVI", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "34CVZ", "RoAmts": [{"DlrCost": "16.20", "NTxblAmt": "26.44", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Intr"}}, {"SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Intr", "CustPrice": "0.59"}}, {"SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Intr", "CustPrice": "8.10"}}, {"SeqNo": "4", "PartNoDesc": "5W20 SYN BLD OL", "PartNo": "AC08798-9033AB", "RoAmts": {"DlrCost": "2.30", "PayType": "Intr", "CustPrice": "3.55"}}]}, {"JobNo": "3", "OpCode": "10CVZ", "RoAmts": [{"DlrCost": "159.49", "NTxblAmt": "295.06", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}], "PartDetail": {"SeqNo": "7", "PartNoDesc": "LAF SENSOR", "PartNo": "GMC5015-137301", "RoAmts": {"DlrCost": "159.49", "PayType": "Intr", "CustPrice": "295.06"}}}, {"JobNo": "4", "OpCode": "09CVZ010", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "5", "OpCode": "09CVZ045", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "21.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "0.00", "RoAmts": [{"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "21.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "1184.02"}], "MiscOpCodeInfo": [{"JobNo": "6"}, {"JobNo": "7"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "0.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "21.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "0.00", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "6", "OpCode": "11CVZ"}, {"JobNo": "7", "OpCode": "15CVZ009"}, {"JobNo": "1", "OpCode": "22CVZAIUVI"}, {"JobNo": "2", "OpCode": "34CVZ"}, {"JobNo": "3", "OpCode": "10CVZ"}, {"JobNo": "4", "OpCode": "09CVZ010"}, {"JobNo": "5", "OpCode": "09CVZ045"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "SILVER", "Carline": "CR-V", "ModelDesc": "4WD EX MANUAL", "VehicleYr": "2004", "VehicleMake": "HONDA", "MakeName": "HONDA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"LastName": "FISHER HONDA", "NameRecId": "1500", "IBFlag": "B", "Address": {"Zip": "80303", "State": "CO", "City": "BOULDER", "Type": "B"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "617.12", "FinalPostDate": "02/10/2021", "WarrPostDate": "02/10/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "08:14:23", "RoCreateDate": "02/10/2021", "MileageOut": "45416", "MileageIn": "45414", "CarlineDesc": "ACURA", "Vin": "5FRYD4H41GB050082", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "DALE R VILLEGAS", "AdvNo": "1034", "CustName": "AMY LUDKE", "CustNo": "209649", "RoNo": "783434", "RoCommentInfo": {"RoComment": "LOANER"}, "RecommendedServc": [{"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "67500 MILE SERVICE", "RecSvcOpCode": "17ACZ090"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "60000 MILE SERVICE", "RecSvcOpCode": "17ACZ080"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "30000 MILE SERVICE", "RecSvcOpCode": "17ACZ040"}, {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "75000 MILE SERVICE", "RecSvcOpCode": "17ACZ100"}]}, "Rolabor": {"RoAmts": [{"DlrCost": "99.00", "NTxblAmt": "349.43", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "6", "OpCode": "34ACZ1B4", "OpCodeDesc": "ROTATE TIRES/BAL 4", "TechInfo": [{"ActualHrsWorked": "0.10", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.80", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED ROTATION OF THE TIRES AND BALANCED ALL 4 TIRES", "Cause": ".", "Complaint": "ROTATE THE TIRES AND <PERSON><PERSON><PERSON><PERSON> ALL 4 TIRES"}, "RoAmts": {"DlrCost": "24.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "59.95"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "34ACZ", "OpCodeDesc": "OIL CHANGE-ACURA", "TechInfo": [{"ActualHrsWorked": "2.80", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.30", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": [{"Correction": "CHANGED OIL AND FILTER, PERFORMED A 19 POINT INSPECTION, TOP", "Cause": "MAINTENANCE", "Complaint": "PERFORMED A LUBE, OIL AND FILTER REPLACEMENT"}, {"Correction": "OFF APPROPRIATE FLUIDS, PERFORM A BATTERY TEST."}], "RoAmts": {"DlrCost": "9.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34ACZ6R", "OpCodeDesc": "REAR DIFF SERVICE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.50", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED THE REAR DIFFERENTIAL SERVICE", "Cause": ".", "Complaint": "SERVICE THE REAR DIFFERENTIAL"}, "RoAmts": {"DlrCost": "15.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "62.76"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.10", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "PERFORM MULTI POINT INSPECTION REPORT AT NO CHARGE TO THE"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS", "Complaint": "TO THE CUSTOMER"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "00ACZCOURWASH", "OpCodeDesc": "ACURA COMP WASH", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "THANK YOU FOR BEING OUR CUSTOMER AND FOR CHOOSING US TO", "Cause": "VALUE ADDED CUSTOMER APPRECIATION", "Complaint": "ACURA COMPLIMENTARY WASH TO INCLUDE - TOUCHLESS CAR WASH -"}, {"Correction": "SERVICE YOUR VEHICLE TODAY! IF YOU SHOULD HAVE ANY CONCERNS", "Complaint": "INTERIOR FRONT CAB AREA VACUUM - INTERIOR DASH BOARD DUSTED"}, {"Correction": "WITH YOUR SERVICE WORK TODAY - PLEASE DO NOT HESITATE IN", "Complaint": "TIRE DRESSING APPLIED"}, {"Correction": "REACHING OUT TO ANY OF OUR TEAM MEMBERS!", "Complaint": "NOTE: NOT RES<PERSON><PERSON><PERSON><PERSON> FOR CURRENT WINDSHIELD CHIPS - STARS -"}, {"Correction": "SEE YOU ON YOUR NEXT SERVICE VISIT!", "Complaint": "CRACKS - THAT MAY EXPAND/GROW AFTER COMPLIMENTARY CAR WASH!"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "5", "OpCode": "34ACZFUEL", "OpCodeDesc": "FUEL INJECTION SERV", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "0.00", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}, {"ActualHrsWorked": "0.00", "CustTechRate": "30.00", "TechHrs": "1.70", "TechName": "TRAVIS LEITNER", "TechNo": "1432"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED FUEL INJECTION SERVICE", "Cause": ".", "Complaint": "PERFORM FUEL INJECTION SERVICE"}, "RoAmts": {"DlrCost": "51.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "200.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "6", "OpCode": "34ACZ1B4", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "1", "OpCode": "34ACZ", "RoAmts": [{"DlrCost": "20.42", "NTxblAmt": "0.00", "TxblAmt": "29.29", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "1", "SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Cust", "CustPrice": "0.54"}}, {"CustQtyShip": "1", "SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Cust", "CustPrice": "7.45"}}, {"CustQtyShip": "6", "SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Cust", "CustPrice": "3.55"}}]}, {"JobNo": "2", "OpCode": "34ACZ6R", "RoAmts": [{"DlrCost": "20.26", "NTxblAmt": "0.00", "TxblAmt": "37.45", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "5", "PartNoDesc": "REAR DIFF", "PartNo": "PKDIFF", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "3", "SeqNo": "6", "PartNoDesc": "FLUID, AWD", "PartNo": "AC08200-9007A", "RoAmts": {"DlrCost": "5.87", "PayType": "Cust", "CustPrice": "10.98"}}, {"CustQtyShip": "1", "SeqNo": "7", "PartNoDesc": "WASHER, DRAIN (18", "PartNo": "AC90471-PX4-000", "RoAmts": {"DlrCost": "2.00", "PayType": "Cust", "CustPrice": "3.40"}}, {"CustQtyShip": "1", "SeqNo": "8", "PartNoDesc": "WASHER, DRAIN (20", "PartNo": "AC94109-20000", "RoAmts": {"DlrCost": "0.65", "PayType": "Cust", "CustPrice": "1.11"}}]}, {"JobNo": "3", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "4", "OpCode": "00ACZCOURWASH", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "5", "OpCode": "34ACZFUEL", "RoAmts": [{"DlrCost": "77.55", "NTxblAmt": "0.00", "TxblAmt": "139.98", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": {"CustQtyShip": "1", "SeqNo": "9", "PartNoDesc": "GDI KIT", "PartNo": "GM2012", "RoAmts": {"DlrCost": "77.55", "PayType": "Cust", "CustPrice": "139.98"}}}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "37.21", "RoAmts": [{"NTxblAmt": "42.69", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "617.12"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "6"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "37.21", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "6", "OpCode": "34ACZ1B4"}, {"JobNo": "1", "OpCode": "34ACZ"}, {"JobNo": "2", "OpCode": "34ACZ6R"}, {"JobNo": "3", "OpCode": "00ACZINSP"}, {"JobNo": "4", "OpCode": "00ACZCOURWASH"}, {"JobNo": "5", "OpCode": "34ACZFUEL"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "WHITE", "Carline": "MDX", "ModelDesc": "4DR SH-AWD TECH/PLUS", "VehicleYr": "2016", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"FirstName": "AMY", "LastName": "LUDKE", "NameRecId": "209649", "IBFlag": "I", "Address": {"Zip": "800271069", "State": "CO", "City": "LOUISVILLE", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "1701.75", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "08:32:41", "RoCreateDate": "02/10/2021", "MileageOut": "80477", "MileageIn": "80476", "CarlineDesc": "ACURA", "Vin": "5FRYD4H27FB015561", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "JOSHUA D STRONG", "AdvNo": "1629", "CustName": "HENRY B LESTER", "CustNo": "614265", "RoNo": "783439", "RoCommentInfo": [{"RoComment": "### Created By: <EMAIL>, Created On: 02-08-2021, <PERSON>a"}, {"RoComment": "tus: Scheduled, Transport Type: LOANER"}], "TechRecommends": [{"TechRecommend": "TRANSMISSION FLUSH AND TRA<PERSON><PERSON><PERSON> CASE DRAIN AND FILL $375"}, {"TechRecommend": "BRAKE FLUID FLUSH $199"}, {"TechRecommend": "FUEL INJECTION SERVICE $395"}], "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "102.60", "NTxblAmt": "862.45", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "5.40", "NTxblAmt": "5.40", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "PERFORM MULTI POINT INSPECTION REPORT AT NO CHARGE TO THE"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS", "Complaint": "TO THE CUSTOMER"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "18ACZ070", "OpCodeDesc": "STRUTS", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "4.50", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "165.00", "BillTime": "4.50"}, "CCCStmts": [{"Correction": "TECHNICIAN REPLACED BOTH FRONT STRUTS AND INSULATORS AS", "Complaint": "PLEASE REPLACE FRONT STRUTD"}, {"Correction": "PREVIOUSLY RECOMMENDED."}], "RoAmts": {"DlrCost": "81.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "742.50"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "27ACZALI", "OpCodeDesc": "ALIGNMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "1.20", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED ALIGNMENT", "Cause": ".", "Complaint": "PERFORM ALIGNMENT"}, "RoAmts": {"DlrCost": "21.60", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "119.95"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "34ACZPPM1", "OpCodeDesc": "1ST PPM OIL CHANGE", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "18.00", "TechHrs": "0.30", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED 1ST PRE-PAID MAINTENANCE OIL AND FILTER", "Cause": ".", "Complaint": "PLEASE PERFORM 1ST PRE-PAID MAINTENANCE OIL AND FILTER"}, {"Correction": "CHANGE SERVICE", "Complaint": "CHANGE SERVICE"}], "RoAmts": {"DlrCost": "5.40", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "5.40"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "00ACZPOC", "OpCodeDesc": "PREPAID LOF PURCHASE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}, {"ActualHrsWorked": "0.00", "CustTechRate": "18.00", "TechHrs": "0.00", "TechName": "HECTOR GARCIA", "TechNo": "1635"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": {"Correction": "PACKAGE PURCHASE COMPLETED TODAY", "Cause": "MAINTENANCE", "Complaint": "CUSTOMER PURCHASE A \"4 PREPAID OIL & FILTER SERVICE\" PACKAGE"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "1", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "18ACZ070", "RoAmts": [{"DlrCost": "335.22", "NTxblAmt": "0.00", "TxblAmt": "621.88", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "2", "SeqNo": "5", "PartNoDesc": "RUBBER, FR. SPRIN", "PartNo": "AC51404-T6Z-A01", "RoAmts": {"DlrCost": "4.75", "PayType": "Cust", "CustPrice": "8.98"}}, {"CustQtyShip": "1", "SeqNo": "6", "PartNoDesc": "S/ABS UNIT, R. FR", "PartNo": "AC51611-TZ6-A01", "RoAmts": {"DlrCost": "161.98", "PayType": "Cust", "CustPrice": "299.98"}}, {"CustQtyShip": "1", "SeqNo": "7", "PartNoDesc": "S/ABS UNIT, L. FR", "PartNo": "AC51621-TZ6-A01", "RoAmts": {"DlrCost": "161.98", "PayType": "Cust", "CustPrice": "299.98"}}, {"CustQtyShip": "2", "SeqNo": "8", "PartNoDesc": "NUT (12MM)", "PartNo": "AC90212-TZ5-A01", "RoAmts": {"DlrCost": "0.88", "PayType": "Cust", "CustPrice": "1.98"}}]}, {"JobNo": "3", "OpCode": "27ACZALI", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "4", "OpCode": "34ACZPPM1", "RoAmts": [{"DlrCost": "20.42", "NTxblAmt": "29.99", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}], "PartDetail": [{"SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Intr"}}, {"SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Intr", "CustPrice": "0.59"}}, {"SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Intr", "CustPrice": "8.10"}}, {"SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Intr", "CustPrice": "3.55"}}]}, {"JobNo": "5", "OpCode": "00ACZPOC", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "37.94", "RoAmts": [{"NTxblAmt": "162.42", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "1701.75"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "-35.39", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "37.94", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3", "MiscLineItmInfo": {"CodeAmt": "-30.00", "MiscCodeDesc": "LABOR DISCOUNT", "MiscCode": "LD"}}, {"JobNo": "4", "MiscLineItmInfo": {"CodeAmt": "-35.39", "MiscCodeDesc": "REDEEM PREPAID OIL/FILTER CHANGE", "MiscCode": "POCR"}}, {"JobNo": "5", "MiscLineItmInfo": {"CodeAmt": "149.00", "MiscCodeDesc": "PURCHASE 4 PREPAID LOF", "MiscCode": "POCSALE"}}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "1", "OpCode": "00ACZINSP"}, {"JobNo": "2", "OpCode": "18ACZ070"}, {"JobNo": "3", "OpCode": "27ACZALI"}, {"JobNo": "4", "OpCode": "34ACZPPM1"}, {"JobNo": "5", "OpCode": "00ACZPOC"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "RED", "Carline": "MDX", "ModelDesc": "4DR AWD", "VehicleYr": "2015", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"MidName": "B", "FirstName": "HENRY", "LastName": "LESTER", "NameRecId": "614265", "IBFlag": "I", "Address": {"Zip": "803055238", "State": "CO", "City": "BOULDER", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "353.74", "FinalPostDate": "02/10/2021", "WarrPostDate": "02/10/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "08:57:23", "RoCreateDate": "02/10/2021", "MileageOut": "108919", "MileageIn": "108917", "CarlineDesc": "ACURA", "Vin": "2HNYD2H22CH539562", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "DALE R VILLEGAS", "AdvNo": "1034", "CustName": "AMY TROMBLEY", "CustNo": "216946", "RoNo": "783443", "RoCommentInfo": [{"RoComment": "### Created By: dale.ville<PERSON>@fisherauto.com, Created On: 02-05-202"}, {"RoComment": "1, Status: Scheduled, Transport Type: CUSTOM"}], "TechRecommends": [{"TechRecommend": "TIMING BELT-$1660"}, {"TechRecommend": "OIL PUMP-$790 IF DONE WITH TIMING BELT"}, {"TechRecommend": "REAR BRAKES-$525"}, {"TechRecommend": "SPARK PLUGS-$265"}, {"TechRecommend": "POWER STEERING SVC.-$129.95"}, {"TechRecommend": "TRANS AND TRANSFER CAE SVC.-$349.95"}, {"TechRecommend": "VALVE ADJUST-$395"}], "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "26.80", "NTxblAmt": "56.72", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "00ACZCOURWASH", "OpCodeDesc": "ACURA COMP WASH", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "THANK YOU FOR BEING OUR CUSTOMER AND FOR CHOOSING US TO", "Cause": "VALUE ADDED CUSTOMER APPRECIATION", "Complaint": "ACURA COMPLIMENTARY WASH TO INCLUDE - TOUCHLESS CAR WASH -"}, {"Correction": "SERVICE YOUR VEHICLE TODAY! IF YOU SHOULD HAVE ANY CONCERNS", "Complaint": "INTERIOR FRONT CAB AREA VACUUM - INTERIOR DASH BOARD DUSTED"}, {"Correction": "WITH YOUR SERVICE WORK TODAY - PLEASE DO NOT HESITATE IN", "Complaint": "TIRE DRESSING APPLIED"}, {"Correction": "REACHING OUT TO ANY OF OUR TEAM MEMBERS!", "Complaint": "NOTE: NOT RES<PERSON><PERSON><PERSON><PERSON> FOR CURRENT WINDSHIELD CHIPS - STARS -"}, {"Correction": "SEE YOU ON YOUR NEXT SERVICE VISIT!", "Complaint": "CRACKS - THAT MAY EXPAND/GROW AFTER COMPLIMENTARY CAR WASH!"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "WIPERS ALL 3 AND BATTERY"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "34ACZ", "OpCodeDesc": "OIL CHANGE-ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.30", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": [{"Correction": "CHANGED OIL AND FILTER, PERFORMED A 19 POINT INSPECTION, TOP", "Cause": "MAINTENANCE", "Complaint": "PERFORMED A LUBE, OIL AND FILTER REPLACEMENT"}, {"Correction": "OFF APPROPRIATE FLUIDS, PERFORM A BATTERY TEST."}], "RoAmts": {"DlrCost": "10.05", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "15ACZ030", "OpCodeDesc": "WIPER INSERT REPLACE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "REPLACE ALL WIPER BLADES. AFTER MARKET"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "15ACZ022", "OpCodeDesc": "BATTERY REPLACEMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.00", "TechName": "DAMON MARES", "TechNo": "1633"}, {"ActualHrsWorked": "0.00", "CustTechRate": "33.50", "TechHrs": "0.50", "TechName": "DAMON MARES", "TechNo": "1633"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED INSTALLATION", "Cause": "MAINTENANCE ITEM", "Complaint": "REPLACE BATTERY ASSEMBLY PER ED-18 TESTER RESULTS"}, {"Correction": "CLEAN CORROSION AND SEAL BATTERY ENDS"}], "RoAmts": {"DlrCost": "16.75", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "30.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "1", "OpCode": "00ACZCOURWASH", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "2", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "3", "OpCode": "34ACZ", "RoAmts": [{"DlrCost": "16.20", "NTxblAmt": "0.00", "TxblAmt": "25.74", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "1", "SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Cust", "CustPrice": "0.54"}}, {"CustQtyShip": "1", "SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Cust", "CustPrice": "7.45"}}, {"CustQtyShip": "5", "SeqNo": "4", "PartNoDesc": "5W20 SYN BLD OL", "PartNo": "AC08798-9033AB", "RoAmts": {"DlrCost": "2.30", "PayType": "Cust", "CustPrice": "3.55"}}]}, {"JobNo": "4", "OpCode": "15ACZ030", "RoAmts": [{"DlrCost": "39.97", "NTxblAmt": "0.00", "TxblAmt": "75.94", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "7", "PartNoDesc": "BLADE, WSW (650MM", "PartNo": "AC76620-SHJ-A01", "RoAmts": {"DlrCost": "18.61", "PayType": "Cust", "CustPrice": "34.98"}}, {"CustQtyShip": "1", "SeqNo": "8", "PartNoDesc": "BLADE, WSW (525MM", "PartNo": "AC76630-S3V-A11", "RoAmts": {"DlrCost": "11.43", "PayType": "Cust", "CustPrice": "21.98"}}, {"CustQtyShip": "1", "SeqNo": "9", "PartNoDesc": "BLADE, WSW (300MM", "PartNo": "AC76730-S3N-003", "RoAmts": {"DlrCost": "9.93", "PayType": "Cust", "CustPrice": "18.98"}}]}, {"JobNo": "5", "OpCode": "15ACZ022", "RoAmts": [{"DlrCost": "91.25", "NTxblAmt": "0.00", "TxblAmt": "160.09", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "5", "PartNoDesc": "BAT (GR24F/630CCA", "PartNo": "AC31500-TK8-A2100M", "RoAmts": {"DlrCost": "107.25", "PayType": "Cust", "CustPrice": "176.09"}}, {"CustQtyShip": "-1", "SeqNo": "6", "PartNoDesc": "CORE RETURN", "PartNo": "AC31500-TK8-A2100M", "RoAmts": {"DlrCost": "16.00", "PayType": "Cust", "CustPrice": "16.00"}}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "37.94", "RoAmts": [{"NTxblAmt": "28.42", "TxblAmt": "-15.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "353.74"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "37.94", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5", "MiscLineItmInfo": [{"CodeAmt": "-15.00", "MiscCodeDesc": "LABOR DISCOUNT", "MiscCode": "LD"}, {"CodeAmt": "-15.00", "MiscCodeDesc": "PARTS DISCOUNT", "MiscCode": "PD"}]}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "1", "OpCode": "00ACZCOURWASH"}, {"JobNo": "2", "OpCode": "00ACZINSP"}, {"JobNo": "3", "OpCode": "34ACZ"}, {"JobNo": "4", "OpCode": "15ACZ030"}, {"JobNo": "5", "OpCode": "15ACZ022"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "POL METAL", "Carline": "MDX", "ModelDesc": "4DR AWD", "VehicleYr": "2012", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"FirstName": "AMY", "LastName": "TROMBLEY", "NameRecId": "216946", "IBFlag": "I", "Address": {"Zip": "80026", "State": "CO", "City": "LAFAYETTE", "Type": "P"}, "Email": {"MailTo": ""}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "1756.37", "FinalPostDate": "02/10/2021", "WarrPostDate": "02/10/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "10:09:20", "RoCreateDate": "02/10/2021", "MileageOut": "140774", "MileageIn": "140771", "CarlineDesc": "ACURA", "Vin": "5FRYD4H44FB013655", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "JOSHUA D STRONG", "AdvNo": "1629", "CustName": "PEGGY TOFT", "CustNo": "616442", "RoNo": "783457", "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "201.60", "NTxblAmt": "1121.01", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "4", "OpCode": "15ACZ030", "OpCodeDesc": "WIPER INSERT REPLACE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}, {"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": {"Correction": "REPLACED ALL THREE WIPER INSERTS."}, "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "18ACZ", "OpCodeDesc": "SUSP AND STEERING", "TechInfo": [{"ActualHrsWorked": "3.60", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}, {"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "5.10", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "5.10"}, "CCCStmts": [{"Correction": "REPLACED FRONT STRUTS AND DISASSEMBLE UPPER MOUNT AND", "Cause": "FRONT STRUTS LEAK AND INSULATORS ARE WORN.", "Complaint": "REPLACE FRONT STRUTS"}, {"Correction": "INSTALLED UPPER INSULATORS."}], "RoAmts": {"DlrCost": "163.20", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "1001.06"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34ACZALI", "OpCodeDesc": "ALIGNMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}, {"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "1.20", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED ALIGNMENT", "Cause": ".", "Complaint": "PERFORM ALIGNMENT"}, "RoAmts": {"DlrCost": "38.40", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "119.95"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "00ACZINSP1", "OpCodeDesc": "DECLINED MPI INSP", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}, {"ActualHrsWorked": "0.00", "CustTechRate": "32.00", "TechHrs": "0.00", "TechName": "CHRISTIAN HOWERY", "TechNo": "1231"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "NO MPI PERFORMED TODAY AS PER CUSTOMERS REQUEST", "Cause": "NO WORK DONE", "Complaint": "CUSTO<PERSON>R DECLINED HAVING US PERFORM A MULTIPOINT INSPECTION"}, {"Complaint": "TODAY"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "4", "OpCode": "15ACZ030", "RoAmts": [{"DlrCost": "12.77", "NTxblAmt": "0.00", "TxblAmt": "24.94", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "5", "PartNoDesc": "RUB, BLADE (650MM", "PartNo": "AC76622-SMA-004", "RoAmts": {"DlrCost": "4.49", "PayType": "Cust", "CustPrice": "8.98"}}, {"CustQtyShip": "1", "SeqNo": "6", "PartNoDesc": "RUB, BLADE (500MM", "PartNo": "AC76622-SZT-G01", "RoAmts": {"DlrCost": "4.14", "PayType": "Cust", "CustPrice": "7.98"}}, {"CustQtyShip": "1", "SeqNo": "7", "PartNoDesc": "RUB, BLADE (350MM", "PartNo": "AC76622-TZ5-A01", "RoAmts": {"DlrCost": "4.14", "PayType": "Cust", "CustPrice": "7.98"}}]}, {"JobNo": "1", "OpCode": "18ACZ", "RoAmts": [{"DlrCost": "335.22", "NTxblAmt": "0.00", "TxblAmt": "621.88", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "1", "PartNoDesc": "S/ABS UNIT, R. FR", "PartNo": "AC51611-TZ6-A01", "RoAmts": {"DlrCost": "161.98", "PayType": "Cust", "CustPrice": "299.98"}}, {"CustQtyShip": "1", "SeqNo": "2", "PartNoDesc": "S/ABS UNIT, L. FR", "PartNo": "AC51621-TZ6-A01", "RoAmts": {"DlrCost": "161.98", "PayType": "Cust", "CustPrice": "299.98"}}, {"CustQtyShip": "2", "SeqNo": "3", "PartNoDesc": "NUT (12MM)", "PartNo": "AC90212-TZ5-A01", "RoAmts": {"DlrCost": "0.88", "PayType": "Cust", "CustPrice": "1.98"}}, {"CustQtyShip": "2", "SeqNo": "4", "PartNoDesc": "RUBBER, FR. SPRIN", "PartNo": "AC51404-T6Z-A01", "RoAmts": {"DlrCost": "4.75", "PayType": "Cust", "CustPrice": "8.98"}}]}, {"JobNo": "2", "OpCode": "34ACZALI", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "3", "OpCode": "00ACZINSP1", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "37.94", "RoAmts": [{"NTxblAmt": "-68.68", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "1756.37"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "4"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "0.00", "MiscCodeDesc": "INTERNAL SHOP SUPPLIES", "MiscCode": "SS1"}, {"CodeAmt": "37.94", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}, {"CodeAmt": "-112.10", "MiscCodeDesc": "LABOR DISCOUNT", "MiscCode": "LD"}]}, {"JobNo": "2"}, {"JobNo": "3"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "4", "OpCode": "15ACZ030"}, {"JobNo": "1", "OpCode": "18ACZ"}, {"JobNo": "2", "OpCode": "34ACZALI"}, {"JobNo": "3", "OpCode": "00ACZINSP1"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "BLACK", "Carline": "MDX", "ModelDesc": "4DR AWD TECH PKG", "VehicleYr": "2015", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"FirstName": "PEGGY", "LastName": "TOFT", "NameRecId": "616442", "IBFlag": "I", "Address": {"Zip": "805166549", "State": "CO", "City": "ERIE", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}, {"RoRecord": {"TransactionPayType": "All", "TransactionType": "Close", "Rogen": {"IntrRoTotalAmt": "0.00", "WarrRoTotalAmt": "0.00", "CustRoTotalAmt": "782.47", "FinalPostDate": "02/11/2021", "WarrPostDate": "02/11/2021", "CustInvoiceDate": "02/10/2021", "RoCreateTime": "10:25:03", "RoCreateDate": "02/10/2021", "MileageOut": "61537", "MileageIn": "61535", "CarlineDesc": "ACURA", "Vin": "5J8TB4H39HL011829", "RoStatus": "F", "DeptType": "S", "DispNo": "3", "AdvName": "DALE R VILLEGAS", "AdvNo": "1034", "CustName": "KARI A ANDERSON", "CustNo": "616878", "RoNo": "783460", "RoCommentInfo": {"RoComment": "PICK UP/DELIVERY"}, "TechRecommends": [{"TechRecommend": "BRAKE FLUSH-$165.95"}, {"TechRecommend": "FUEL INJECTOR SVC.-$339.95"}], "RecommendedServc": {"RecSvcPerformFlag": "NO", "RecSvcOpCdDesc": "AMAM BETTER LOF", "RecSvcOpCode": "15CVZ006"}}, "Rolabor": {"RoAmts": [{"DlrCost": "75.60", "NTxblAmt": "260.68", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total"}, {"DlrCost": "8.10", "NTxblAmt": "8.10", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total"}], "OpCodeLaborInfo": [{"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "8", "OpCode": "15ACZ030", "OpCodeDesc": "WIPER INSERT REPLACE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": {"Correction": "TECHNCIAN REPLACED ALL THREE WIPER INSERTS.", "Cause": "STREAKING"}, "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "Y", "JobNo": "9", "OpCode": "15ACZ022", "OpCodeDesc": "BATTERY REPLACEMENT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.50", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00"}, "CCCStmts": {"Correction": "COMPLETED INSTALLATION", "Cause": "MAINTENANCE ITEM", "Complaint": "REPLACE BATTERY ASSEMBLY PER ED-18 TESTER RESULTS"}, "RoAmts": {"DlrCost": "13.50", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "30.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "1", "OpCode": "34ACZ", "OpCodeDesc": "OIL CHANGE-ACURA", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": [{"Correction": "CHANGED OIL AND FILTER, PERFORMED A 19 POINT INSPECTION, TOP", "Cause": "MAINTENANCE", "Complaint": "PERFORMED A LUBE, OIL AND FILTER REPLACEMENT"}, {"Correction": "OFF APPROPRIATE FLUIDS, PERFORM A BATTERY TEST."}], "RoAmts": {"DlrCost": "8.10", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "26.72"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "2", "OpCode": "34ACZ1", "OpCodeDesc": "ROTATE TIRES", "TechInfo": [{"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "IntrTechRate": "27.00", "TechHrs": "0.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "140.00"}, "CCCStmts": [{"Correction": "COMPLETED ACURA TIRE ROTATION", "Cause": ".", "Complaint": "PLEASE PERFORM ACURA TIRE ROTATION"}, {"Complaint": "PURCHASED HERE"}], "RoAmts": {"DlrCost": "8.10", "PayType": "Intr", "AmtType": "Job", "TotalAmt": "8.10"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "3", "OpCode": "34ACZ2", "OpCodeDesc": "REPL ENG/CAB FILTERS", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.30", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED THE REPLACEMENT OF THE ENGINE & CABIN AIR FILTERS", "Cause": ".", "Complaint": "REPLACE THE ENGINE AND CABIN AIR FILTERS"}, "RoAmts": {"DlrCost": "8.10", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "28.05"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "4", "OpCode": "34ACZ3", "OpCodeDesc": "TRANS FLUID EXCHANGE", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "1.20", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED THE AUTOMATIC TRANSMISSION FLUID EXCHANGE", "Cause": ".", "Complaint": "AUTOMATIC TRANSMISSION FLUID EXCHANGE"}, "RoAmts": {"DlrCost": "32.40", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "99.02"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "5", "OpCode": "34ACZ3TC", "OpCodeDesc": "TRANSFER CASE SERV", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.50", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "160.00"}, "CCCStmts": {"Correction": "COMPLETED TRANSFER CASE SERVICE", "Cause": ".", "Complaint": "TRANSFER CASE SERVICE"}, "RoAmts": {"DlrCost": "13.50", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "76.89"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "6", "OpCode": "00ACZINSP", "OpCodeDesc": "MULTI POINT INSPECT", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "COMPLETED MULTIPOINT INSPECTION WITH TIRE PROFILE REPORT", "Cause": "TO INCLUDE TREAD SPEC TIRE WEAR INSPECTION REPORT WITH", "Complaint": "PERFORM MULTI POINT INSPECTION REPORT AT NO CHARGE TO THE"}, {"Cause": "ROTATE AND ALIGNMENT RECOMMENDATIONS", "Complaint": "TO THE CUSTOMER"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}, {"JobStatus": "F", "UpSellFlag": "N", "JobNo": "7", "OpCode": "00ACZCOURWASH", "OpCodeDesc": "ACURA COMP WASH", "TechInfo": [{"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}, {"ActualHrsWorked": "0.00", "CustTechRate": "27.00", "TechHrs": "0.00", "TechName": "SAM BURDEKIN", "TechNo": "1636"}], "BillTimeRateHrs": {"BillRate": "0.00", "BillTime": "0.00"}, "CCCStmts": [{"Correction": "THANK YOU FOR BEING OUR CUSTOMER AND FOR CHOOSING US TO", "Cause": "VALUE ADDED CUSTOMER APPRECIATION", "Complaint": "ACURA COMPLIMENTARY WASH TO INCLUDE - TOUCHLESS CAR WASH -"}, {"Correction": "SERVICE YOUR VEHICLE TODAY! IF YOU SHOULD HAVE ANY CONCERNS", "Complaint": "INTERIOR FRONT CAB AREA VACUUM - INTERIOR DASH BOARD DUSTED"}, {"Correction": "WITH YOUR SERVICE WORK TODAY - PLEASE DO NOT HESITATE IN", "Complaint": "TIRE DRESSING APPLIED"}, {"Correction": "REACHING OUT TO ANY OF OUR TEAM MEMBERS!", "Complaint": "NOTE: NOT RES<PERSON><PERSON><PERSON><PERSON> FOR CURRENT WINDSHIELD CHIPS - STARS -"}, {"Correction": "SEE YOU ON YOUR NEXT SERVICE VISIT!", "Complaint": "CRACKS - THAT MAY EXPAND/GROW AFTER COMPLIMENTARY CAR WASH!"}], "RoAmts": {"DlrCost": "0.00", "PayType": "Cust", "AmtType": "Job", "TotalAmt": "0.00"}}]}, "Ropart": {"PartInfoByJob": [{"JobNo": "8", "OpCode": "15ACZ030", "RoAmts": [{"DlrCost": "12.77", "NTxblAmt": "0.00", "TxblAmt": "24.94", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "15", "PartNoDesc": "RUB, BLADE (650MM", "PartNo": "AC76622-STK-A02", "RoAmts": {"DlrCost": "4.49", "PayType": "Cust", "CustPrice": "8.98"}}, {"CustQtyShip": "1", "SeqNo": "16", "PartNoDesc": "RUB, BLADE (400MM", "PartNo": "AC76632-STK-A02", "RoAmts": {"DlrCost": "4.14", "PayType": "Cust", "CustPrice": "7.98"}}, {"CustQtyShip": "1", "SeqNo": "17", "PartNoDesc": "RUB, BLADE (350MM", "PartNo": "AC76632-S2K-004", "RoAmts": {"DlrCost": "4.14", "PayType": "Cust", "CustPrice": "7.98"}}]}, {"JobNo": "9", "OpCode": "15ACZ022", "RoAmts": [{"DlrCost": "91.25", "NTxblAmt": "0.00", "TxblAmt": "152.98", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "13", "PartNoDesc": "BAT (GR24F/630CCA", "PartNo": "AC31500-TK8-A2100M", "RoAmts": {"DlrCost": "107.25", "PayType": "Cust", "CustPrice": "168.98"}}, {"CustQtyShip": "-1", "SeqNo": "14", "PartNoDesc": "CORE RETURN", "PartNo": "AC31500-TK8-A2100M", "RoAmts": {"DlrCost": "16.00", "PayType": "Cust", "CustPrice": "16.00"}}]}, {"JobNo": "1", "OpCode": "34ACZ", "RoAmts": [{"DlrCost": "17.80", "NTxblAmt": "0.00", "TxblAmt": "25.74", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "1", "PartNoDesc": "OIL CHANGE", "PartNo": "PK1", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "1", "SeqNo": "2", "PartNoDesc": "WASHER, DRAIN (14", "PartNo": "AC94109-14000", "RoAmts": {"DlrCost": "0.32", "PayType": "Cust", "CustPrice": "0.54"}}, {"CustQtyShip": "1", "SeqNo": "3", "PartNoDesc": "FILTER, OIL", "PartNo": "AC15400-PLM-A02", "RoAmts": {"DlrCost": "4.38", "PayType": "Cust", "CustPrice": "7.45"}}, {"CustQtyShip": "5", "SeqNo": "4", "PartNoDesc": "0W-20 SYNTH OIL", "PartNo": "AC08798-9073AB", "RoAmts": {"DlrCost": "2.62", "PayType": "Cust", "CustPrice": "3.55"}}]}, {"JobNo": "2", "OpCode": "34ACZ1", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}]}, {"JobNo": "3", "OpCode": "34ACZ2", "RoAmts": [{"DlrCost": "28.76", "NTxblAmt": "0.00", "TxblAmt": "61.90", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "11", "PartNoDesc": "ELEMENT, AIR CLEA", "PartNo": "AC17220-R8A-A01", "RoAmts": {"DlrCost": "15.77", "PayType": "Cust", "CustPrice": "29.95"}}, {"CustQtyShip": "1", "SeqNo": "12", "PartNoDesc": "ELEMENT", "PartNo": "AC80292-SDA-407", "RoAmts": {"DlrCost": "12.99", "PayType": "Cust", "CustPrice": "31.95"}}]}, {"JobNo": "4", "OpCode": "34ACZ3", "RoAmts": [{"DlrCost": "82.28", "NTxblAmt": "0.00", "TxblAmt": "150.74", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "5", "PartNoDesc": "ATF EXCHANGE", "PartNo": "PKEXCHANGE", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "1", "SeqNo": "6", "PartNoDesc": "ATC PLUS TRANS FL", "PartNo": "GM3020", "RoAmts": {"DlrCost": "20.60", "PayType": "Cust", "CustPrice": "36.98"}}, {"CustQtyShip": "12", "SeqNo": "7", "PartNoDesc": "ACURA ATF DW1", "PartNo": "AC08200-9009AB", "RoAmts": {"DlrCost": "5.14", "PayType": "Cust", "CustPrice": "9.48"}}]}, {"JobNo": "5", "OpCode": "34ACZ3TC", "RoAmts": [{"DlrCost": "12.77", "NTxblAmt": "0.00", "TxblAmt": "23.20", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}], "PartDetail": [{"CustQtyShip": "1", "SeqNo": "8", "PartNoDesc": "T CASE", "PartNo": "PKTCASE", "RoAmts": {"DlrCost": "0.00", "PayType": "Cust"}}, {"CustQtyShip": "2", "SeqNo": "9", "PartNoDesc": "WASHER, DRAIN (20", "PartNo": "AC94109-20000", "RoAmts": {"DlrCost": "0.65", "PayType": "Cust", "CustPrice": "1.11"}}, {"CustQtyShip": "1", "SeqNo": "10", "PartNoDesc": "FLUID (HGO-1)", "PartNo": "AC08200-9014A", "RoAmts": {"DlrCost": "11.47", "PayType": "Cust", "CustPrice": "20.98"}}]}, {"JobNo": "6", "OpCode": "00ACZINSP", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}, {"JobNo": "7", "OpCode": "00ACZCOURWASH", "RoAmts": [{"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Job"}, {"DlrCost": "0.00", "NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Job"}]}]}, "Rogog": {"AllGogTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Gog"}, {"AllTotAmtType": "Paint"}, {"AllTotAmtType": "ShopSuppl"}, {"AllTotAmtType": "Freight"}]}, "Romisc": {"IntrLbrTot": "0.00", "WarrLbrTot": "0.00", "CustLbrTot": "0.00", "IntrPartsTot": "0.00", "WarrPartsTot": "0.00", "CustPartsTot": "37.94", "RoAmts": [{"NTxblAmt": "43.42", "TxblAmt": "0.00", "PayType": "Cust", "AmtType": "Total", "TotalAmt": "782.47"}, {"NTxblAmt": "0.00", "TxblAmt": "0.00", "PayType": "<PERSON>r", "AmtType": "Total", "TotalAmt": "0.00"}, {"NTxblAmt": "-8.10", "TxblAmt": "0.00", "PayType": "Intr", "AmtType": "Total", "TotalAmt": "0.00"}], "MiscOpCodeInfo": [{"JobNo": "8"}, {"JobNo": "9"}, {"JobNo": "1", "MiscLineItmInfo": [{"CodeAmt": "3.00", "MiscCodeDesc": "FISHER COVID SANITIZING", "MiscCode": "FCS"}, {"CodeAmt": "2.48", "MiscCodeDesc": "ELECTRONIC DOCUMENT STORAGE", "MiscCode": "DS"}, {"CodeAmt": "37.94", "MiscCodeDesc": "WASTE DISPOSAL", "MiscCode": "SS"}, {"CodeAmt": "-8.10", "MiscCodeDesc": "LABOR DISCOUNT", "MiscCode": "LD"}]}, {"JobNo": "2"}, {"JobNo": "3"}, {"JobNo": "4"}, {"JobNo": "5"}, {"JobNo": "6"}, {"JobNo": "7"}]}, "Rosub": {"AllSubTotalAmts": [{"AllTotAmtType": "All"}, {"AllTotAmtType": "Labor"}, {"AllTotAmtType": "Parts"}], "SubInfoByJob": [{"JobNo": "8", "OpCode": "15ACZ030"}, {"JobNo": "9", "OpCode": "15ACZ022"}, {"JobNo": "1", "OpCode": "34ACZ"}, {"JobNo": "2", "OpCode": "34ACZ1"}, {"JobNo": "3", "OpCode": "34ACZ2"}, {"JobNo": "4", "OpCode": "34ACZ3"}, {"JobNo": "5", "OpCode": "34ACZ3TC"}, {"JobNo": "6", "OpCode": "00ACZINSP"}, {"JobNo": "7", "OpCode": "00ACZCOURWASH"}]}}, "ServVehicle": {"Vehicle": {"ExtClrDesc": "SILVER", "Carline": "RDX", "ModelDesc": "RDX", "VehicleYr": "2017", "VehicleMake": "ACURA", "MakeName": "ACURA", "VehicleDetail": {}}, "VehicleServInfo": {}}, "CustRecord": {"ContactInfo": {"MidName": "A", "FirstName": "KARI", "LastName": "ANDERSON", "NameRecId": "616878", "IBFlag": "I", "Address": {"Zip": "800276101", "State": "CO", "City": "SUPERIOR", "Type": "P"}, "Email": {"MailTo": "<EMAIL>"}}}}]}}