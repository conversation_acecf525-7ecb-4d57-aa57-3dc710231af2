BEGIN;
\pset pager off

ALTER TABLE :IMPORT_SCHEMA.etl_head_detail ALTER COLUMN "RoCreateDate" TYPE date USING ("RoCreateDate"::date);

-- \ir './finalize-scripts/create-linkages.psql'


UPDATE :IMPORT_SCHEMA.etl_head_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_labor_details
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_job_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_customer_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_estimate_details
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_other_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_sublet_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_json
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';


UPDATE :IMPORT_SCHEMA.etl_recommended_service_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_tech_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

UPDATE :IMPORT_SCHEMA.etl_warrantyclaim_detail
SET "roNumber" = regexp_replace("roNumber", 'C$', '')
WHERE "roNumber" ~ '^7.*|^8.*';

DELETE from :IMPORT_SCHEMA.etl_head_detail 
where "roNumber"~'[A-Z]';


UPDATE :IMPORT_SCHEMA.etl_job_detail
   SET "OpCodeDesc" = 'OP: ' || "OpCode"
 WHERE NULLIF("OpCodeDesc", '') IS NULL;

UPDATE :IMPORT_SCHEMA.etl_parts_detail
   SET "PartNoDesc" = 'PN: ' || "PartNo"
 WHERE NULLIF("PartNoDesc", '') IS NULL;

-- UPDATE :IMPORT_SCHEMA.etl_job_detail SET "BillTime" =  ROUND(("TotalAmt"::numeric/"BillRate"::numeric),2)
-- WHERE "BillTime" IS NULL AND ("TotalAmt" IS NOT NULL OR "BillRate" IS NOT NULL) AND "BillRate"::numeric != 0;

UPDATE :IMPORT_SCHEMA.etl_job_detail SET "BillTime" = '0.00'
WHERE "BillTime" IS NULL AND "BillRate"::numeric = 0;

UPDATE :IMPORT_SCHEMA.etl_job_detail SET "BillTime" = '0.00'
WHERE "BillTime" IS NULL AND "TotalAmt"::numeric != 0;

\C 'Removed Labor Items with No Hours or Sale Amount'
WITH dels AS (
DELETE FROM :IMPORT_SCHEMA.etl_job_detail
 WHERE "TotalAmt"::numeric = 0 AND "BillTime"::numeric = 0
      AND "roNumber" NOT IN (SELECT "roNumber" FROM :IMPORT_SCHEMA.etl_other_detail 
      UNION SELECT "roNumber" FROM :IMPORT_SCHEMA.etl_misc_detail)
 RETURNING *
) 
SELECT count(*) AS zero_zero_labor FROM dels;


\C 'Removed Parts Items with No Cost or Sale Amount'
WITH delsparts AS (
DELETE FROM :IMPORT_SCHEMA.etl_parts_detail
 WHERE "DlrCost"::numeric = 0 AND "CustPrice"::numeric = 0
 RETURNING *
) 
SELECT count(*) AS zero_zero_parts FROM delsparts;


\ir './core-logic/core-table-preparation.psql'

\ir './kit-logic/kit-table-preparation.psql'
\ir './kit-logic/assign-kit-pseudo-jobs.psql'
\ir './kit-logic/allocate-sale-to-kit-parts.psql'




\ir './finalize-scripts/fix-part-numbers-and-descriptions.psql'


\C 'ROs older than 6 months'
SELECT count(*) FROM :IMPORT_SCHEMA.etl_head_detail WHERE "RoCreateDate"::date <  CURRENT_DATE - INTERVAL '6 months';

\C 
SELECT count(DISTINCT ("roNumber", "JobNo")) AS "Jobless parts"
  FROM :IMPORT_SCHEMA.etl_parts_detail
 WHERE NOT EXISTS (SELECT 1
                     FROM :IMPORT_SCHEMA.etl_job_detail
                    WHERE :IMPORT_SCHEMA.etl_job_detail."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber"
                          AND :IMPORT_SCHEMA.etl_job_detail."JobNo"::integer = :IMPORT_SCHEMA.etl_parts_detail."JobNo"::integer);

\C 
SELECT  assert_none(count(DISTINCT ("roNumber", "JobNo")),
                        'Jobless Parts Without Paytype') AS "Jobless Parts Without Paytype"
  FROM :IMPORT_SCHEMA.etl_parts_detail
 WHERE NOT EXISTS (SELECT 1
                     FROM :IMPORT_SCHEMA.etl_job_detail
                    WHERE :IMPORT_SCHEMA.etl_job_detail."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber"
                          AND :IMPORT_SCHEMA.etl_job_detail."JobNo"::integer = :IMPORT_SCHEMA.etl_parts_detail."JobNo"::integer)
       and :IMPORT_SCHEMA.etl_parts_detail."PayType" IS NULL;

WITH splitparts AS (
	SELECT "roNumber", "JobNo","SeqNo", "PartNo",
	    STRING_AGG("PayType",', ' ORDER BY "PayType"),
	    SUM ("DlrCost"::numeric) AS new_cost,
	    SUM ("CustPrice"::numeric)  FILTER (WHERE "PayType"  = 'Cust') AS new_price,
	    'X' AS paytype
	 FROM  :IMPORT_SCHEMA.etl_parts_detail
	GROUP BY  "roNumber", "JobNo","SeqNo", "PartNo" HAVING  count (*) = 2 AND STRING_AGG ("PayType", ',' ORDER BY "PayType") IN ('Cust,Warr', 'Cust,Intr')
) , updating_table AS (
    UPDATE :IMPORT_SCHEMA.etl_parts_detail 
    SET "DlrCost"   = new_cost, 
        "CustPrice" = new_price,
        "PayType"   = paytype
    FROM splitparts sp 
    WHERE sp."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber" 
      AND sp."JobNo"    = :IMPORT_SCHEMA.etl_parts_detail."JobNo" 
      AND sp."SeqNo"    = :IMPORT_SCHEMA.etl_parts_detail."SeqNo"  
      AND sp."PartNo"   = :IMPORT_SCHEMA.etl_parts_detail."PartNo"
      AND "PayType"     = 'Cust'    
RETURNING *
)
DELETE FROM :IMPORT_SCHEMA.etl_parts_detail pd
USING splitparts sp
WHERE   sp."roNumber" = pd."roNumber" 
    AND sp."JobNo"      = pd."JobNo" 
    AND sp."SeqNo"      = pd."SeqNo"  
    AND sp."PartNo"     = pd."PartNo"
    AND pd."PayType"    ~ '^[WI]';

UPDATE :IMPORT_SCHEMA.etl_job_detail SET "PayType" = 'X' WHERE "PayType" ~ 'Cust' AND "PayType" ~ ',';

CREATE TABLE IF NOT EXISTS :IMPORT_SCHEMA.etl_uuid_detail
    (
        unique_id       text NOT NULL  
    );

-- \ir './finalize-scripts/transfer-data-to-client-schema.psql'

\ir './finalize-scripts/transfer-data-to-job-client-schema.psql'

COMMIT;
