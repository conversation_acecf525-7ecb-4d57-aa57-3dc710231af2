/*Generate Data for DU-Jobs loading*/

/*Single Store */
INSERT INTO :SRC_SCHEMA.client_invoicedetails (invoicenumber, open_date, close_date,
                                   vehicle_year, vehicle_make, vehicle_model, vin,
                                   customer_id,customer_name, advisor_id, store_code, is_current_store,
                                   mileage, deptname, comment)
    WITH multiStore AS (
        SELECT dms_store_id FROM :IMPORT_SCHEMA.etl_invoicemaster GROUP BY dms_store_id
    )
    SELECT
        h."roNumber",
        "RoCreateDate" :: date,
        nullif("FinalPostDate", '') :: date AS closeddate,
        "VehicleYr",
        UPPER(COALESCE("VehicleMake", 'MISSING')),
        UPPER("ModelDesc"),
        COALESCE("Vin", 'Missing'),
        COALESCE("CustNo", 'N/A'),
        "CustName",
        LEFT("AdvNo", 10),
        'N/A',
        TRUE,
        "MileageIn",
        COALESCE("DeptType",'MISSING'),
        COALESCE(CASE WHEN json_typeof("RoComment"::json) = 'object'
           THEN  "RoComment"::json->>'RoComment'
       	    WHEN json_typeof("RoComment"::json) = 'array'
           THEN (
           	SELECT string_agg(t."RoComment", ' ') FROM json_to_recordset("RoComment"::json) AS t("RoComment" text)
           )
       	END, '')
        ||' '||
        COALESCE(
        	CASE WHEN json_typeof("TechRecommend"::json) = 'object'
           	THEN  "TechRecommend"::json->>'TechRecommend'
       		WHEN json_typeof("TechRecommend"::json) = 'array'
           	THEN (
           		SELECT string_agg(t."TechRecommend", ' ') FROM json_to_recordset("TechRecommend"::json) AS t("TechRecommend" text)
           	)
       	END, ''
        ) AS comment
    FROM :IMPORT_SCHEMA.etl_head_detail h
    WHERE (SELECT count(dms_store_id) FROM multiStore) <= 0
    ORDER BY 1;


-- /*Multi Store */
INSERT INTO :SRC_SCHEMA.client_invoicedetails (invoicenumber, open_date, close_date,
                                   vehicle_year, vehicle_make, vehicle_model, vin,
                                   customer_id,customer_name, advisor_id, store_code, is_current_store,
                                   mileage, deptname, comment)
    WITH multiStore AS (
        SELECT dms_store_id FROM :IMPORT_SCHEMA.etl_invoicemaster GROUP BY dms_store_id
    )
    SELECT
        h."roNumber",
        "RoCreateDate" :: date,
        CASE WHEN nullif(closedate,'') IS NULL 
		    THEN  NULL
		ELSE
		    nullif("FinalPostDate", '') :: date
		END AS closeddate,
        "VehicleYr",
        COALESCE("VehicleMake", 'MISSING'),
        "ModelDesc",
        COALESCE("Vin", 'Missing'),
        COALESCE("CustNo", 'N/A'),
        "CustName",
        LEFT("AdvNo", 10),
        'N/A',
        TRUE,
        "MileageIn",
        COALESCE("DeptType",'MISSING'),
        COALESCE(CASE WHEN json_typeof("RoComment"::json) = 'object'
           THEN  "RoComment"::json->>'RoComment'
       	    WHEN json_typeof("RoComment"::json) = 'array'
           THEN (
           	SELECT string_agg(t."RoComment", ' ') FROM json_to_recordset("RoComment"::json) AS t("RoComment" text)
           )
       	END, '')
        ||' '||
        COALESCE(CASE WHEN json_typeof("TechRecommend"::json) = 'object'
           THEN  "TechRecommend"::json->>'TechRecommend'
       	WHEN json_typeof("TechRecommend"::json) = 'array'
           THEN (
           	SELECT string_agg(t."TechRecommend", ' ') FROM json_to_recordset("TechRecommend"::json) AS t("TechRecommend" text)
           )	
           END, ''
        ) AS comment
    FROM :IMPORT_SCHEMA.etl_head_detail h 
    JOIN (select invoicenumber, string_agg(closedate,'') as closedate from :IMPORT_SCHEMA.etl_invoicemaster group by invoicenumber) i ON "roNumber" = invoicenumber
    WHERE (SELECT count(dms_store_id) FROM multiStore) > 0 --AND (closedate IS NOT NULL AND "FinalPostDate" IS NOT NULL)
    ORDER BY 1;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation)
    
    SELECT
        "roNumber",
        'AMOUNT',
        CASE WHEN lower("AutoCalc") = 'y' THEN 'RO'
    	ELSE 'JOB'
    	END AS disc_level,
    	CASE WHEN lower("AutoCalc") = 'y' THEN NULL
    	ELSE "JobNo"
    	END AS jobno,
        "PayType",
        CASE WHEN (lower("MiscCodeDesc") ~* 'labor' OR lower("MiscCodeDesc") ~* 'service') AND lower("MiscCodeDesc") ~* 'parts' THEN
        NULL
		WHEN (lower("MiscCodeDesc") ~* 'labor' OR lower("MiscCodeDesc") ~* 'service') THEN 
      		ABS("CodeAmt" ::numeric)
    	ELSE  NULL
    	END AS labor_disc,
		CASE WHEN lower("MiscCodeDesc") ~* 'labor' AND lower("MiscCodeDesc") ~* 'parts' THEN
        NULL
        WHEN lower("MiscCodeDesc") ~* 'parts' THEN 
      		ABS("CodeAmt" ::numeric)
    	ELSE  NULL
    	END AS part_disc, 
        ABS("CodeAmt" :: numeric),
        "MiscCodeDesc",
        coalesce((upper(coalesce("MiscCodeDesc", '')) !~* 'CHRYSLER COMPETITIVE MAKE ESP|ESP WARRANTY DEDUCTIBLE|FORD COMPETITIVE MAKE ESP|SHOP SUPPLIES CREDIT|WARRANTY DEDUCTIBLE|EXTENDED WARRANTY|INTERNAL SS CREDIT|JMA WARRANTY|COST OF SALE - WARR|
            WARRANTY|WAVE & SVC CONTRACTS|SHOP SUPPLY CREDIT|COST OF SALE - CP|COST OF SALE - WARR - PARTS|SUNBIT FINANCE|EXT WARR|EXTENDED WARRANTY-OTHER|SUNBIT|FACTORY WTY PONT DED (-)|GMPP WARRANTY CREDIT (-)|WARRANTY DEDUCTIBLE DR|
            WARRANTY|SHOP SUPPLIES FROM PARTS|GM WARRANTY DEDUCTIBLE|MISC WARRANTY CO|COMPLIMENTARY PSI|GM WARRANTY DEDUCTIBLE|PORTFOLIO MAINTENANCE|CHRYSLER SVC/EXT CONTRACTS|WARRANTY CREDIT|MISC EXT WARRANTY|MISC SVC/EXT CONTRACTS|
            WARRANTY|COMPLIMENTARY STATE INSPECTION|PORTFOLIO WARRANTY|PORTFOLIO EXT WARRANTY-NON TAX|EXT WARRANTY MISC-NON TAX|PORTFOLIO SVC/EXT CONTRACTS|OTHER SERVICE CONTRACT TAXABLE|PORTFOLIO CLAIM|PORTFOLIO EXT WARRANTY|
            WARRANTY|PORTFOLIO|FIRST EXTENDED WARRANTY|JMA|FREE STATE INSPECTION|OTHER SERVICE CONTRACT|PORTFOLIO SERVICE CONT|PORTFOLIO WARRANTY|EXT WARRANTY-MISC TAXABLE|REMOVE DEDUCTIBLE FROM WARRANTY|REMOVE SHOP SUPPLIES|WARRANTY|EXT CONTRACT'  
            ),false)
    FROM :IMPORT_SCHEMA.etl_misc_detail
    WHERE "CodeAmt" :: numeric < 0 AND "MiscCode" IS NOT NULL;

-- INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
--                                      ro_line, labor_discount, part_discount, total_discount, summary, need_estimation)
    
--     SELECT
--         "roNumber",
--          'AMOUNT',
--          'RO',
--           NULL,
--         '0',
--         '0',
--         ABS("NTxblAmt" :: numeric),
--         "Type",
--         TRUE
--     FROM :IMPORT_SCHEMA.etl_other_detail
--     WHERE
--         "NTxblAmt" :: numeric < 0;

INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
                                 department, base_paytype, base_paytype_suffix,
                                 opcode, opdescription,
                                 unit_hours, unit_sale, extended_sale)
    WITH parts_paytype AS (
     SELECT "roNumber" AS parts_rono,"PayType" AS parts_pay,row_number() OVER(PARTITION BY "roNumber", "JobNo") AS row_count,"JobNo" as jobid FROM :IMPORT_SCHEMA.etl_parts_detail
    )
    SELECT
        row_number()
        OVER (
            ORDER BY "roNumber", "JobNo" ),
        jd."roNumber",
        "JobNo"                                    AS jobid,
        "DeptType"                                 AS department,
        COALESCE(COALESCE(string_agg("PayType", ','),string_agg("parts_pay", ',')), 'NA')      AS base_paytype,
        string_agg(CASE WHEN "OpCode" IS NULL
            THEN 'O'
                   ELSE NULL END,
                   ',')                            AS base_paytype_suffix,
        string_agg(upper(COALESCE("OpCode", 'NO-OP')),
                   ',')                            AS opcode,
        CASE WHEN LENGTH(string_agg("OpCodeDesc",',')) < '38'
                THEN string_agg("OpCodeDesc",',')          
        ELSE            
        string_agg(COALESCE(regexp_replace(regexp_replace(regexp_replace(
                                                                        regexp_replace("OpCodeDesc", 'CUSTOMER STATES.',
                                                                                       'C/S ', 'gi'), 'CUSTOMER REQUESTS.',
                                                                        'C/R ', 'gi'), 'CUSTOMER STATE.', '', 'gi'),
                                                     'CUSTOMER REQUEST.', '', 'gi'), 'OP: ' || "OpCode"),
                   ',')
        END                            AS opdescription,
        sum("BillTime" :: numeric)                 AS unit_hours,
        coalesce(sum("TotalAmt" :: numeric), 0)    AS unit_sale,
        coalesce(sum("TotalAmt" :: numeric), 0)    AS extended_sale
    FROM :IMPORT_SCHEMA.etl_job_detail jd LEFT JOIN ( SELECT * FROM parts_paytype WHERE row_count = 1 ) pp ON pp."parts_rono" = jd."roNumber" AND pp."jobid" = jd."JobNo"
    JOIN :IMPORT_SCHEMA.etl_head_detail hd USING ("roNumber")
    JOIN :SRC_SCHEMA.client_invoicedetails id ON hd."roNumber" = id.invoicenumber
    GROUP BY jd."roNumber", "JobNo", "DeptType";

INSERT INTO :SRC_SCHEMA.client_partsdetails (pdid, invoicenumber, jobid,
                                 department, base_paytype,
                                 base_partsource, partnumber, partdescription,
                                 unitcost, unitsale, unitcount,
                                 extendedcost, extendedsale,
                                 part_line, corecharge_sale, corecharge_cost,
                                 kit_name)
    SELECT
        row_number()
        OVER (
            ORDER BY pd."roNumber", pd."JobNo" ),
        pd."roNumber"                                                                                 AS invoicenumber,
        pd."JobNo"                                                                                    AS jobid,
        ro."DeptType"                                                                                            AS department,
        COALESCE(COALESCE(pd."PayType",jd."PayType"), '')                    AS base_paytype,
        NULL                                                                                          AS base_partsource,
        COALESCE(upper(pd."PartNo"), 'NPN-' || pd."roNumber" || '-' || pd."JobNo")                    AS partnumber,
        COALESCE(pd."PartNoDesc", 'P/N: ' || COALESCE(pd."PartNo", 'NPN'))                            AS partdescription,
        pd."DlrCost" :: numeric                                                                       AS unitcost,
        pd."CustPrice" :: numeric                                                                     AS unitsale,
        ceil(coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: integer    AS unitcount,
        -- avoids rounding fractions < 1 to zero
        (pd."DlrCost" :: numeric *
         coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: numeric(20, 2) AS extendedcost,
        (pd."CustPrice" :: numeric *
         coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: numeric(20, 2) AS extendedsale,
        "SeqNo"::numeric                                                                              AS part_line,
        NULL                                                                                          AS corecharge_sale,
        NULL                                                                                         AS corecharge_cost,
        CASE WHEN (pd."PartKit" IS NOT NULL AND pd."PartKit" = pd."PartNo")
            THEN 'KIT-' || pd."PartKit"
        WHEN (pd."PartKit" IS NOT NULL AND pd."DlrCost" IS NOT NULL)
            THEN pd."PartKit"
        WHEN (pd."PartKit" IS NOT NULL AND pd."DlrCost" IS NULL)
            THEN 'KIT-' || pd."PartKit"
        ELSE NULL
        END                                                                                           AS kit_name
    FROM (SELECT
              *,
              (SELECT regexp_matches(upper("PartNo"), '^[^A-Z0-9]*([A-Z0-9])')) AS source_letter_arr
          FROM :IMPORT_SCHEMA.etl_parts_detail
         ) AS pd
    JOIN :IMPORT_SCHEMA.etl_head_detail AS ro ON (ro."roNumber" = pd."roNumber")
    JOIN :SRC_SCHEMA.client_invoicedetails id ON ro."roNumber" = id.invoicenumber
    LEFT JOIN :IMPORT_SCHEMA.etl_job_detail AS jd ON (pd."roNumber" = jd."roNumber" AND pd."JobNo" = jd."JobNo")
    WHERE (pd."CustPrice" !='0' OR ("PartKit" IS NOT NULL))
    ORDER BY pd."roNumber",pd."JobNo"::numeric, "SeqNo"::numeric;

-- WITH core_data AS (SELECT
--                        p1."roNumber",
--                        p1."JobNo",
--                        p2."DlrCost" :: numeric *
--                        coalesce(p2."CustQtyShip", p2."WarrQtyShip", p2."IntrQtyShip") :: numeric AS corecost,
--                        p2."CustPrice" :: numeric *
--                        coalesce(p2."CustQtyShip", p2."WarrQtyShip", p2."IntrQtyShip") :: numeric AS coresale,
--                        p1.corereturnitemindex,
--                        p1."PartNo"
--                    FROM :IMPORT_SCHEMA.etl_parts_detail p1
--                        JOIN :IMPORT_SCHEMA.etl_parts_detail p2 ON p1."roNumber" = p2."roNumber" AND
--                                                                             p1."JobNo" = p2."JobNo" AND p2.corerelated
--                                                                             AND
--                                                                             p2.is_core_return
--                    WHERE p1.corerelated AND NOT p1.is_core_return),
--     update_core AS ( UPDATE
--                         :SRC_SCHEMA.client_partsdetails
--                     SET
--                         corereturn_sale = coresale,
--                         corereturn_cost = corecost
--                     FROM
--                         core_data cd
--                     WHERE cd."roNumber" = :SRC_SCHEMA.client_partsdetails.invoicenumber AND jobid = "JobNo" AND "PartNo" = partnumber
--                     RETURNING *
--     )
-- DELETE FROM :SRC_SCHEMA.client_partsdetails
-- USING core_data
-- WHERE invoicenumber = "roNumber" AND "JobNo" = jobid AND "PartNo" = partnumber AND
--       unitcount < 0;

-- UPDATE :SRC_SCHEMA.client_partsdetails
-- SET corereturn_cost = unitcost * unitcount,
--     corereturn_sale = unitsale * unitcount,
--     corecharge_sale = NULL,
--     corecharge_cost = NULL
-- WHERE unitcount < 0 and (partdescription = 'CORE RETURN'
--                           OR
--                           (partdescription~* 'core' AND unitcost = unitsale));

WITH etl_parts_detail AS (
    SELECT DISTINCT
        "roNumber"              AS ro_number,   
        "JobNo"                 AS line_number,  
        "PartNo"                AS part_number,  
        "CustQtyShip"::int      AS qty,    
        "DlrCost"::numeric      AS cost, 
        "CustPrice"::numeric    AS net_price
    FROM :IMPORT_SCHEMA.etl_parts_detail
    WHERE NULLIF("PartNo",'') IS NOT NULL
          AND "CustQtyShip"::int != 0 
          AND "DlrCost"::numeric != 0
)
, parts_list AS(
    SELECT 
        ro_number,   
        line_number,  
        part_number,  
        qty,    
        cost, 
        net_price,
        CASE 
        WHEN part_number = lead(part_number, 1 ) over (wnd) 
            THEN TRUE 
        WHEN part_number = lag(part_number, 1) over (wnd)
            THEN TRUE 
        ELSE FALSE END                             AS have_2,
        bool_or(qty::numeric < 0)over (wnd)        AS have_negative,
        bool_or(qty::numeric > 0)over (wnd)        AS have_positive
    FROM etl_parts_detail
    WINDOW wnd AS (partition by ro_number, line_number, part_number 
                       ORDER BY ro_number, line_number, part_number)
    ORDER BY  ro_number, line_number, part_number
)
, parts_classification AS(
  SELECT
        ro_number,  
        line_number,   
        part_number, 
        qty,    
        cost, 
        net_price, 
        CASE 
        WHEN qty < 0 
            THEN 'Core Return'
        ELSE 'Part Number' END AS classification
    FROM parts_list
    WHERE have_2 
          AND have_negative
          AND have_positive
    WINDOW wnd AS (PARTITION BY ro_number, line_number, part_number 
                   ORDER BY qty < 0 DESC)
)               
, update_client_partsdetails AS(
    UPDATE :SRC_SCHEMA.client_partsdetails cp
    SET extendedsale    = extendedsale - (cr.qty * cr.net_price * -1),
        extendedcost    = extendedcost - (cr.qty * cr.cost * -1),
        unitsale        = unitsale - (cr.net_price),
        unitcost        = unitcost - (cr.cost),
        corecharge_sale = (cr.qty * cr.net_price * -1),
        corecharge_cost = (cr.qty * cr.cost * -1),
        corereturn_sale = (cr.qty * cr.net_price),
        corereturn_cost = (cr.qty * cr.cost)
    FROM parts_classification pn
         LEFT JOIN parts_classification cr 
             ON pn.ro_number = cr.ro_number
                 AND pn.line_number = cr.line_number
                 AND pn.part_number = cr.part_number
                 AND cr.classification = 'Core Return'
    WHERE pn.classification = 'Part Number'
          AND pn.ro_number = cp.invoicenumber 
          AND pn.line_number = cp.jobid
          AND pn.part_number = cp.partnumber
          AND pn.cost = cp.unitcost
          AND pn.qty = cp.unitcount
)
DELETE FROM :SRC_SCHEMA.client_partsdetails cp
USING parts_classification pc
WHERE pc.classification = 'Core Return'
      AND pc.ro_number = cp.invoicenumber 
      AND pc.line_number = cp.jobid
      AND pc.part_number = cp.partnumber
      AND pc.cost = cp.unitcost 
      AND pc.qty = cp.unitcount;

UPDATE :SRC_SCHEMA.client_partsdetails
SET extendedcost = ROUND( COALESCE(extendedcost,0)::numeric + COALESCE(corecharge_cost,0)::numeric + COALESCE(corereturn_cost,0)::numeric , 2),
    extendedsale = ROUND( COALESCE(extendedsale,0)::numeric + COALESCE(corecharge_sale,0)::numeric + COALESCE(corereturn_sale,0)::numeric, 2),
    unitcost     = ROUND( COALESCE(unitcost,0)::numeric +  ((COALESCE(corecharge_cost,0)::numeric + COALESCE(corereturn_cost,0)::numeric)/COALESCE(unitcount,0)::numeric), 2),
    unitsale     = ROUND( COALESCE(unitsale,0)::numeric + ((COALESCE(corecharge_sale,0)::numeric + COALESCE(corereturn_sale,0)::numeric)/COALESCE(unitcount,0)::numeric), 2)
WHERE (corereturn_cost IS NOT NULL OR corereturn_sale IS NOT NULL 
      OR corecharge_cost IS NOT NULL 
      OR corecharge_sale IS NOT NULL) AND (unitcount IS NOT NULL AND unitcount::numeric > 0);

WITH kit_data AS (SELECT
                 "roNumber",
                 "PartNo",
                 allocated_sale_amount,
                 "CustQtyShip" as quantity
             FROM :IMPORT_SCHEMA.etl_parts_detail
             WHERE allocated_kit_sale_item
             ORDER BY "roNumber", coalesce(allocated_sale_amount, 0)
)

UPDATE :SRC_SCHEMA.client_partsdetails
SET 
    unitsale    =  CASE WHEN allocated_sale_amount IS NOT NULL
        		THEN ROUND(allocated_sale_amount/quantity::numeric,2)
                   ELSE unitsale END,
    extendedsale = CASE WHEN allocated_sale_amount IS NOT NULL
        	        THEN allocated_sale_amount
                   ELSE extendedsale END
FROM kit_data
WHERE kit_data."roNumber" = :SRC_SCHEMA.client_partsdetails.invoicenumber AND kit_data."PartNo" = :SRC_SCHEMA.client_partsdetails.partnumber AND
      :SRC_SCHEMA.client_partsdetails.kit_name IS NOT NULL
      AND quantity::numeric <> 0;

---parts there but no labor
INSERT INTO :SRC_SCHEMA.client_labordetails (ldid, invoicenumber, jobid,
                                department, base_paytype, base_paytype_suffix,
                                opcode, opdescription,
                                unit_hours, unit_sale, extended_sale
                                ) 
      
SELECT
        (SELECT MAX(ldid) FROM :SRC_SCHEMA.client_labordetails) + (row_number() OVER (ORDER BY pd.invoicenumber, pd.jobid)),
         	pd.invoicenumber::text,
        	pd.jobid::text,
        	NULL,
        	(array_agg(pd.base_paytype))[1] as paytype,
        	NULL,
        	'NO-OP' AS "LaborOpCode",
         'NO-OP' AS opdescription,
         '0.00' AS "unit_hours",
         '0.00' AS "unit_sale",
         '0.00' AS "extended_sale"
      FROM :SRC_SCHEMA.client_partsdetails pd 
      WHERE invoicenumber||'--'||jobid NOT IN  (
         SELECT invoicenumber||'--'||jobid FROM :SRC_SCHEMA.client_labordetails
      ) 
GROUP BY pd.invoicenumber, pd.jobid;

INSERT INTO :SRC_SCHEMA.client_job_details (invoicenumber, jobid, job_description, complaint, cause, correction)
    (SELECT
         "roNumber"   AS invoicenumber,
         "JobNo"      AS jobid,
         "OpCodeDesc" AS job_description,
         "Complaint"  AS complaint,
         "Cause"      AS cause,
         "Correction" AS correction
     FROM :IMPORT_SCHEMA.etl_job_detail)
    UNION
    (SELECT
         pd.invoicenumber :: text,
         pd.jobid :: text,
         jd."OpCodeDesc" AS job_description,
         jd."Complaint",
         jd."Cause",
         jd."Correction"
     FROM :SRC_SCHEMA.client_partsdetails pd LEFT JOIN (SELECT
                                                                   "roNumber",
                                                                   "JobNo",
                                                                   "OpCodeDesc",
                                                                   "Complaint",
                                                                   "Cause",
                                                                   "Correction"
                                                               FROM :IMPORT_SCHEMA.etl_job_detail) jd
             ON pd.invoicenumber = jd."roNumber"
                AND pd.jobid :: text = jd."JobNo" :: text
     WHERE jd."roNumber" IS NULL AND pd.invoicenumber IS NOT NULL);

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation, source_type) 
    SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    base_paytype,
	    NULL,
	    ABS("extendedsale" :: numeric) AS parts_discount,
	    ABS("extendedsale" :: numeric) AS discount,
	    partdescription,
        false,
        'Negative Sale'
 	FROM :SRC_SCHEMA.client_partsdetails  
      WHERE (unitcount::numeric * unitsale::numeric) < 0;
 
DELETE	FROM :SRC_SCHEMA.client_partsdetails WHERE (unitcount::numeric * unitsale::numeric) < 0;

INSERT INTO :SRC_SCHEMA.client_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, paytype, labor_discount, part_discount, total_discount, summary, need_estimation, source_type) 
    SELECT 
	    invoicenumber,
	    'AMOUNT',
	    'JOB',
	    jobid,
	    base_paytype,
	    ABS("unit_sale" :: numeric) AS labor_disc,
	    NULL AS parts_discount,
	    ABS("unit_sale" :: numeric) AS discount,
	    opdescription,
        true,
        'Negative Labor'
 	FROM :SRC_SCHEMA.client_labordetails  
      WHERE unit_sale::numeric < 0;

UPDATE :SRC_SCHEMA.client_labordetails SET
 unit_sale = '0.00',
 unit_hours = '0.00',
 extended_sale = '0.00' 
WHERE unit_sale::numeric < 0;

UPDATE :SRC_SCHEMA.client_labordetails SET base_paytype = 'CWSPLIT' WHERE base_paytype = 'X';
UPDATE :SRC_SCHEMA.client_partsdetails SET base_paytype = 'CWSPLIT' WHERE base_paytype = 'X';
UPDATE :SRC_SCHEMA.client_labordetail  SET base_paytype = 'CWSPLIT' WHERE base_paytype = 'X';
UPDATE :SRC_SCHEMA.client_partsdetail  SET base_paytype = 'CWSPLIT' WHERE base_paytype = 'X';

INSERT INTO :SRC_SCHEMA.client_scheduler_id(scheduler_id)
SELECT unique_id FROM :IMPORT_SCHEMA.etl_uuid_detail;

INSERT INTO :SRC_SCHEMA.department_details
SELECT * FROM :IMPORT_SCHEMA.etl_department_detail WHERE is_allowed = false;

INSERT INTO :SRC_SCHEMA.paytype_details
SELECT * FROM :IMPORT_SCHEMA.etl_paytype_detail WHERE is_allowed = false;

INSERT INTO :SRC_SCHEMA.client_scenarioinfo
SELECT manufacturer, valid_makes_array::text[], NULL, NULL FROM :IMPORT_SCHEMA.etl_manufacturer_detail;

INSERT INTO :SRC_SCHEMA.all_manufacturer_details
SELECT manufacturer, valid_makes_array::text[] FROM :IMPORT_SCHEMA.etl_all_manufacturer_detail;

INSERT INTO :SRC_SCHEMA.client_makerenames
SELECT original_name,renamed_name,COALESCE(is_default,false) FROM :IMPORT_SCHEMA.etl_makerenames_detail;

INSERT INTO :SRC_SCHEMA.sequence_detail
SELECT * FROM :IMPORT_SCHEMA.etl_sequence_detail;

-- DELETE FROM :SRC_SCHEMA.client_scenarioinfo WHERE is_default = false;
-- DELETE FROM :SRC_SCHEMA.department_details WHERE is_default = false;
-- DELETE FROM :SRC_SCHEMA.paytype_details WHERE is_default = false;
-- DELETE FROM :SRC_SCHEMA.all_manufacturer_details WHERE is_default = false;
-- DELETE FROM :SRC_SCHEMA.client_makerenames WHERE is_default = false;
