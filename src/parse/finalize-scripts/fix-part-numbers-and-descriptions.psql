
\set MAX_LENGTH 40
\C 'Part Descriptions > MAGE Max Length ':MAX_LENGTH
SELECT "PartNo"                                     AS "Part Number",
       "PartNoDesc"                                 AS "Old Part Description",
       left("PartNoDesc", :MAX_LENGTH - 3) || '...' AS "New Part Description"
  FROM :IMPORT_SCHEMA.etl_parts_detail
 WHERE length("PartNoDesc") > :MAX_LENGTH
 GROUP BY 1, 2, 3
 ORDER BY 1;

UPDATE :IMPORT_SCHEMA.etl_parts_detail
  SET "PartNoDesc" = left("PartNoDesc", :MAX_LENGTH - 3) || '...'
WHERE length("PartNoDesc") > :MAX_LENGTH;

\set MAX_LENGTH 20
\C 'Part Numbers > MAGE Max Length ':MAX_LENGTH
SELECT *,
       length("New Part Number") AS new_length,
       count(*) OVER (PARTITION BY left("New Part Number", :MAX_LENGTH)) AS new_pn_count_at_max_len
  FROM (
        SELECT left(regexp_replace("PartNo", '[^A-Z0-9]', '', 'gi'), :MAX_LENGTH) AS "New Part Number",
               array_agg(DISTINCT "PartNo" ORDER BY "PartNo") AS "Old Part Number(s)",
               array_agg(DISTINCT "PartNoDesc" ORDER BY "PartNoDesc") AS "Part Description(s)"
          FROM :IMPORT_SCHEMA.etl_parts_detail
         WHERE length("PartNo") > :MAX_LENGTH
         GROUP BY 1
       ) cleanup
;

UPDATE :IMPORT_SCHEMA.etl_parts_detail
   SET "PartNo" = left(regexp_replace("PartNo", '[^A-Z0-9]', '', 'gi'), :MAX_LENGTH)
 WHERE length("PartNo") > :MAX_LENGTH;
