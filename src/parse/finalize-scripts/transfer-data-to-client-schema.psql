CREATE TABLE make_lookup (
    make_code text NOT NULL PRIMARY KEY,
    make_name text NOT NULL
);

INSERT INTO make_lookup
    SELECT DISTINCT ON (make_code)
        make_code,
        make_name
    FROM (
             SELECT
                 COALESCE("VehicleMake", 'MISSING') AS make_code,
                 COALESCE("VehicleMake", 'MISSING') AS make_name,
                 count(*)      AS cnt
             FROM :IMPORT_SCHEMA.etl_head_detail
             GROUP BY 1, 2
             ORDER BY 3 DESC
         ) src;

INSERT INTO :SRC_SCHEMA.client_invoicemaster (
    invoicenumber, open_date, close_date,
    vehicle_year, vehicle_make, vehicle_model, vin,
    customer_id, advisor_id, store_code, is_current_store,
    mileage)
    SELECT
        h."roNumber",
        "RoCreateDate" :: date,
        nullif("FinalPostDate", '') :: date AS closeddate,
        "VehicleYr",
        COALESCE("VehicleMake", 'MISSING'),
        "ModelDesc",
        COALESCE("<PERSON>", 'Missing'),
        COALESCE("CustNo", 'N/A'),
        LEFT("AdvNo",10),
        'N/A',
        TRUE,
        "MileageIn"
    FROM :IMPORT_SCHEMA.etl_head_detail h
    ORDER BY 1;

INSERT INTO :SRC_SCHEMA.client_labordetail (
    ldid, invoicenumber, jobid,
    department, base_paytype, base_paytype_suffix,
    opcode, opdescription,
    sold_hours, sold_dollars,
    is_warranty, is_internal)
    SELECT
        row_number()
        OVER (
            ORDER BY jd."roNumber", jd."JobNo" ),
        jd."roNumber",
        jd."JobNo"                                           AS line_number,
        hd."DeptType",
        COALESCE(jd."PayType", '')                  AS base_paytype,
        NULL                                                 AS paytype_suffix,
        COALESCE(jd."OpCode", 'NO-OP'),
        CASE WHEN LENGTH(COALESCE(jd."OpCodeDesc",jd."Complaint",jd."Cause",jd."Correction")) < '38'
                THEN COALESCE(jd."OpCodeDesc",jd."Complaint",jd."Cause",jd."Correction")          
        ELSE            
        COALESCE(regexp_replace(regexp_replace(regexp_replace(regexp_replace(jd."OpCodeDesc", 'CUSTOMER STATES.', 'C/S '),
        'CUSTOMER REQUESTS.', 'C/R ', 'gi'), 'CUSTOMER STATE.', '', 'gi'),'CUSTOMER REQUEST.', '', 'gi'), regexp_replace(regexp_replace(regexp_replace(regexp_replace(jd."Complaint", 'CUSTOMER STATES.', 'C/S '),
        'CUSTOMER REQUESTS.', 'C/R ', 'gi'), 'CUSTOMER STATE.', '', 'gi'),'CUSTOMER REQUEST.', '', 'gi'),  regexp_replace(regexp_replace(regexp_replace(regexp_replace(jd."Cause", 'CUSTOMER STATES.', 'C/S '),
        'CUSTOMER REQUESTS.', 'C/R ', 'gi'), 'CUSTOMER STATE.', '', 'gi'),'CUSTOMER REQUEST.', '', 'gi'), regexp_replace(regexp_replace(regexp_replace(regexp_replace(jd."Correction", 'CUSTOMER STATES.', 'C/S '),
        'CUSTOMER REQUESTS.', 'C/R ', 'gi'), 'CUSTOMER STATE.', '', 'gi'),'CUSTOMER REQUEST.', '', 'gi'))
        END                            AS opdescription,
        COALESCE(jd."BillTime", '0') :: numeric              AS soldhours,
        COALESCE(jd."TotalAmt", '0') :: numeric              AS solddollers,
        CASE WHEN jd."PayType" ~ '^W'
            THEN TRUE
        ELSE FALSE
        END                                                  AS is_warranty,
        CASE WHEN jd."PayType" ~ '^I'
            THEN TRUE
        ELSE FALSE
        END                                                  AS is_internal
    FROM :IMPORT_SCHEMA.etl_job_detail jd
        JOIN :IMPORT_SCHEMA.etl_head_detail hd USING ("roNumber");

--SELECT update_kit_parts_pricse ();

INSERT INTO :SRC_SCHEMA.client_partsdetail (
    pdid, ldid, invoicenumber, jobid,
    department, base_paytype,
    base_partsource, partnumber, partdescription,
    unitcost, unitsale, unitcount,
    extendedcost, extendedsale,
    is_warranty, is_internal,
    part_line, is_core_related,
    is_core_partid, net_zero_core_sale,
    --is_core_return_on_normal_part_number,
    kit_pseudo_job, allocated_sale_amount, allocated_kit_sale_item)
    SELECT
        row_number()
        OVER (
            ORDER BY pd."roNumber", pd."JobNo" ),
        NULL                                                                          AS ldid,
        pd."roNumber",
        pd."JobNo",
        ro."DeptType"                                                                 AS department,
        COALESCE(COALESCE(pd."PayType",jd."PayType"), '')                    AS base_paytype,
        btrim(regexp_matches(upper("PartNo"), '^[&A-Z0-9]*([A-Z0-9])') :: text, '{}') AS part_source,
        pd."PartNo",
        COALESCE(pd."PartNoDesc", pd."PartNo", 'NPN'),
        COALESCE(pd."DlrCost", '0') :: numeric,
        COALESCE(pd."CustPrice", '0') :: numeric,
        pd."CustQtyShip" :: integer,
        (COALESCE(pd."DlrCost", '0') :: numeric * pd."CustQtyShip" :: integer) :: numeric(20, 2),
        (COALESCE(pd."CustPrice", '0') :: numeric * pd."CustQtyShip" :: integer) :: numeric(20, 2),
        CASE WHEN COALESCE(pd."PayType",jd."PayType") = 'Warr'
            THEN TRUE
        ELSE FALSE
        END                                                                           AS is_warranty,
        CASE WHEN COALESCE(pd."PayType",jd."PayType") = 'Intr'
            THEN TRUE
        ELSE FALSE
        END                                                                           AS is_internal,
        row_number()
        OVER (
            PARTITION BY pd."roNumber", pd."JobNo"
            ORDER BY pd."JobNo" ),
        pd.corerelated,
        -- pd.is_core_return,
        (pd. "PartNoDesc" = 'CORE RETURN'
        OR (pd. "PartNoDesc" ~* 'core'
        AND pd. "DlrCost" = pd. "CustPrice"
        AND COALESCE("CustQtyShip", "WarrQtyShip", "IntrQtyShip")::numeric = - 1)),
        pd.net_zero_core_sale,
        --pd.corereturnitemindex,
        pd."PartKit" AS kit_pseudo_job,
        CASE pd."isKit" :: boolean
            WHEN true THEN COALESCE(pd."CustPrice", '0') :: numeric
            WHEN false THEN 0
            ELSE 0
        END  allocated_sale_amount,
        pd."isKit" :: boolean AS allocated_kit_sale_item
    FROM :IMPORT_SCHEMA.etl_parts_detail AS pd
        JOIN :IMPORT_SCHEMA.etl_head_detail AS ro ON (ro."roNumber" = pd."roNumber")
       LEFT JOIN :IMPORT_SCHEMA.etl_job_detail AS jd ON (pd."roNumber" = jd."roNumber" AND pd."JobNo" = jd."JobNo")
        WHERE pd."CustPrice" !='0';

\C 'Reporting detail record counts'
SELECT
    (SELECT count(*)
     FROM :SRC_SCHEMA.client_invoicemaster) AS invoice_count,
    (SELECT count(*)
     FROM :SRC_SCHEMA.client_labordetail)   AS labor_count,
    (SELECT count(*)
     FROM :SRC_SCHEMA.client_partsdetail)   AS parts_count;

