CREATE INDEX IF NOT EXISTS eld_ronum ON :IMPORT_SCHEMA.etl_job_detail ("roNumber");

ALTER TABLE :IMPORT_SCHEMA.etl_job_detail
 ADD FOREIGN KEY ("roNumber")
     REFERENCES :IMPORT_SCHEMA.etl_head_detail ("roNumber")
     ON UPDATE CASCADE
     ON DELETE CASCADE
;
--ALTER TABLE :IMPORT_SCHEMA.etl_head_detail ADD PRIMARY KEY("roNumber");

CREATE INDEX IF NOT EXISTS epd_ronum ON :IMPORT_SCHEMA.etl_parts_detail ("roNumber");

ALTER TABLE :IMPORT_SCHEMA.etl_parts_detail
  ADD FOREIGN KEY ("roNumber")
      REFERENCES :IMPORT_SCHEMA.etl_head_detail ("roNumber")
      ON UPDATE CASCADE
      ON DELETE CASCADE
;

ALTER TABLE :IMPORT_SCHEMA.etl_parts_detail
ADD COLUMN IF NOT EXISTS "isKit" text NULL;

DROP FUNCTION IF EXISTS update_kit_parts_pricse () CASCADE;

CREATE FUNCTION update_kit_parts_pricse ()
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    rec RECORD;
    recParts RECORD;
    kit_pricse text;
    total_cost numeric;
    job_number text;
BEGIN
    FOR rec IN SELECT DISTINCT
        "roNumber"
    FROM
        :IMPORT_SCHEMA.etl_parts_detail
    WHERE
        "PartKit" IS NOT NULL LOOP
            UPDATE
                :IMPORT_SCHEMA.etl_parts_detail
            SET
                "isKit" = ("DlrCost" != '0.00'
                    AND "DlrCost" IS NOT NULL)
            WHERE
                "roNumber" = rec. "roNumber";
            SELECT
                "CustPrice"::numeric,
                "JobNo" INTO kit_pricse,
                job_number
            FROM
                :IMPORT_SCHEMA.etl_parts_detail
            WHERE
                "roNumber" = rec. "roNumber"
                AND ("DlrCost" = '0.00'
                    OR "DlrCost" IS NULL);
            SELECT
                SUM("DlrCost"::numeric * "CustQtyShip"::numeric) INTO total_cost
            FROM
                :IMPORT_SCHEMA.etl_parts_detail
            WHERE
                "roNumber" = rec. "roNumber"
                AND "JobNo" = job_number;
            FOR recParts IN
            SELECT
                *
            FROM
                :IMPORT_SCHEMA.etl_parts_detail
            WHERE
                "PartKit" IS NOT NULL
                AND ("DlrCost" != '0.00'
                    AND "DlrCost" IS NOT NULL)
                AND "roNumber" = rec. "roNumber"
                AND "JobNo" = job_number LOOP
                    UPDATE
                        :IMPORT_SCHEMA.etl_parts_detail
                    SET
                        "CustPrice" = ROUND((COALESCE(recParts. "CustQtyShip", '0')::numeric * COALESCE(recParts. "DlrCost", '0')::numeric / COALESCE(total_cost, '0')::numeric) * COALESCE(kit_pricse, '0')::numeric, 2)
                    WHERE
                        "roNumber" = recParts. "roNumber"
                        AND "JobNo" = recParts. "JobNo"
                        AND "CustQtyShip" = recParts. "CustQtyShip"
                        AND "DlrCost" = recParts. "DlrCost";
                END LOOP;
        END LOOP;
END;
$body$
