ALTER TABLE :IMPORT_SCHEMA.etl_parts_detail
    ADD COLUMN IF NOT EXISTS corerelated boolean DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS corereturnitemindex text,
    ADD COLUMN IF NOT EXISTS is_core_return boolean DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS net_zero_core_sale boolean DEFAULT FALSE;

UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET corerelated    = TRUE,
    is_core_return = TRUE
WHERE "CustPrice" :: numeric = "DlrCost" :: numeric AND "CustQtyShip" :: numeric < 0;


-- UPDATE etl_parts_detail
-- SET corerelated         = TRUE,
--     corereturnitemindex = corereturnindex,
--     is_core_return      = FALSE
-- FROM (
--          WITH core_credits AS (
--              SELECT
--                  "SeqNo" AS corereturnindex,
--                  "roNumber",
--                  "PartNo"
--              FROM etl_parts_detail
--              WHERE is_core_return
--          ),
--                  matching_parts AS (
--                  SELECT
--                      cch."SeqNo" AS corechargeindex,
--                      ccr.*
--                  FROM etl_parts_detail AS cch
--                      JOIN (SELECT *
--                            FROM core_credits) ccr
--                          ON (cch."roNumber", cch."PartNo") =
--                             (ccr."roNumber", ccr."PartNo")
--                  WHERE COALESCE("CustPrice", '0.00') :: numeric > 0.00
--              )
--          SELECT
--              corechargeindex,
--              corereturnindex
--          FROM matching_parts
--      ) src
-- WHERE corechargeindex = "SeqNo";


UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET net_zero_core_sale = TRUE
WHERE corerelated
      AND corereturnitemindex IN (
    SELECT corereturnitemindex
    FROM :IMPORT_SCHEMA.etl_parts_detail
    WHERE corerelated
    GROUP BY 1
    HAVING sum("DlrCost" :: numeric) = 0.00
);
