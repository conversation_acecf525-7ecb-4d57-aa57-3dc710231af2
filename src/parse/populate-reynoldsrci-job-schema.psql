DROP SCHEMA IF EXISTS du_dms_reynoldsrci_import CASCADE;
CREATE SCHEMA du_dms_reynoldsrci_import;

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_details (
    invoicenumber text PRIMARY KEY,
    open_date     date NOT NULL,
    close_date    date NULL,
    void_date     date NULL,
    vehicle_year  text NULL,
    vehicle_make  text NOT NULL,
    vehicle_model text NULL,
    vin           text NULL,
    customer_id   text NOT NULL,
    advisor_id    text NULL,
    store_code    text NOT NULL DEFAULT 'N/A',
    -- The invoice master for some stores may contain data
    -- for non-current stores as well.  We identify these
    -- so that process may choose to limit what they consider
    -- to those where the record belongs to the current store.
    -- We can assume that absent the explicit setting of this
    -- value that all records belong to the store being processed.
    is_current_store boolean NOT NULL DEFAULT true,
    branch        text NULL,
    deptname      text NULL,
    mileage       text NULL
);

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_discount_details (
    invoicenumber           text NOT NULL,
    discount_type           text,
    discount_level          text,
    ro_line                 text,
    labor_discount          text,
    labor_discount_percent  text,    
    part_discount           text,
    part_discount_percent   text,
    total_discount          text,
    summary                 text

);

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_gl_accounting (    
    invoicenumber	text NOT NULL,
    hostitemid 		text,
    accountingdate 	text,
    accountnumber 	text,
    accountdescription 	text,
    accounttype 	text,
    incbalgrpdesc 	text,
    incbalsubgrp 	text,
    companyid 		text,
    control 		text,
    journalid 		text,
    postingamount 	text,
    postingsequence 	text,
    postingtime 	text,
    paytype             text,
    insuranceamount     text
);

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_job_details (
    invoicenumber           text NOT NULL,
    jobid                   text,
    job_description         text,
    Complaint               text,
    Cause                   text,
    Correction              text    
    );

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_labordetails (
    ldid                bigint NOT NULL PRIMARY KEY,
    invoicenumber       text NOT NULL,
    jobid               text,
    department          text,
    base_paytype        text,
    base_paytype_suffix text,
    opcode              text,
    opdescription       text,
    unit_hours          numeric,
    unit_sale           numeric,
    extended_sale       numeric,
    has_multiple_desc   boolean NOT NULL DEFAULT false,
    subgroup_size       integer NOT NULL DEFAULT 0,
    multiple_rank       integer NOT NULL DEFAULT 0,
    alt_group_desc      text NULL,
    use_alt_desc        boolean NOT NULL DEFAULT false
);

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_partsdetails (
    pdid                bigint NOT NULL PRIMARY KEY,
    invoicenumber       text NOT NULL ,
    jobid               text,
    department          text,
    base_paytype        text,
    base_paytype_suffix text,
    base_partsource     text,
    partnumber          text,
    partdescription     text,
    unitcost            numeric,
    unitsale            numeric,
    unitcount           integer,
    extendedcost        numeric,
    extendedsale        numeric,
    part_line           integer,
    corecharge_sale     numeric NULL,
    corecharge_cost     numeric NULL,
    corereturn_sale     numeric NULL,
    corereturn_cost     numeric NULL,
    kit_name            text NULL
);

CREATE UNLOGGED TABLE du_dms_reynoldsrci_import.ro_sequence (
    sequence_no     bigint NOT NULL PRIMARY KEY,
    ro_number       text NOT NULL ,
    open_date       date,
    close_date      date,
    void_date       date
);

/*Generate Data for DU-Jobs loading*/

INSERT INTO du_dms_reynoldsrci_import.ro_details (invoicenumber, open_date, close_date,
                                   vehicle_year, vehicle_make, vehicle_model, vin,
                                   customer_id, advisor_id, store_code, is_current_store,
                                   mileage)
    SELECT
        h."roNumber",
        "RoCreateDate" :: date,
        nullif("CustInvoiceDate", '') :: date AS closeddate,
        "VehicleYr",
        COALESCE("VehicleMake", 'MISSING'),
        "ModelDesc",
        COALESCE("Vin", 'Missing'),
        COALESCE("CustNo", 'N/A'),
        LEFT("AdvNo", 10),
        'N/A',
        TRUE,
        "MileageIn"
    FROM etl_head_detail h
    ORDER BY 1;

INSERT INTO du_dms_reynoldsrci_import.ro_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary)
    SELECT
        "roNumber",
         'AMOUNT',
         'JOB',
        "JobNo",
        '0',
        '0',
        ABS("CodeAmt" :: numeric),
        "MiscCodeDesc"
    FROM etl_misc_detail
    WHERE
        "CodeAmt" :: numeric < 0;

INSERT INTO du_dms_reynoldsrci_import.ro_discount_details (invoicenumber, discount_type, discount_level,
                                     ro_line, labor_discount, part_discount, total_discount, summary)
    
    SELECT
        "roNumber",
         'AMOUNT',
         'RO',
          NULL,
        '0',
        '0',
        ABS("NTxblAmt" :: numeric),
        "Type"
    FROM etl_other_detail
    WHERE
        "NTxblAmt" :: numeric < 0;

INSERT INTO du_dms_reynoldsrci_import.ro_labordetails (ldid, invoicenumber, jobid,
                                 department, base_paytype, base_paytype_suffix,
                                 opcode, opdescription,
                                 unit_hours, unit_sale, extended_sale)
    SELECT
        row_number()
        OVER (
            ORDER BY "roNumber", "JobNo" ),
        "roNumber",
        "JobNo"                                    AS jobid,
        ''                                         AS department,
        coalesce(string_agg("PayType", ','), 'NA') AS base_paytype,
        string_agg(CASE WHEN "OpCode" IS NULL
            THEN 'O'
                   ELSE NULL END,
                   ',')                            AS base_paytype_suffix,
        string_agg(upper(COALESCE("OpCode", 'NO-OP')),
                   ',')                            AS opcode,
        string_agg(COALESCE(regexp_replace(regexp_replace(regexp_replace(
                                                                        regexp_replace("OpCodeDesc", 'CUSTOMER STATES.',
                                                                                       'C/S '), 'CUSTOMER REQUESTS.',
                                                                        'C/R '), 'CUSTOMER STATE.', '', 'g'),
                                                     'CUSTOMER REQUEST.', '', 'g'), 'OP: ' || "OpCode"),
                   ',')                            AS opdescription,
        sum("BillTime" :: numeric)                 AS unit_hours,
        coalesce(sum("BillRate" :: numeric), 0)    AS unit_sale,
        coalesce(sum("TotalAmt" :: numeric), 0)    AS extended_sale
    FROM etl_job_detail
    GROUP BY "roNumber", "JobNo";

INSERT INTO du_dms_reynoldsrci_import.ro_partsdetails (pdid, invoicenumber, jobid,
                                 department, base_paytype,
                                 base_partsource, partnumber, partdescription,
                                 unitcost, unitsale, unitcount,
                                 extendedcost, extendedsale,
                                 part_line, corecharge_sale, corecharge_cost,
                                 kit_name)
    SELECT
        row_number()
        OVER (
            ORDER BY pd."roNumber", pd."JobNo" ),
        pd."roNumber"                                                                                 AS invoicenumber,
        pd."JobNo"                                                                                    AS jobid,
        ''                                                                                            AS department,
        pd."PayType" :: text                                                                          AS base_paytype,
        CASE WHEN pd.source_letter_arr IS NULL
            THEN 'NA'
        ELSE pd.source_letter_arr [1]
        END                                                                                           AS base_partsource,
        COALESCE(upper(pd."PartNo"), 'NPN-' || pd."roNumber" || '-' || pd."JobNo")                    AS partnumber,
        COALESCE(pd."PartNoDesc", 'P/N: ' || COALESCE(pd."PartNo", 'NPN'))                            AS partdescription,
        pd."DlrCost" :: numeric                                                                       AS unitcost,
        pd."CustPrice" :: numeric                                                                     AS unitsale,
ceil(coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: integer    AS unitcount,
        -- avoids rounding fractions < 1 to zero
        (pd."DlrCost" :: numeric *
         coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: numeric(20, 2) AS extendedcost,
        (pd."CustPrice" :: numeric *
         coalesce(pd."CustQtyShip", pd."WarrQtyShip", pd."IntrQtyShip") :: numeric) :: numeric(20, 2) AS extendedsale,
        row_number()
        OVER (
            PARTITION BY pd."roNumber", pd."JobNo"
            ORDER BY pd."JobNo" )                                                                     AS part_line,
        NULL                                                                                          AS corecharge_sale,
        NULL                                                                                          AS corecharge_cost,
        CASE WHEN (pd.allocated_kit_sale_item :: boolean AND kit_pseudo_job != '')
            THEN pd.kit_pseudo_job
        WHEN (kit_pseudo_job != '')
            THEN 'KIT-' || pd.kit_pseudo_job
        ELSE NULL
        END                                                                                           AS kit_name
    FROM (SELECT
              *,
              (SELECT regexp_matches(upper("PartNo"), '^[^A-Z0-9]*([A-Z0-9])')) AS source_letter_arr
          FROM etl_parts_detail
         ) AS pd
    WHERE pd."CustPrice" !='0';

-- WITH core_data AS (SELECT
--                        p1."roNumber",
--                        p1."JobNo",
--                        p2."DlrCost" :: numeric *
--                        coalesce(p2."CustQtyShip", p2."WarrQtyShip", p2."IntrQtyShip") :: numeric AS corecost,
--                        p2."CustPrice" :: numeric *
--                        coalesce(p2."CustQtyShip", p2."WarrQtyShip", p2."IntrQtyShip") :: numeric AS coresale,
--                        p1.corereturnitemindex,
--                        p1."PartNo"
--                    FROM etl_parts_detail p1
--                        JOIN etl_parts_detail p2 ON p1."roNumber" = p2."roNumber" AND
--                                                                             p1."JobNo" = p2."JobNo" AND p2.corerelated
--                                                                             AND
--                                                                             p2.is_core_return
--                    WHERE p1.corerelated AND NOT p1.is_core_return),
--     update_core AS ( UPDATE
--                         du_dms_reynoldsrci_import.ro_partsdetails
--                     SET
--                         corereturn_sale = coresale,
--                         corereturn_cost = corecost
--                     FROM
--                         core_data cd
--                     WHERE cd."roNumber" = ro_partsdetails.invoicenumber AND jobid = "JobNo" AND "PartNo" = partnumber
--                     RETURNING *
--     )
-- DELETE FROM du_dms_reynoldsrci_import.ro_partsdetails
-- USING core_data
-- WHERE invoicenumber = "roNumber" AND "JobNo" = jobid AND "PartNo" = partnumber AND
--       unitcount < 0;

-- UPDATE du_dms_reynoldsrci_import.ro_partsdetails
-- SET corereturn_cost = unitcost * unitcount,
--     corereturn_sale = unitsale * unitcount,
--     corecharge_sale = NULL,
--     corecharge_cost = NULL
-- WHERE unitcount < 0 and (partdescription = 'CORE RETURN'
--                           OR
--                           (partdescription~* 'core' AND unitcost = unitsale));WITH etl_parts_detail AS (

WITH etl_parts_detail AS (
    SELECT DISTINCT
        "roNumber"              AS ro_number,   
        "JobNo"                 AS line_number,  
        "PartNo"                AS part_number,  
        "CustQtyShip"::int      AS qty,    
        "DlrCost"::numeric      AS cost, 
        "CustPrice"::numeric    AS net_price
    FROM etl_parts_detail
    WHERE NULLIF("PartNo",'') IS NOT NULL
          AND "CustQtyShip"::int != 0 
          AND "DlrCost"::numeric != 0
)
, parts_list AS(
    SELECT 
        ro_number,   
        line_number,  
        part_number,  
        qty,    
        cost, 
        net_price,
        CASE 
        WHEN part_number = lead(part_number, 1 ) over (wnd) 
            THEN TRUE 
        WHEN part_number = lag(part_number, 1) over (wnd)
            THEN TRUE 
        ELSE FALSE END                             AS have_2,
        bool_or(qty::numeric < 0)over (wnd)        AS have_negative,
        bool_or(qty::numeric > 0)over (wnd)        AS have_positive
    FROM etl_parts_detail
    WINDOW wnd AS (partition by ro_number, line_number, part_number 
                       ORDER BY ro_number, line_number, part_number)
    ORDER BY  ro_number, line_number, part_number
)
, parts_classification AS(
  SELECT
        ro_number,  
        line_number,   
        part_number, 
        qty,    
        cost, 
        net_price, 
        CASE 
        WHEN qty < 0 
            THEN 'Core Return'
        ELSE 'Part Number' END AS classification
    FROM parts_list
    WHERE have_2 
          AND have_negative
          AND have_positive
    WINDOW wnd AS (PARTITION BY ro_number, line_number, part_number 
                   ORDER BY qty < 0 DESC)
)               
, update_ro_partsdetails AS(
    UPDATE du_dms_reynoldsrci_import.ro_partsdetails cp
    SET extendedsale    = extendedsale - (cr.qty * cr.net_price * -1),
        extendedcost    = extendedcost - (cr.qty * cr.cost * -1),
        corecharge_sale = (cr.qty * cr.net_price * -1),
        corecharge_cost = (cr.qty * cr.cost * -1),
        corereturn_sale = (cr.qty * cr.net_price),
        corereturn_cost = (cr.qty * cr.cost)
    FROM parts_classification pn
         LEFT JOIN parts_classification cr 
             ON pn.ro_number = cr.ro_number
                 AND pn.line_number = cr.line_number
                 AND pn.part_number = cr.part_number
                 AND cr.classification = 'Core Return'
    WHERE pn.classification = 'Part Number'
          AND pn.ro_number = cp.invoicenumber 
          AND pn.line_number = cp.jobid
          AND pn.part_number = cp.partnumber
          AND pn.cost = cp.unitcost
          AND pn.qty = cp.unitcount
)
DELETE FROM du_dms_reynoldsrci_import.ro_partsdetails cp
USING parts_classification pc
WHERE pc.classification = 'Core Return'
      AND pc.ro_number = cp.invoicenumber 
      AND pc.line_number = cp.jobid
      AND pc.part_number = cp.partnumber
      AND pc.cost = cp.unitcost 
      AND pc.qty = cp.unitcount;

UPDATE du_dms_reynoldsrci_import.ro_partsdetails
SET extendedcost = ROUND( COALESCE(extendedcost,0)::numeric + COALESCE(corecharge_cost,0)::numeric + COALESCE(corereturn_cost,0)::numeric , 2),
    extendedsale = ROUND( COALESCE(extendedsale,0)::numeric + COALESCE(corecharge_sale,0)::numeric + COALESCE(corereturn_sale,0)::numeric, 2),
    unitcost     = ROUND( COALESCE(unitcost,0)::numeric +  ((COALESCE(corecharge_cost,0)::numeric + COALESCE(corereturn_cost,0)::numeric)/COALESCE(unitcount,0)::numeric), 2),
    unitsale     = ROUND( COALESCE(unitsale,0)::numeric + ((COALESCE(corecharge_sale,0)::numeric + COALESCE(corereturn_sale,0)::numeric)/COALESCE(unitcount,0)::numeric), 2)
WHERE (corereturn_cost IS NOT NULL OR corereturn_sale IS NOT NULL 
      OR corecharge_cost IS NOT NULL 
      OR corecharge_sale IS NOT NULL) AND (unitcount IS NOT NULL AND unitcount::numeric > 0);

WITH kit_data AS (SELECT
                 "roNumber",
                 "PartNo",
                 allocated_sale_amount
             FROM etl_parts_detail
             WHERE allocated_kit_sale_item
             ORDER BY "roNumber", coalesce(allocated_sale_amount, 0)
),
   kit_details AS (SELECT
                   c1.*,
                   CASE WHEN c1.allocated_sale_amount IS NOT NULL
                       THEN c2."PartNo"
                   ELSE 'KIT-' || c2."PartNo" END AS kit_number
               FROM kit_data c1
                   JOIN kit_data c2 ON c1."roNumber" = c2."roNumber" AND c2.allocated_sale_amount IS NULL
    )
UPDATE du_dms_reynoldsrci_import.ro_partsdetails
SET kit_name     = kit_number,
    extendedsale = CASE WHEN allocated_sale_amount IS NOT NULL
        THEN allocated_sale_amount
                   ELSE extendedsale END
FROM kit_details
WHERE kit_details."roNumber" = ro_partsdetails.invoicenumber AND kit_details."PartNo" = ro_partsdetails.partnumber AND
      ro_partsdetails.kit_name IS NOT NULL;

INSERT INTO du_dms_reynoldsrci_import.ro_job_details (invoicenumber, jobid, job_description, complaint, cause, correction)
    (SELECT
         "roNumber"   AS invoicenumber,
         "JobNo"      AS jobid,
         ''           AS job_description,
         "Complaint"  AS complaint,
         "Cause"      AS cause,
         "Correction" AS correction
     FROM etl_job_detail)
    UNION
    (SELECT
         pd.invoicenumber :: text,
         pd.jobid :: text,
         '' AS job_description,
         jd."Complaint",
         jd."Cause",
         jd."Correction"
     FROM du_dms_reynoldsrci_import.ro_partsdetails pd LEFT JOIN (SELECT
                                                                   "roNumber",
                                                                   "JobNo",
                                                                   "Complaint",
                                                                   "Cause",
                                                                   "Correction"
                                                               FROM etl_job_detail) jd
             ON pd.invoicenumber = jd."roNumber"
                AND pd.jobid :: text = jd."JobNo" :: text
     WHERE jd."roNumber" IS NULL AND pd.invoicenumber IS NOT NULL);

INSERT INTO du_dms_reynoldsrci_import.ro_sequence (sequence_no, ro_number, open_date, close_date)
WITH inv_master AS (select
                        "roNumber" as invoicenumber,
                        regexp_replace("roNumber", '[[:alpha:]s]', '', 'g')::integer inv_number,
                       "RoCreateDate" :: date as open_date,
                       nullif("CustInvoiceDate", '') :: date as close_date
                   FROM etl_head_detail
                    )
    , ro_sequence AS (SELECT generate_series(min_invoicenumber,
                                             max_invoicenumber) AS ro_number
                      FROM (SELECT
                                min(inv_number) AS min_invoicenumber,
                                max(inv_number) AS max_invoicenumber
                            FROM inv_master) t
)
SELECT														
    ROW_NUMBER()												
    OVER (													
        ORDER BY COALESCE(number_part::integer, ro_number::integer ), code_part nulls first) AS sequence_no,			
    ro_number,													
    open_date,													
    close_date													
FROM (SELECT													
          coalesce(ro_number::text , invoicenumber)                                        AS ro_number,	
          substring(invoicenumber from '^[0-9]+')                                          AS number_part,	
          substring(invoicenumber from '[A-Za-z]+$')                                       AS code_part,	
          open_date,												
          close_date												
      FROM ro_sequence												
          FULL OUTER JOIN inv_master ON ro_number :: text = invoicenumber						
     ) t;