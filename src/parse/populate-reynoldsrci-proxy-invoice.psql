-- Search Path has already been set
SET client_min_messages = WARNING;


ALTER TABLE repair_order  ADD COLUMN stock_no                text  NULL;
ALTER TABLE repair_order  ADD COLUMN accounting_make_code    text  NULL;
ALTER TABLE repair_order  ADD COLUMN advisor_number          text  NULL;
ALTER TABLE repair_order  ADD COLUMN recommendations         text  NULL;
ALTER TABLE repair_order  ADD COLUMN ro_prefix               text  NULL;

ALTER TABLE repair_job ADD COLUMN parts_cost   numeric NULL;
ALTER TABLE repair_job ADD COLUMN up_sell_flag text    NULL;
ALTER TABLE repair_job ADD COLUMN billtime_tech  text NULL;

ALTER TABLE repair_order_vehicle ADD COLUMN carline text NULL;
ALTER TABLE repair_order_vehicle ADD COLUMN delivery_odm text NULL;
ALTER TABLE repair_order_vehicle ADD COLUMN engine_no text NULL;

ALTER TABLE repair_part ADD COLUMN part_kit text NULL;

ALTER TABLE repair_job ADD COLUMN split_cost   text NULL;
ALTER TABLE repair_job ADD COLUMN split_total  text NULL;
ALTER TABLE repair_job ALTER COLUMN sold_hours TYPE text;

ALTER TABLE repair_job ADD COLUMN origcorrection text NULL;
ALTER TABLE repair_job ADD COLUMN origcause text NULL;

ALTER TABLE repair_part ADD COLUMN seq_no       text NULL;

UPDATE du_dms_reynoldsrci_model.etl_job_detail SET "OrigCause" = NULL WHERE "OrigCause" = 'C(#)';
UPDATE du_dms_reynoldsrci_model.etl_job_detail SET "OrigCorrection" = NULL WHERE "OrigCorrection" = 'C(#)';

--repair_order
INSERT INTO repair_order (ro_number, creation_date, completion_date, department,
                          open_time, comments, status, advisor, stock_no, tag_no, ro_prefix,
			  accounting_make_code, advisor_number, recommendations, tax_warranty, tax_customer , 
              tax_internal, estimate_amount)
    WITH multiStore AS (
       	SELECT dms_store_id FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY dms_store_id
    )
    SELECT DISTINCT ON (etl_head_detail."roNumber")
        etl_head_detail."roNumber" AS ro_number,
        "RoCreateDate" :: date     AS creation_date,
        (CASE  WHEN (SELECT count(dms_store_id) FROM multiStore) > 0 THEN
        	CASE WHEN iv.closedate is not null THEN
        	    "FinalPostDate"
        	ELSE
        		null
        	END
        ELSE
           "FinalPostDate"
        END):: date                AS completion_date,
        "DeptType"                 AS department,
        "RoCreateTime" :: time     AS open_time,
        -- "RoComment"                AS comments,
        CASE WHEN json_typeof("RoComment" :: json) = 'array' THEN
        (
            SELECT
                string_agg("RoComment", '~')
            FROM
                json_to_recordset("RoComment" :: json) AS elem ("RoComment" text))
            WHEN json_typeof("RoComment" :: json) = 'object' THEN
        (
            SELECT
                string_agg("RoComment", '~')
            FROM
                json_to_record("RoComment" :: json) AS elem ("RoComment" text))
        END AS comments,
        "RoStatus"                 AS status,
        "AdvName"                  AS advisor,
        "StockID"                  AS stock_no,
        "TagNo"                    AS tag_no,
        "AcctgPfx"                 AS ro_prefix,
        iv."accounting_make_code"  AS accounting_make_code,
        "AdvNo"                    AS advisor_number,
         CASE WHEN json_typeof("TechRecommend" :: json) = 'array' THEN
        (
            SELECT
                string_agg("TechRecommend", '::')
            FROM
                json_to_recordset("TechRecommend" :: json) AS elem ("TechRecommend" text))
            WHEN json_typeof("TechRecommend" :: json) = 'object' THEN
        (
            SELECT
                string_agg("TechRecommend", '\n')
            FROM
                json_to_record("TechRecommend" :: json) AS elem ("TechRecommend" text))
        END recommendations,
        "WarrTaxTotalAmt" ::numeric  AS tax_warranty,
        "CustTaxTotalAmt" ::numeric  AS tax_customer, 
        "IntrTaxTotalAmt" ::numeric  AS tax_internal,
        "EstTotalAmt" ::numeric      AS estimate_amount
    FROM du_dms_reynoldsrci_model.etl_head_detail
        LEFT JOIN du_dms_reynoldsrci_model.etl_customer_detail USING ("roNumber")
        LEFT JOIN du_dms_reynoldsrci_model.etl_job_detail ON etl_head_detail."roNumber" = etl_job_detail."roNumber"
        LEFT JOIN du_dms_reynoldsrci_model.etl_tech_detail td ON etl_head_detail."roNumber" = td."roNumber"
        LEFT JOIN du_dms_reynoldsrci_model.etl_invoicemaster iv ON etl_head_detail."roNumber" = iv."invoicenumber"
        LEFT JOIN du_dms_reynoldsrci_model.etl_estimate_details ON etl_head_detail."roNumber" = etl_estimate_details."roNumber";

--	repair_order_vehicle
INSERT INTO repair_order_vehicle (ro_number, vehicle_year, vehicle_make,
				  vehicle_model, vehicle_vin, vehicle_color,
			          mileage_in, mileage_out, carline, delivery_odm, engine_no)
    SELECT
        "roNumber"           AS ro_number,
        "VehicleYr"          AS vehicle_year,
        "VehicleMake"        AS vehicle_make,
        "ModelDesc"          AS vehicle_model,
        "Vin"                AS vehicle_vin,
        "ExtClrDesc"         AS vehicle_color,
        "MileageIn" :: int8  AS mileage_in,
        "MileageOut" :: int8 AS mileage_out,
        "Carline"            AS carline,
        "DeliveryOdom"       AS delivery_odm,
        "EngNo"              AS engine_no
    FROM du_dms_reynoldsrci_model.etl_head_detail;

--repair_order_customer
INSERT INTO repair_order_customer (ro_number, customer_number, customer_name, customer_city,
                                   customer_state, customer_zip, customer_email, customer_address)
    SELECT
        "roNumber" AS ro_number,
        "CustNo"   AS customer_number,
        coalesce("CustName", '') AS customer_name, 
        -- coalesce("FirstName", '') || ' ' || coalesce("LastName", '') AS customer_name,
        "City"     AS customer_city,
        "State"    AS customer_state,
        "Zip"      AS customer_zip,
        "MailTo"   AS customer_email,
        "Addr1"    AS customer_address
    FROM du_dms_reynoldsrci_model.etl_customer_detail;

--repair_line
INSERT INTO repair_line (ro_number, ro_line, complaint)
    SELECT
        "roNumber"  AS ro_number,
        "JobNo"     AS ro_line,
        "Complaint" AS complaint
    FROM du_dms_reynoldsrci_model.etl_job_detail;

--repair_job
INSERT INTO repair_job (ro_number, ro_line, ro_job_line, billing_code, op_code,
                        op_description, labor_cost, sale_amount, cause,
			correction, origcause, origcorrection, up_sell_flag, billtime_tech, split_cost, split_total, sold_hours)
  SELECT 
        jd."roNumber"                 AS ro_number,
        jd."JobNo"                    AS ro_line,
        1                          AS ro_job_line,
        coalesce(coalesce (jd."PayType",p."paytype"),'Cust') AS billing_code,
        jd."OpCode"                   AS op_code,  
        jd."OpCodeDesc"               AS op_description, 
        coalesce( jd."DlrCost"::numeric , 0.00)  AS labor_cost, 
        coalesce( jd."TotalAmt"::numeric , 0.00) AS sale_amount,
        coalesce(jd."Cause", '')      AS cause,
        coalesce(jd."Correction", '') AS correction,
        coalesce(jd."OrigCause", '')  AS origcause,
        coalesce(jd."OrigCorrection", '') AS origcorrection,
        jd."UpSellFlag"               AS up_sell_flag,
        jd."BillTime_Tech"            AS billtime_tech,
        jd."SplitCost"                AS split_cost,
        jd."SplitTotal"               AS split_total,
        jd."BillTime"				  AS sold_hours 
    FROM du_dms_reynoldsrci_model.etl_job_detail jd
    LEFT JOIN 
    (SELECT (array_agg(COALESCE(p."PayType", s."PayType", m."PayType")))[1] AS paytype,jd."roNumber",jd."JobNo" 
    FROM du_dms_reynoldsrci_model.etl_job_detail jd 
    LEFT JOIN du_dms_reynoldsrci_model.etl_parts_detail p ON p."roNumber" = jd."roNumber" AND p."JobNo"= jd."JobNo"
    LEFT JOIN du_dms_reynoldsrci_model.etl_sublet_detail s ON jd."roNumber" = s."roNumber" AND jd."JobNo"= s."JobNo"
    LEFT JOIN du_dms_reynoldsrci_model.etl_misc_detail m ON jd."roNumber" = m."roNumber" AND jd."JobNo"= m."JobNo"
        GROUP BY jd."roNumber",jd."JobNo")p 
            ON p."roNumber"=jd."roNumber" AND p."JobNo"=jd."JobNo";

--repair_job_technician_detail
INSERT INTO repair_job_technician_detail (ro_number, ro_line, ro_job_line, ro_job_tech_line,
                                          booked_hours, actual_hours, tech_id, tech_name, 
                                          customer_tech_rate, waranty_tech_rate, internal_tech_rate,
                                          job_work_date, start_time, finish_time)
    SELECT
        "roNumber"                                            AS ro_number,
        "JobNo"                                               AS ro_line,
        1                                                     AS ro_job_line,
        row_number() OVER ()                                  AS ro_job_tech_line,
        coalesce(nullif("TechHrs", '') :: numeric, 0)         AS booked_hours,
        coalesce(nullif("ActualHrsWorked", '') :: numeric, 0) AS actual_hours,
        "TechNo"                                              AS tech_id,
        "TechName"                                            AS tech_name,
        "CustTechRate"                                        AS customer_tech_rate,
        "WarrTechRate"                                        AS waranty_tech_rate, 
        "IntrTechRate"                                        AS internal_tech_rate,
        "JobWorkDate"                                         AS work_date,
        "TechStartTime"                                       AS start_time,
        "TechFinishTime"                                      AS finish_time
    FROM du_dms_reynoldsrci_model.etl_tech_detail;


--repair_part
INSERT INTO repair_part (ro_number, ro_line, ro_job_line, ro_part_line, part_number,
                         part_description, quantity_sold, unit_cost, unit_sale, prt_billing_type, part_kit, seq_no)
    SELECT
        "roNumber"                                              AS ro_number,
        "JobNo"                                                 AS ro_line,
        1                                                       AS ro_job_line,
        row_number()
        OVER (
            PARTITION BY "roNumber", "JobNo" ORDER BY "roNumber", "JobNo", "SeqNo"  )                                                                         AS ro_part_line,
        "PartNo"                                                AS part_number,
        "PartNoDesc"                                            AS part_description,
        coalesce(nullif(trim("CustQtyShip"), '') :: numeric, 0) AS quantity_sold,
        coalesce(nullif(trim("DlrCost"), '') :: numeric, 0)     AS unit_cost,
        coalesce(nullif(trim("CustPrice"), '') :: numeric, 0)   AS unit_sale,
        "PayType"                                               AS prt_billing_type,
        "PartKit"                                               AS part_kit,
        "SeqNo"                                                 AS seq_no
    FROM du_dms_reynoldsrci_model.etl_parts_detail;

--repair_other
INSERT INTO repair_other (ro_number, item_type, ro_line, other_code, other_description,
                          other_sale, other_billing_type, autocalc)
     SELECT  fd."roNumber"                                              AS ro_number,
        'MISC'                                                      AS item_type,
        fd."JobNo"                                                  AS ro_line,
        fd."MiscCode"                                               AS other_code,
        fd."MiscCodeDesc"                                           AS other_description,
        COALESCE(nullif(trim(fd."CodeAmt"), ''), '0.00') :: numeric AS other_sale,
        COALESCE(f."PayType",  fd."PayType" )                       AS other_billing_type,
        fd."AutoCalc"                                               AS autocalc                                      
    FROM du_dms_reynoldsrci_model.etl_misc_detail fd
    JOIN du_dms_reynoldsrci_model.etl_head_detail hd ON hd."roNumber" =  fd."roNumber"    
    LEFT JOIN (SELECT "roNumber", "JobNo", (array_agg("PayType"))[1] AS "PayType", (array_agg("TotalAmt"))[1] AS  "TotalAmt"
		       FROM du_dms_reynoldsrci_model.etl_misc_detail1 
		       GROUP BY "roNumber", "JobNo" HAVING COUNT(*)= 1 ) f
        ON fd."roNumber" = f."roNumber"
        AND fd."JobNo"= f."JobNo" AND fd."CodeAmt" = f."TotalAmt"
    WHERE fd."CodeAmt"::numeric <> 0 
    ORDER BY fd."roNumber",fd."JobNo";
    
-- INSERT INTO repair_other (ro_number, item_type, ro_line, other_code, other_description, other_billing_type)
--     SELECT
--         "roNumber"                                               AS ro_number,
--         'SUBLET'                                                 AS item_type,
--         "JobNo"                                                  AS ro_line,
--         "SubletPoNo"                                             AS other_code,
--         "SubletDesc"                                             AS other_description,
--         "PayType"                                                AS other_billing_type
--     FROM du_dms_reynoldsrci_model.etl_sublet_detail
--          left join du_dms_reynoldsrci_model.etl_job_detail using ("roNumber", "JobNo");

 INSERT INTO du_dms_reynoldsrci_proxy.repair_other
    (
    ro_number,
    item_type,
    ro_line,
    other_billing_type,
    other_code,
    other_description,
    other_cost,
    other_sale,
    other_date,
    vendor_inv
    )
SELECT
    sd."roNumber"                                                                                          AS ro_number,
    'SUBLET'                                                                                            AS item_type,
    "JobNo"                                                                                             AS ro_line,
    "PayType"                                                                                           AS other_billing_type,
    "SubletPoNo"                                                                                        AS other_code,
    "SubletDesc"                                                                                        AS other_description,
    coalesce(nullif ("DlrCost", '')::numeric, 0.00)                                                     AS other_cost,
    coalesce(nullif ("CustPrice", '')::numeric, 0.00)                                                   AS other_sale,
    "PoRecDate"                  AS other_date,
    "VendorInvNo"
FROM du_dms_reynoldsrci_model.etl_sublet_detail sd
JOIN du_dms_reynoldsrci_model.etl_head_detail hd ON hd."roNumber" =  sd."roNumber";       


INSERT INTO du_dms_reynoldsrci_proxy.repair_other
    (
    ro_number,
    item_type,
    ro_line,
    other_billing_type,
    other_code,
    other_description,
    other_cost,
    other_sale,
    other_quantity
    )
SELECT
    gd."roNumber"                                                                                          AS ro_number,
    'GOG'                                                                                               AS item_type,
    "JobNo"                                                                                             AS ro_line,
    "PayType"                                                                                           AS other_billing_type,
    "ItemType"                                                                                          AS other_code,
    "ItemDesc"                                                                                          AS other_description,
    coalesce(nullif ("DlrCost", '')::numeric, 0.00)                                                     AS other_cost,
    coalesce(nullif ("CustPrice", '')::numeric, 0.00)                                                   AS other_sale,
    coalesce(nullif ("CustQty", '')::numeric, 0.00)                                                     AS other_quantity
FROM du_dms_reynoldsrci_model.etl_gog_detail gd
JOIN du_dms_reynoldsrci_model.etl_head_detail hd ON hd."roNumber" =  gd."roNumber";
 

--repair_other_totals
INSERT INTO repair_other_totals (ro_number, types, all_to_amt_type, dlr_cost, ntxble_amt,
                          txbl_amt, pay_type, amt_type, cust_price)
    SELECT
        od."roNumber"                                               AS ro_number,
        od."Type"                                                   AS types,
        "AllTotAmtType"                                          AS all_to_amt_type,
        "DlrCost"                                                AS dlr_cost,
        "NTxblAmt"                                               AS ntxble_amt,
        "TxblAmt"                                                AS txbl_amt,
        "PayType"                                                AS pay_type,
        "AmtType"                                                AS amt_type,
        "CustPrice"                                              AS cust_price 
    FROM du_dms_reynoldsrci_model.etl_other_detail  od
JOIN du_dms_reynoldsrci_model.etl_head_detail hd ON hd."roNumber" =  od."roNumber";


-- --repair_dealer_details
INSERT INTO repair_dealer_details ( component, task, dealer_number, store_number,
                          area_number, creation_date_time, bod_id, destination_name_code )
    SELECT
        'Component'                                              AS component,
        "Task"                                                   AS task,
        "DealerNumber"                                           AS dealer_number,
        "StoreNumber"                                            AS store_number,
        "AreaNumber"                                             AS area_number,
        "CreationDateTime"                                       AS creation_date_time,
        "BODId"                                                  AS bod_id, 
        "DestinationNameCode"                                    AS destination_name_code
    FROM du_dms_reynoldsrci_model.etl_store_detail;


-- --repair_claim_details
-- INSERT INTO repair_claim_details ( ro_number, job_number, labor_sale, parts_sale,
--                           sublet_labor, sublet_parts, gog, misc, tax, total )
-- WITH CTE1 AS(
--     SELECT "roNumber", "JobNo", 
--     SUM("CustPrice" :: numeric * "CustQtyShip" :: numeric) AS parts_sale
--     FROM du_dms_reynoldsrci_model.etl_parts_detail
--     WHERE "PayType"='Warr' 
--     Group by  1, 2
-- ),
-- CTE2 AS(
-- 	SELECT "roNumber", "NTxblAmt" AS sublet_labor
-- 	FROM du_dms_reynoldsrci_model.etl_other_detail
-- 	WHERE "AllTotAmtType" ='Labor' AND "PayType"= 'Warr' AND  "Type"='SUBLET'
-- ),
-- CTE3 AS(
-- SELECT "roNumber", "NTxblAmt" AS sublet_parts
-- 	FROM du_dms_reynoldsrci_model.etl_other_detail
-- 	WHERE "AllTotAmtType" ='Parts' AND "PayType"= 'Warr' AND  "Type"='SUBLET'
-- ),
-- CTE4 AS(
-- SELECT "roNumber", "NTxblAmt" AS gog
-- 	FROM du_dms_reynoldsrci_model.etl_other_detail
-- 	WHERE "AllTotAmtType" ='All' AND "PayType"= 'Warr' AND  "Type"='GOG'
-- ),
-- CTE5 AS(
-- 	SELECT "roNumber", "NTxblAmt" AS misc
-- 	FROM du_dms_reynoldsrci_model.etl_other_detail
-- 	WHERE "AllTotAmtType" ='All' AND "PayType"= 'Warr' AND  "Type"='MISC'
-- ),
-- CTE6 AS(
-- SELECT ejd."roNumber",ejd."JobNo",
-- 	coalesce(nullif(ejd."TotalAmt", '')::numeric, 0.00) AS labor_sale,
--     coalesce(nullif(CTE1.parts_sale, '0.00'), 0.00) ::numeric AS parts_sale,
--     coalesce(nullif(CTE2.sublet_labor, '')::numeric, 0.00) AS sublet_labor,
-- 	coalesce(nullif(CTE3.sublet_parts, '')::numeric, 0.00) AS sublet_parts,
-- 	coalesce(nullif(CTE4.gog, '')::numeric, 0.00) AS gog,
-- 	coalesce(nullif(CTE5.misc, '')::numeric, 0.00) AS misc,
--     '0.00' ::numeric AS tax
-- 	FROM du_dms_reynoldsrci_model.etl_job_detail AS ejd
--     LEFT JOIN CTE1 USING ("roNumber", "JobNo")
-- 	LEFT JOIN CTE2 USING ("roNumber")
-- 	LEFT JOIN CTE3 USING ("roNumber")
-- 	LEFT JOIN CTE4 USING ("roNumber")
-- 	LEFT JOIN CTE5 USING ("roNumber")
-- WHERE ejd."PayType"='Warr' ORDER BY ejd."roNumber" :: numeric ASC 
-- )
-- SELECT "roNumber","JobNo",labor_sale, parts_sale, sublet_labor, sublet_parts, gog,
-- misc, tax,
-- ( labor_sale + parts_sale + sublet_labor + sublet_parts + gog + misc + tax) AS
-- total FROM CTE6 ORDER BY "roNumber";

INSERT INTO repair_claim_details
    (ro_number, claim_number, labor_sale, parts_sale, sublet_labor, sublet_parts, gog, misc, tax, total)
SELECT
    "roNumber",
    "ClaimNo",
    coalesce(nullif ("WarrClaimNTxblLbrAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimNTxblPtsAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimTotalSubLbrAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimTotalSubPtsAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimTotalGogAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimTotalMiscAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimTotalTaxAmt", '')::numeric, 0.00),
    coalesce(nullif ("WarrClaimNTxblLbrAmt", '')::numeric, 0.00) +
	coalesce(nullif ("WarrClaimNTxblPtsAmt", '')::numeric, 0.00) +
	coalesce(nullif ("WarrClaimTotalSubLbrAmt", '')::numeric, 0.00) + 
	coalesce(nullif ("WarrClaimTotalSubPtsAmt", '')::numeric, 0.00) +
	coalesce(nullif ("WarrClaimTotalGogAmt", '')::numeric, 0.00) + 
	coalesce(nullif ("WarrClaimTotalMiscAmt", '')::numeric, 0.00) +
	coalesce(nullif ("WarrClaimTotalTaxAmt", '')::numeric, 0.00)
FROM
    du_dms_reynoldsrci_model.etl_warrantyclaim_detail;

INSERT INTO repair_job_labor_details (ro_number, job_number, opcode, opcode_desc, tech_no, tech_name, pay_type, bill_time, dlr_cost, total_amt)
SELECT
    "roNumber",
    "JobNo",
    "OpCode",
    "OpCodeDesc",
    "TechNo",
    "TechName",
    "PayType",
    "BillTime",
    "DlrCost",
    "TotalAmt"
FROM
    du_dms_reynoldsrci_model.etl_labor_details;


INSERT INTO repair_spg_details (ro_number, job_number, opcode, opcode_desc)
SELECT * FROM  du_dms_reynoldsrci_model.etl_spg_detail;

INSERT INTO repair_recommended_service_detail (ro_number, rec_serv_flag, rec_serv_desc, rec_serv_code)
 SELECT * FROM  du_dms_reynoldsrci_model.etl_recommended_service_detail;

CREATE TABLE du_dms_reynoldsrci_proxy.inv_sequence_master (
  store            text,
  ro_num           text NOT NULL,
  inv_type         text
);
INSERT INTO inv_sequence_master (store, ro_num, inv_type)
WITH multiStore AS (
    SELECT dms_store_id FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY dms_store_id
)
SELECT null, regexp_replace("roNumber", '[^0-9]', '', 'g'), 'C' FROM du_dms_reynoldsrci_model.etl_head_detail 
    WHERE (SELECT count(dms_store_id) FROM multiStore) <= 0 GROUP BY "roNumber";

INSERT INTO inv_sequence_master (store, ro_num, inv_type)
WITH multiStore AS (
    SELECT dms_store_id FROM du_dms_reynoldsrci_model.etl_invoicemaster GROUP BY dms_store_id
)
SELECT null, regexp_replace("roNumber", '[^0-9]', '', 'g'), 'C' FROM du_dms_reynoldsrci_model.etl_head_detail h 
    JOIN du_dms_reynoldsrci_model.etl_invoicemaster i ON "roNumber" = invoicenumber
    WHERE (SELECT count(dms_store_id) FROM multiStore) > 0 AND (closedate IS NOT NULL AND "FinalPostDate" IS NOT NULL) GROUP BY "roNumber";

INSERT INTO inv_sequence_master (store, ro_num, inv_type)
  select dms_store_id , regexp_replace("invoicenumber", '[^0-9]', '', 'g'), 
  case when dms_store_id = (select "AreaNumber"||'S' as store from du_dms_reynoldsrci_model.etl_store_detail limit 1) then
  'V'
  else 'O'
  end as inv_type
  FROM du_dms_reynoldsrci_model.etl_invoicemaster 
 	where invoicenumber not in (
 		SELECT "roNumber" FROM du_dms_reynoldsrci_model.etl_head_detail GROUP BY "roNumber"
 	) GROUP BY dms_store_id, invoicenumber;


INSERT INTO inv_sequence_master (store, ro_num, inv_type)
with min_max_ro as (
 SELECT min((regexp_replace("roNumber", '[^0-9]', '', 'g'))::integer),
        max((regexp_replace("roNumber", '[^0-9]', '', 'g'))::integer)
 FROM du_dms_reynoldsrci_model.etl_head_detail
      WHERE "roNumber" ~ '^\d+$'
)
,gen_series_ro as (
select * from min_max_ro, generate_series(min, max) gs (ro_num)
)
select null, ro_num, 'V' from gen_series_ro WHERE ro_num::text NOT IN (
   SELECT ro_num::text FROM inv_sequence_master
)


