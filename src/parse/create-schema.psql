BEGIN;

SELECT 'Creating Schema!';

SELECT set_config('client_min_messages', 'WARNING', true);

DROP SCHEMA  IF EXISTS  du_dms_reynoldsrci CASCADE;
CREATE SCHEMA du_dms_reynoldsrci;

SELECT set_config('search_path', 'du_dms_reynoldsrci', true);

\i :DU_ETL_HOME/DU-Transform/client/process/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/process/create-client-functions.psql
\i :DU_ETL_HOME/DU-Transform/client/makes/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/invoicesequence/prepare-client-tables.psql
\i :DU_ETL_HOME/DU-Transform/client/paytypes/prepare-client-tables.psql

\i :DU_ETL_HOME/DU-Transform/client/decisions/paytype-decision-functions.psql

CREATE FUNCTION print_non_empty(text_to_print text)
RETURNS text
LANGUAGE plpgsql
AS $$
BEGIN
RAISE NOTICE '%', text_to_print;
RETURN text_to_print;
END;
$$;

COMMIT;
