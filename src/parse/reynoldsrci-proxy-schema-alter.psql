-- Search Path has already been set
SET client_min_messages = warning;

-- ALTER TABLE du_dms_automate_proxy.repair_order  ADD COLUMN terms                          text  NULL;
-- ALTER TABLE du_dms_automate_proxy.repair_order_vehicle ADD stock_number                   text  NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD customer_tech_rate        text  NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD waranty_tech_rate         text  NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD internal_tech_rate        text  NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD job_work_date                 text  NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD start_time                text  NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_job_technician_detail ADD finish_time               text  NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_other                 ADD vendor_inv                text  NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_order                 ADD tax_warranty              text NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_order                 ADD tax_customer              text NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_order                 ADD tax_internal              text NULL;

ALTER TABLE du_dms_reynoldsrci_proxy.repair_other                 ADD other_quantity            text NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_other                 ADD other_date                text NULL;
ALTER TABLE du_dms_reynoldsrci_proxy.repair_other                 ADD autocalc                  text NULL;

CREATE TABLE repair_dealer_details (
    ro_dealer_id                                bigserial NOT NULL PRIMARY KEY,
    component                                   text     NULL,
    task                                        text     NULL,
    dealer_number                               text     NULL,
    store_number                                text     NULL,
    area_number                                 text     NULL,
    creation_date_time                          text     NULL,
    bod_id                                      text     NULL,
    destination_name_code                       text     NULL
);

CREATE TABLE repair_other_totals (
    ro_other_total_id                             bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    types                                         text     NULL,
    all_to_amt_type                               text     NULL,
    dlr_cost                                      text     NULL,
    ntxble_amt                                    text     NULL,
    txbl_amt                                      text     NULL,
    pay_type                                      text     NULL,
    amt_type                                      text     NULL,
    cust_price                                    text     NULL
);


CREATE TABLE du_dms_reynoldsrci_proxy.repair_claim_details (
    ro_claim_detail_id                            bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    -- job_number                                 text     NULL,
    claim_number                                  text     NULL,
    labor_sale                                    text     NULL,
    parts_sale                                    text     NULL,
    sublet_labor                                  text     NULL,
    sublet_parts                                  text     NULL,
    gog                                           text     NULL,
    misc                                          text     NULL,
    tax                                           text     NULL,
    total                                         text     NULL
);



CREATE TABLE du_dms_reynoldsrci_proxy.repair_warranty_claim_details (
    ro_warranty_claim_detail_id                   bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    job_number                                    text     NULL,
    labor_sale                                    text     NULL,
    parts_sale                                    text     NULL,
    sublet_labor                                  text     NULL,
    sublet_parts                                  text     NULL,
    gog                                           text     NULL,
    misc                                          text     NULL,
    tax                                           text     NULL,
    total                                         text     NULL
);


CREATE TABLE du_dms_reynoldsrci_proxy.repair_job_labor_details (
    ro_technician_item_id                         bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    job_number                                    text     NULL,
    opcode                                        text     NULL,
    opcode_desc                                   text     NULL,
    tech_no                                       text     NULL,
    tech_name                                     text     NULL,
    pay_type                                      text     NULL,
    bill_time                                     text     NULL,
    dlr_cost                                      text     NULL,
    total_amt                                     text     NULL
);


CREATE TABLE du_dms_reynoldsrci_proxy.repair_spg_details (
    ro_spg_id                         bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    job_number                                    text     NULL,
    opcode                                        text     NULL,
    opcode_desc                                   text     NULL
);

CREATE TABLE du_dms_reynoldsrci_proxy.repair_recommended_service_detail (
    ro_rec_serv_id                           bigserial NOT NULL PRIMARY KEY,
    ro_number                                     text NOT NULL,
    rec_serv_flag                                 text     NULL,
    rec_serv_desc                                 text     NULL,
    rec_serv_code                                 text     NULL
);












