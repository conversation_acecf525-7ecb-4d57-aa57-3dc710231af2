SELECT set_config('client_min_messages', 'WARNING', false);

DROP TABLE IF EXISTS data_dictionary_reference;
CREATE TABLE data_dictionary_reference (
    field_name text not null primary key,
    is_array boolean null, --can only know if has_children is true
    has_children boolean not null
);

DROP TABLE IF EXISTS data_dictionary_current;
CREATE TABLE data_dictionary_current (
    field_name text not null primary key,
    is_array boolean null,
    has_children boolean not null
);

DROP FUNCTION IF EXISTS build_records(text, variadic text[]) CASCADE;
CREATE OR REPLACE FUNCTION build_records(jsonb_table_name text, primary_field text, variadic fields text[])
RETURNS SETOF record
LANGUAGE plpgsql
STRICT
AS $$
DECLARE tlist text; fld text; from_list text;
BEGIN

    tlist := format('%I', 'roNumber');
    from_list := format(E'\nFROM %I', jsonb_table_name);

    IF primary_field <> 'roNumber'
    THEN
        tlist := tlist || format(', %s.*', lower(primary_field));
        from_list := from_list || format( ', LATERAL (SELECT jsonb_array_elements(%I->%L)->>%L AS %I,
                                                                (jsonb_array_elements(%I->%L)->%L->>%L)::integer AS %I) %s',
                                          primary_field,
                                          'V',
                                          '_text',
                                          primary_field,
                                          primary_field,
                                          'V',
                                          '_attributes',
                                          'Idx',
                                          primary_field || '_id',
                                          lower(primary_field));
        fields := array_remove(fields, primary_field || '[]');
    END IF;

    FOREACH fld IN ARRAY fields
    LOOP
        IF (fld ~ '\[') AND jsonb_table_name <> 'etl_head' THEN
            tlist := tlist || format(', %s.*', substring(fld, 1, length(fld) - 2));

            from_list := from_list || format(' LEFT JOIN LATERAL (
                                                   SELECT jsonb_array_elements(%I->%L)->>%L AS %I,
                                                          (jsonb_array_elements(%I->%L)->%L->>%L)::integer AS %I) %s
                                                   ON %s.%I = %s.%I',
                                               substring(fld, 1, length(fld) - 2),
                                               'V',
                                               '_text',
                                               substring(fld, 1, length(fld) - 2),
                                               substring(fld, 1, length(fld) - 2),
                                               'V',
                                               '_attributes',
                                               'Idx',
                                               substring(fld, 1, length(fld) - 2) || '_id',
                                               substring(lower(fld), 1, length(fld) - 2),
                                               lower(primary_field),
                                               primary_field || '_id',
                                               substring(lower(fld), 1, length(fld) - 2),
                                               substring(fld, 1, length(fld) - 2) || '_id');
        ELSIF (fld ~ '\[') AND jsonb_table_name = 'etl_head' THEN
            RAISE EXCEPTION '% - Array fields are not expecting in Head table', substring(fld, 1, length(fld) - 2);
        ELSE
            tlist := tlist || format(', %I', fld);
        END IF;
    END LOOP;
    RETURN QUERY EXECUTE 'SELECT ' || tlist || from_list;

END;
$$;

CREATE OR REPLACE FUNCTION build_etl_table(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE created_table_name text; table_builder text; model_cols record; table_prefix text;
BEGIN
    table_prefix := CASE WHEN lower(target_table) = 'other' THEN 'proxy_' ELSE 'etl_' END;
    created_table_name := table_prefix || lower(target_table);
    table_builder := format('CREATE TABLE %I ( "roNumber" text NOT NULL', created_table_name);
    FOR model_cols IN EXECUTE format('SELECT * FROM du_dms_reynoldsrci_model.etl_model WHERE target_table = %L AND field_name <> %L', target_table, 'roNumber')
    LOOP
        table_builder := table_builder || format(', %I %s', model_cols.field_name, case when model_cols.is_array then 'jsonb' else 'text' end);
    END LOOP;

    table_builder := table_builder || ' );';
    EXECUTE format('DROP TABLE IF EXISTS %I CASCADE;', created_table_name);
    EXECUTE table_builder;
    RETURN created_table_name;
END;
$func$;

CREATE OR REPLACE FUNCTION create_jq_transform_from_model(target_table text)
RETURNS text
LANGUAGE plpgsql
STRICT
AS $func$
DECLARE result_json text;
BEGIN
    SELECT format(E'{\n%s\n}', field_set) AS jqtrans
      INTO result_json
      FROM (
           SELECT string_agg(field_name, E',\n') AS field_set
             FROM du_dms_reynoldsrci_model.etl_model
            WHERE etl_model.target_table = create_jq_transform_from_model.target_table
           ) AS consolidate;

    RETURN result_json;
END;
$func$;

CREATE OR REPLACE FUNCTION assert_not_null(val_to_test anyelement, message_if_null text)
RETURNS anyelement
LANGUAGE plpgsql
AS $func$
BEGIN
    IF val_to_test IS NULL
    THEN
        RAISE EXCEPTION '%', message_if_null;
    ELSE
        RETURN val_to_test;
    END IF;
END;
$func$;
