BEGIN;
\pset pager off
  \o 'Misc-Job-A-exception.csv'
        COPY (
                WITH custro AS (
                    SELECT DISTINCT "roNumber" FROM "du_dms_reynoldsrci_model".etl_parts_detail WHERE "PayType" = 'Cust'
                    UNION
                    SELECT DISTINCT "roNumber" FROM "du_dms_reynoldsrci_model".etl_job_detail WHERE "PayType" = 'Cust'
                ),
                mytable AS (
                    SELECT "MiscCode", "MiscCodeDesc",coalesce("CodeAmt",'0.00') as "Amount", count(*), 
                            row_number() OVER (PARTITION BY "MiscCode"  ORDER BY count(*) DESC) AS rank                             
                    FROM "du_dms_reynoldsrci_model".etl_misc_detail WHERE "MiscCode" IS NOT NULL 
                    AND "roNumber" IN (SELECT "roNumber" FROM custro) 
                    GROUP BY "MiscCode", "MiscCodeDesc", coalesce("CodeAmt",'0.00') 
                    ORDER BY  "MiscCode"
                )
                SELECT "MiscCode", "MiscCodeDesc", SUM(count::numeric) AS "Total Count", 
                    (SELECT COUNT(DISTINCT "roNumber") FROM custro) as "Total RO",
                    STRING_AGG("Amount" || '(' || count || ')', ', ') AS "Summary"  
                FROM mytable WHERE rank < 12 GROUP BY  "MiscCode", "MiscCodeDesc" ORDER BY  "Total Count" DESC
         ) TO stdout WITH (FORMAT csv, HEADER true);
        INSERT INTO du_dms_reynoldsrci_model.etl_misc_job_a_detail
        WITH custro AS (
            SELECT DISTINCT "roNumber" FROM "du_dms_reynoldsrci_model".etl_parts_detail WHERE "PayType" = 'Cust'
            UNION
            SELECT DISTINCT "roNumber" FROM "du_dms_reynoldsrci_model".etl_job_detail WHERE "PayType" = 'Cust'
        ),
        mytable AS (
            SELECT "MiscCode", "MiscCodeDesc", count(*),
            row_number() OVER (PARTITION BY "MiscCode"  ORDER BY count(*) DESC) AS rank,
            coalesce("CodeAmt",'0.00') as amt                           
            FROM "du_dms_reynoldsrci_model".etl_misc_detail WHERE "MiscCode" IS NOT NULL 
            AND "roNumber" IN (SELECT "roNumber" FROM custro) --AND coalesce("CodeAmt",'0.00')::numeric = 0
            GROUP BY "MiscCode", "MiscCodeDesc", coalesce("CodeAmt",'0.00') ORDER BY  "MiscCode"
        )
        SELECT "MiscCode","MiscCodeDesc","count" FROM mytable WHERE rank < 10 AND count > 5  AND amt::numeric = 0 ORDER BY count DESC;
COMMIT;
