--# Identifies kit sales and then, by proximity, related costs items
--    and assigning a common job id to them.

UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET kit_pseudo_job = kit_job_id
FROM (
         WITH kitsource AS (
             SELECT *
             FROM (
                      SELECT
                          "SeqNo"                        AS recordindex,
                          "roNumber",
                          coalesce("DlrCost", '0.00')    AS "DlrCost",
                          coalesce("CustPrice", '0.00')  AS "CustPrice",
                          row_number()
                          OVER (
                              ORDER BY "roNumber" )      AS orderingcolumn,
                          coalesce(lag("DlrCost")
                                   OVER rec_by_inv, '0') AS prior_item_cost,
                          coalesce(lag("CustPrice")
                                   OVER rec_by_inv, '0') AS prior_item_sale,
                          coalesce(lead("DlrCost")
                                   OVER rec_by_inv, '0') AS next_item_cost,
                          coalesce(lead("CustPrice")
                                   OVER rec_by_inv, '0') AS next_item_sale
                      FROM :IMPORT_SCHEMA.etl_parts_detail
                      WINDOW rec_by_inv AS (
                          PARTITION BY "roNumber"
                          ORDER BY "SeqNo" )
                  ) src
             --only one of the amounts can be zero (not neither and we've already removed both)
             WHERE ("CustPrice" = '0.00') != ("DlrCost" = '0.00') --XOR
                   --but, it is possible for random single-zero items to be present in the data
                   --so we further stipulate that sale items must be followed by a zero cost
                   --and also (OR-clause) that cost items must be preceded by either a zero sale or zero cost
                   AND (
                       ("CustPrice" <> '0.00' AND next_item_sale :: numeric = 0 AND next_item_cost :: numeric <> 0)
                       OR
                       ("DlrCost" <> '0.00' AND (prior_item_cost :: numeric = 0 OR prior_item_sale :: numeric = 0))
                   )
         ),
                 ordereddetail AS (
                 --ensure ordered source detail; assumes the sale precedes the cost(s) and that they are contigiuous
                 SELECT
                     *,
                     row_number()
                     OVER (
                         ORDER BY orderingcolumn ) AS ordered_index
                 FROM kitsource
             ),
                 maxdetailrow AS (
                 --allows us to assign an endpoint for the last row of sale item bounds
                 SELECT max(ordered_index)
                 FROM ordereddetail
             ),
                 calculatekitbounds AS (
                 --when ordered the kit bounds are from the current kit's ordered index
                 --up to, but not including, the next kit
                 --where the last kit simply uses the largest of the
                 --known indicies in the ordered detail
                 --we are only dealing with job assignment here so discard
                 --part and amount info - leaving only ID and Job related column
                 SELECT
                     recordindex                   AS recordindex_sale,
                     ordered_index                 AS current_job_start,
                     COALESCE(lead(ordered_index)
                              OVER (
                                  ORDER BY ordered_index ) - 1, --not inclusive of the final item
                              (SELECT max
                               FROM maxdetailrow)
                     )                             AS current_job_end,
                     ROW_NUMBER()
                     OVER (
                         PARTITION BY "roNumber" ) AS job_index_on_invoice
                 FROM ordereddetail
                 WHERE "CustPrice" <> '0.00'
             )
         --then simply list the partdetailid and the newly associated jobid for pushing back onto magepartdetail_staging
         SELECT
             recordindex                     AS pid,
             job_index_on_invoice :: varchar AS kit_job_id,
             "roNumber"
         FROM calculatekitbounds
             JOIN ordereddetail ON (ordered_index BETWEEN current_job_start AND current_job_end)
     ) src
WHERE "SeqNo" = pid
      AND src."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber";
