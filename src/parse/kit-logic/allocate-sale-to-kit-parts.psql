UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET allocated_sale_amount = item_sale_adjustment
FROM (

         WITH kit_cost_raw AS (
             SELECT
                 mpd."SeqNo"                  AS recordindex,
                 mpd."roNumber",
                 mpd."PartKit",
                 mpd."DlrCost",
                 ceil(coalesce(mpd."CustQtyShip", mpd."WarrQtyShip", mpd."IntrQtyShip") :: numeric) as qty,
                 SUM(mpd."DlrCost" :: numeric * (ceil(coalesce(mpd."CustQtyShip", mpd."WarrQtyShip", mpd."IntrQtyShip") :: numeric) :: integer))
                 OVER (
                     PARTITION BY mpd."roNumber",
                         mpd."PartKit" ) AS kit_cost,
                 COUNT(*)
                 OVER inv_job                 AS kit_part_count,
                 ROW_NUMBER()
                 OVER inv_job :: integer      AS item_index
             FROM :IMPORT_SCHEMA.etl_parts_detail mpd
             WHERE "PartKit" IS NOT NULL
                   AND coalesce("DlrCost" :: numeric, 0) <> 0.00
             WINDOW inv_job AS (
                 PARTITION BY mpd."roNumber", mpd."PartKit"
                 ORDER BY mpd."SeqNo" )
         ),
                 kit_cost AS (
                 SELECT
                     *,
                     (kit_cost_raw."DlrCost" :: numeric * qty / kit_cost) AS item_sale_share
                 FROM kit_cost_raw WHERE kit_cost <> 0
             ),
                 kit_sale AS (
                 SELECT
                     mpd."roNumber",
                     mpd."PartKit",
                     mpd."CustPrice"
                 FROM :IMPORT_SCHEMA.etl_parts_detail mpd
                 WHERE mpd."PartKit" IS NOT NULL
                       AND coalesce("CustPrice" :: numeric, 0) <> 0.00
                       AND coalesce("DlrCost" :: numeric, 0) = 0.00
                       AND EXISTS(SELECT 1
                                  FROM kit_cost
                                  WHERE (mpd."roNumber", mpd."PartKit") =
                                        (kit_cost."roNumber", kit_cost."PartKit"))
             ),
                 matched_kit_full AS (
                 SELECT
                     *,
                     (kit_sale."CustPrice" :: numeric *
                      kit_cost.item_sale_share) :: numeric(20, 2)                    AS item_sale_allocation,
                     SUM((kit_sale."CustPrice" :: numeric * kit_cost.item_sale_share) :: numeric(20, 2))
                     OVER (
                         PARTITION BY kit_cost."roNumber", kit_cost."PartKit" ) AS allocated_sale
                 FROM kit_cost
                     NATURAL JOIN kit_sale
             )
             , update_detail AS (
             SELECT
                 *,
                 -- The first parts get any excess pennies needed to compensate for rounding
                 CASE WHEN item_index = 1
                     THEN item_sale_allocation + (mkf."CustPrice" :: numeric - allocated_sale)
                 ELSE item_sale_allocation
                 END AS item_sale_adjustment
             FROM matched_kit_full mkf
             ORDER BY mkf."roNumber",
                 mkf."PartKit",
                 mkf.item_index
         )
         SELECT
             ud.recordindex,
             ud."roNumber",
             ud."PartKit",
             ud.item_sale_adjustment
         FROM update_detail ud
     ) src
WHERE src.recordindex = :IMPORT_SCHEMA.etl_parts_detail."SeqNo"
      AND src."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber";

UPDATE :IMPORT_SCHEMA.etl_parts_detail
SET allocated_kit_sale_item = TRUE
WHERE EXISTS(SELECT 1
             FROM :IMPORT_SCHEMA.etl_parts_detail AS pdc
             WHERE pdc.allocated_sale_amount IS NOT NULL
                   AND (pdc."roNumber", pdc."PartKit", pdc."SeqNo") =
                       (etl_parts_detail."roNumber", :IMPORT_SCHEMA.etl_parts_detail."PartKit", :IMPORT_SCHEMA.etl_parts_detail."SeqNo"));


 UPDATE :IMPORT_SCHEMA.etl_parts_detail
 SET allocated_sale_amount = cust_price,
 allocated_kit_sale_item = true
 FROM (

         WITH kit_sale_raw AS (
              SELECT * from :IMPORT_SCHEMA.etl_parts_detail
              WHERE "PartKit" IS NOT NULL AND "PartNo" = "PartKit"
                    AND coalesce("CustPrice" :: numeric, 0) = 0.00
          )
          ,kit_sale AS (
                SELECT "roNumber", 
                    SUM((coalesce("CustPrice" :: numeric, 0)::numeric * 
                            coalesce("CustQtyShip", "WarrQtyShip","IntrQtyShip") :: numeric) :: numeric(20, 2)) AS cust_price 
                FROM :IMPORT_SCHEMA.etl_parts_detail
                WHERE "PartKit" IS NOT NULL AND "PartNo" <> "PartKit"
                    AND coalesce("CustPrice" :: numeric, 0) <> 0.00 
                GROUP BY "roNumber", "JobNo", "PartKit"
          )
        
          SELECT kr.*, ks.cust_price FROM kit_sale_raw kr JOIN kit_sale ks ON   kr."roNumber" = ks."roNumber" AND  kr."JobNo" = kr."JobNo" AND  kr."PartKit" =  kr."PartKit"
      ) src
 WHERE src."JobNo" = :IMPORT_SCHEMA.etl_parts_detail."JobNo"  AND src."PartKit" = :IMPORT_SCHEMA.etl_parts_detail."PartNo" 
       AND src."roNumber" = :IMPORT_SCHEMA.etl_parts_detail."roNumber";
