
DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_gog_job ( ro_num text, gog_info json) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.insert_gog_job ( ro_num text, gog_info json)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    gog_info_type text;
    gog json;
BEGIN
    IF gog_info ISNULL THEN
        RETURN;
    END IF;

    gog_info_type = json_typeof(gog_info);
    IF gog_info_type = 'array' THEN
        FOR gog IN
        SELECT
            *
        FROM
            json_array_elements(gog_info)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.insert_gog_detail (ro_num, gog);
            END LOOP;
    ELSIF gog_info_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.insert_gog_detail(ro_num, gog_info);
    END IF;
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_gog_detail
( ro_num text, gog_info json) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.insert_gog_detail ( ro_num text, gog_info json)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    gog_info_type text;
    gog_info_array_temp json;
    gog_info_array jsonb;
	
	gog_detail_info_type text;
    gog_detail json; 
    gog_detail_array_temp jsonb;
	gog_detail_array jsonb;


BEGIN
    IF gog_info ISNULL THEN
    RETURN;
END
IF;
    gog_info_type = json_typeof
(gog_info);

IF gog_info_type = 'array' THEN
SELECT
    gog_info::json
INTO gog_info_array;
ELSIF gog_info_type = 'object' THEN
SELECT
    JSON_BUILD_ARRAY(gog_info::json)
INTO gog_info_array;
END
IF;
	IF gog_info -> 'AllGogLineItmInfo' IS NOT NULL THEN
        gog_detail_info_type = json_typeof
(gog_info -> 'AllGogLineItmInfo');
SELECT gog_info -> 'AllGogLineItmInfo'
INTO gog_detail;

IF gog_detail_info_type = 'array' THEN
SELECT
    gog_detail
INTO gog_detail_array_temp;
ELSIF gog_detail_info_type = 'object' THEN
SELECT
    JSON_BUILD_ARRAY(gog_detail)
INTO gog_detail_array_temp;
END
IF;
		  WITH
    CTE1
    AS
    (
        SELECT
            t || jsonb_build_object('roNumber', trim(BOTH '"'
        FROM ro_num
    ), 'JobNo',  gog_info ->> 'JobNo', 'OpCode', gog_info ->> 'OpCode')
					AS "array_elemnts"
				FROM 
					jsonb_array_elements
(gog_detail_array_temp) AS t 
		),
		CTE11 AS
( 
			SELECT
    jsonb_agg("array_elemnts") AS "json_elemnts"
FROM
    CTE1 
		)
,
		CTE2 AS
( 
			SELECT
    "json_elemnts"::json jsn1
FROM
    CTE11 
		)
SELECT jsn1
INTO gog_detail_array
FROM CTE2;
END
IF; 
WITH
    CTE20
    AS
    
    (
        SELECT
            *
        FROM
            json_to_recordset(gog_detail_array
::json) AS elem (
	            "roNumber" text,
	            "JobNo" text, 
                "OpCode" text,
				"RoAmts" json,
				"CustQty" text,
                "IntrQty" text,
                "WarrQty" text,
				"ItemDesc" text,
				"ItemType" text,
				"CustPayTypeFlag" text,
                "IntrPayTypeFlag" text,
                "WarrPayTypeFlag" text ) 
)
INSERT INTO  du_dms_reynoldsrci_model.etl_gog_detail(
    "roNumber",
    "JobNo", 
    "OpCode", 
    "CustPayTypeFlag", 
    "CustQty",
    "ItemDesc", 
    "ItemType",
    "DlrCost",
    "PayType", 
    "AmtType",
    "CustPrice"
    )
SELECT 
    "roNumber",
    "JobNo" ,
    "OpCode",
    case when "CustPayTypeFlag" is NOT NULL then "CustPayTypeFlag" 
    when "IntrPayTypeFlag" is NOT NULL then "IntrPayTypeFlag" 
    when "WarrPayTypeFlag" is NOT NULL then "WarrPayTypeFlag" end "CustPayTypeFlag",
    case when "CustQty" is NOT NULL then "CustQty" 
    when "IntrQty" is NOT NULL then "IntrQty" 
    when "WarrQty" is NOT NULL then "WarrQty" end "CustQty",
    "ItemDesc",
    "ItemType",
    "RoAmts" ->> 'DlrCost',
    "RoAmts" ->> 'PayType',
    "RoAmts" ->> 'AmtType',
    "RoAmts" ->> 'CustPrice'
FROM CTE20;
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_sublet_detail
( ro_num text, sublet_info json) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.insert_sublet_detail ( ro_num text, sublet_info json)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
 
	sublet_detail_info_type text;
    sublet_detail json; 
    sublet_detail_array_temp jsonb;

BEGIN
    IF sublet_info ISNULL THEN
    RETURN;
END
IF;
 
sublet_detail_info_type = json_typeof
(sublet_info);
SELECT sublet_info
INTO sublet_detail;

IF sublet_detail_info_type = 'array' THEN
SELECT
    sublet_detail
INTO sublet_detail_array_temp;
ELSIF sublet_detail_info_type = 'object' THEN
SELECT
    JSON_BUILD_ARRAY(sublet_detail)
INTO sublet_detail_array_temp;
END
IF;
	
WITH
    CTE20
    AS
    (
        SELECT
            trim(BOTH '"'
        FROM ro_num
    ) AS "roNumber",
            *		  
        FROM
            json_to_recordset
(sublet_detail_array_temp
::json) AS elem
(
	            "JobNo" text, 
                "OpCode" text,
				"SubDetail" json
                ) 
)
INSERT INTO  du_dms_reynoldsrci_model.etl_sublet_detail
    (
    "roNumber",
    "JobNo",
    "OpCode",
    "PoRecDate",
    "PoDate",
    "SubletPoNo",
    "SubletVendNo",
    "VendorInvNo",
    "SubletDesc",
    "DlrCost",
    "PayType",
    "AmtType",
    "CustPrice"
    )
SELECT
    "roNumber",
    "JobNo",
    "OpCode",
    "SubDetail" ->> 'PoRecDate',
    "SubDetail" ->> 'PoDate',
    "SubDetail" ->> 'SubletPoNo',
    "SubDetail" ->> 'SubletVendNo',
    "SubDetail" ->> 'VendorInvNo',
    "SubDetail" ->> 'SubletDesc',
    "SubDetail" -> 'RoAmts' ->> 'DlrCost',
    "SubDetail" -> 'RoAmts' ->> 'PayType',
    "SubDetail" -> 'RoAmts' ->> 'AmtType',
    "SubDetail" -> 'RoAmts' ->> 'CustPrice'
FROM CTE20;
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_jobs (ro_num text, jobs json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_jobs (ro_num text, jobs json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    job json;
    input_type text;
BEGIN
    IF jobs ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(jobs);
    IF input_type = 'array' THEN
        FOR job IN
        SELECT
            *
        FROM
            json_array_elements(jobs)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.job_insertion (job, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.job_insertion (jobs, ro_num);
    END IF;
END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.job_insertion (job json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.job_insertion (job json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    tech_type text;
    tech json;
    tech_array json;
    totaltechhrs text;
    spg_info_type text;
    spg_info_tmp  json;
    spg_info_array json;
BEGIN
    IF job ISNULL THEN
        RETURN;
    END IF;


    IF json_typeof(job -> 'TechInfo') = 'array' THEN
        SELECT	
        SUM("TechHrs") INTO totaltechhrs
        FROM   json_to_recordset(job -> 'TechInfo'
        ) AS elem ("ActualHrsWorked" text, "CustTechRate" text, "TechHrs" numeric, "TechName" text, "TechNo" text);
    ELSIF json_typeof(job -> 'TechInfo') = 'object' THEN
        SELECT	
        SUM("TechHrs") INTO totaltechhrs
        FROM   json_to_record(job -> 'TechInfo'
        ) AS elem ("ActualHrsWorked" text, "CustTechRate" text, "TechHrs" numeric, "TechName" text, "TechNo" text);
    END IF;


    INSERT INTO du_dms_reynoldsrci_model.etl_job_detail ("roNumber", "JobNo", "JobStatus", "UpSellFlag", "OpCode", "OpCodeDesc", "BillRate", "BillTime", "OrigCorrection", "OrigCause", "Correction", "Cause", "Complaint", "DlrCost", "PayType", "AmtType", "TotalAmt", "BillTime_Tech", "SplitCost", "SplitTotal")
    WITH CTE AS (
        SELECT
            trim(BOTH '"' FROM ro_num) AS ro_number,
            *
        FROM
            json_to_record(job) AS elem ("JobStatus" text,
                "UpSellFlag" text,
                "JobNo" text,
                "OpCode" text,
                "OpCodeDesc" text,
                "BillTimeRateHrs" json,
                "CCCStmts" json,
                "RoAmts" json))
    SELECT
        trim(BOTH '"' FROM ro_number),
        "JobNo",
        "JobStatus",
        "UpSellFlag",
        "OpCode",
        "OpCodeDesc",
        "BillTimeRateHrs" ->> 'BillRate',
         CASE WHEN "BillTimeRateHrs" ->> 'BillTime':: text IS NOT NULL THEN
         "BillTimeRateHrs" ->> 'BillTime'
        ELSE
           totaltechhrs
        END "BillTime",
        CASE WHEN json_typeof("CCCStmts") = 'array' THEN
            (SELECT string_agg("origcorrection", '~')
            FROM
                (SELECT
                    origcorrection
                FROM (
            SELECT
                        "OrigCorrection" AS origcorrection, ROW_number() OVER () AS rank
                    FROM
                        json_to_recordset("CCCStmts") AS elem ("OrigCorrection" text) 
            ) t
            ORDER BY RANK ASC
            ) q)
        WHEN json_typeof("CCCStmts") = 'object' THEN
            (SELECT string_agg("origcorrection", '~')
            FROM
                (SELECT
                    origcorrection
                FROM (
            SELECT
                        "OrigCorrection" AS origcorrection, ROW_number() OVER () AS rank
                    FROM
                        json_to_record("CCCStmts") AS elem ("OrigCorrection" text) 
            ) t
            ORDER BY RANK ASC
            ) q)
        END origcorrection,
        CASE WHEN json_typeof("CCCStmts") = 'array' THEN
        (
        SELECT string_agg("origcause", '~')
        FROM
            (SELECT
                origcause
            FROM (
        SELECT
                    "OrigCause" AS origcause, ROW_number() OVER () AS rank
                FROM
                    json_to_recordset("CCCStmts") AS elem ("OrigCause" text) 
        ) t
        ORDER BY RANK ASC
        ) q
        )
        WHEN json_typeof("CCCStmts") = 'object' THEN
        (
       SELECT string_agg("origcause", '~')
        FROM
            (SELECT
                origcause
            FROM (
        SELECT
                    "OrigCause" AS origcause, ROW_number() OVER () AS rank
                FROM
                    json_to_record("CCCStmts") AS elem ("OrigCause" text) 
        ) t
        ORDER BY RANK ASC
        ) q
        )
        END origcause,
        CASE WHEN json_typeof("CCCStmts") = 'array' THEN
            (SELECT string_agg("correction", '~')
            FROM
                (SELECT
                    correction
                FROM (
            SELECT
                        "Correction" AS correction, ROW_number() OVER () AS rank
                    FROM
                        json_to_recordset("CCCStmts") AS elem ("Correction" text) 
            ) t
            ORDER BY RANK ASC
            ) q)
        WHEN json_typeof("CCCStmts") = 'object' THEN
            (SELECT string_agg("correction", '~')
            FROM
                (SELECT
                    correction
                FROM (
            SELECT
                        "Correction" AS correction, ROW_number() OVER () AS rank
                    FROM
                        json_to_record("CCCStmts") AS elem ("Correction" text) 
            ) t
            ORDER BY RANK ASC
            ) q)
        END correction,
        CASE WHEN json_typeof("CCCStmts") = 'array' THEN
    (
        SELECT string_agg("cause", '~')
        FROM
            (SELECT
                cause
            FROM (
        SELECT
                    "Cause" AS cause, ROW_number() OVER () AS rank
                FROM
                    json_to_recordset("CCCStmts") AS elem ("Cause" text) 
        ) t
        ORDER BY RANK ASC
        ) q
    )
        WHEN json_typeof("CCCStmts") = 'object' THEN
    (
       SELECT string_agg("cause", '~')
        FROM
            (SELECT
                cause
            FROM (
        SELECT
                    "Cause" AS cause, ROW_number() OVER () AS rank
                FROM
                    json_to_record("CCCStmts") AS elem ("Cause" text) 
        ) t
        ORDER BY RANK ASC
        ) q
    )
        END cause,
        CASE WHEN json_typeof("CCCStmts") = 'array' THEN
    (
        SELECT string_agg("complaint", '~')
        FROM
            (SELECT
                complaint
            FROM (
        SELECT
                    "Complaint" AS complaint, ROW_number() OVER () AS rank
                FROM
                    json_to_recordset("CCCStmts") AS elem ("Complaint" text) 
        ) t
        ORDER BY RANK ASC
        ) q
    )
        WHEN json_typeof("CCCStmts") = 'object' THEN
    (
        SELECT string_agg("complaint", '~')
        FROM
            (SELECT
                complaint
            FROM (
        SELECT
                    "Complaint" AS complaint, ROW_number() OVER () AS rank
                FROM
                    json_to_record("CCCStmts") AS elem ("Complaint" text) 
        ) t
        ORDER BY RANK ASC
        ) q
    )
        END complaint,
        CASE WHEN json_typeof("RoAmts") = 'array' THEN
            (
            select sum( "DlrCost"::numeric)::text from( SELECT
                    "DlrCost",
                    "PayType"
                FROM
                    json_to_recordset("RoAmts") AS elem ("DlrCost" text,
                                                        "PayType" text))t 
                where "PayType"='Cust') 
            WHEN json_typeof("RoAmts") = 'object' THEN
            (
                SELECT
                    string_agg("DlrCost", ',')
                FROM
                    json_to_record("RoAmts") AS elem ("DlrCost" text))
                END DlrCost,
          CASE WHEN json_typeof("RoAmts") = 'array' THEN
    (
        SELECT
            string_agg("PayType", ',')
        FROM
            json_to_recordset("RoAmts") AS elem ("PayType" text))
        WHEN json_typeof("RoAmts") = 'object' THEN
    (
        SELECT
            string_agg("PayType", ',')
        FROM
            json_to_record("RoAmts") AS elem ("PayType" text))
        END PayType,        
        CASE WHEN json_typeof("RoAmts") = 'array' THEN
    (
        SELECT
            string_agg("AmtType", ',')
        FROM
            json_to_recordset("RoAmts") AS elem ("AmtType" text))
        WHEN json_typeof("RoAmts") = 'object' THEN
    (
        SELECT
            string_agg("AmtType", ',')
        FROM
            json_to_record("RoAmts") AS elem ("AmtType" text))
        END AmtType,        
        CASE WHEN json_typeof("RoAmts") = 'array' THEN
     (
       select sum( "TotalAmt"::numeric)::text from( SELECT
            "TotalAmt",
            "PayType"
        FROM
            json_to_recordset("RoAmts") AS elem ("TotalAmt" text,
                                                 "PayType" text))t 
        where "PayType"='Cust') 
       WHEN json_typeof("RoAmts") = 'object' THEN
    (
        SELECT
            string_agg("TotalAmt", ',')
        FROM
            json_to_record("RoAmts") AS elem ("TotalAmt" text))
        END TotalAmt,
      "BillTimeRateHrs" ->> 'BillTime',
        CASE WHEN json_typeof("RoAmts") = 'array' THEN
    (
        SELECT
            string_agg("DlrCost", ',')
        FROM
            json_to_recordset("RoAmts") AS elem ("DlrCost" text))
        WHEN json_typeof("RoAmts") = 'object' THEN
    (
        SELECT
            string_agg("DlrCost", ',')
        FROM
            json_to_record("RoAmts") AS elem ("DlrCost" text))
        END SplitCost,        
         CASE WHEN json_typeof("RoAmts") = 'array' THEN
    (
        SELECT
            string_agg("TotalAmt", ',')
        FROM
            json_to_recordset("RoAmts") AS elem ("TotalAmt" text))
        WHEN json_typeof("RoAmts") = 'object' THEN
    (
        SELECT
            string_agg("TotalAmt", ',')
        FROM
            json_to_record("RoAmts") AS elem ("TotalAmt" text))
        END SplitTotal    
    FROM
        CTE;
    IF job -> 'TechInfo' ISNULL THEN
        RETURN;
    ELSE
        tech_type = json_typeof(job -> 'TechInfo');
        IF tech_type = 'array' THEN
            tech_array = job -> 'TechInfo';
            FOR tech IN
            SELECT
                *
            FROM
                json_array_elements(tech_array)
                LOOP
                    PERFORM
                        du_dms_reynoldsrci_model.tech_insertion (tech, ro_num, job ->> 'JobNo');
                END LOOP;
        ELSIF tech_type = 'object' THEN
            PERFORM
                du_dms_reynoldsrci_model.tech_insertion (job -> 'TechInfo', ro_num, job ->> 'JobNo');
        END IF;
    END IF;

   IF job -> 'SPGInfo' ISNULL THEN
        RETURN;
   ELSE
        spg_info_type = json_typeof
        (job -> 'SPGInfo');
        SELECT job -> 'SPGInfo'
        INTO spg_info_tmp;
        IF spg_info_type = 'array' THEN
            SELECT spg_info_tmp :: JSON
            INTO spg_info_array;
        ELSIF spg_info_type = 'object' THEN
            SELECT JSON_BUILD_ARRAY(spg_info_tmp :: JSON)
            INTO spg_info_array;
        END IF;

        INSERT INTO du_dms_reynoldsrci_model.etl_spg_detail
        SELECT
            trim(BOTH '"' FROM ro_num) AS ro_number,
                job ->> 'JobNo',
                "SPGOpCode", 
                "SPGOpCodeDesc"
                FROM
                    (
                    SELECT "SPGOpCode", "SPGOpCodeDesc"
                    FROM json_to_recordset(spg_info_array) AS specs("SPGOpCode"
                    text, "SPGOpCodeDesc" text)
                )t;
   END IF;
        
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_rec_serv (ro_num text, recom_info json) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.insert_rec_serv ( ro_num text, recom_info json)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    rec_info_type text;
    recser json;
BEGIN
    IF recom_info ISNULL THEN
        RETURN;
    END IF;

    rec_info_type = json_typeof(recom_info);
    IF rec_info_type = 'array' THEN
        FOR recser IN
        SELECT
            *
        FROM
            json_array_elements(recom_info)
            LOOP
                INSERT INTO du_dms_reynoldsrci_model.etl_recommended_service_detail ("roNumber", "rec_serv_flag", "rec_serv_desc", "rec_serv_code")
        VALUES (trim(BOTH '"' FROM ro_num),  recser ->> 'RecSvcPerformFlag',recser ->> 'RecSvcOpCdDesc', recser ->> 'RecSvcOpCode');
            END LOOP;
    ELSIF rec_info_type = 'object' THEN
        
        INSERT INTO du_dms_reynoldsrci_model.etl_recommended_service_detail ("roNumber", "rec_serv_flag", "rec_serv_desc", "rec_serv_code")
        VALUES (trim(BOTH '"' FROM ro_num),  recom_info ->> 'RecSvcPerformFlag',recom_info ->> 'RecSvcOpCdDesc', recom_info ->> 'RecSvcOpCode');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.tech_insertion (tech json, ro_num text, ID text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.tech_insertion (tech json, ro_num text, ID text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF tech ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_tech_detail ("roNumber", "JobNo", "TechNo", "TechName", "TechHrs", "WarrTechRate", "CustTechRate", "IntrTechRate", "ActualHrsWorked", "JobWorkDate", "TechStartTime", "TechFinishTime")
        VALUES (trim(BOTH '"' FROM ro_num), ID, tech ->> 'TechNo', tech ->> 'TechName', tech ->> 'TechHrs', tech ->> 'WarrTechRate', tech ->> 'CustTechRate', tech ->> 'IntrTechRate', tech ->> 'ActualHrsWorked', tech ->> 'JobWorkDate', tech ->> 'TechStartTime', tech ->> 'TechFinishTime');
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_parts (ro_num text, parts json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_parts (ro_num text, parts json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    part json;
    input_type text;
BEGIN
    IF parts ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(parts);
    IF input_type = 'array' THEN
        FOR part IN
        SELECT
            *
        FROM
            json_array_elements(parts)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.part_insertion (part, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.part_insertion (parts, ro_num);
    END IF;
END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.part_insertion (part json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.part_insertion (part json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    part_detail_type text;
    part_detail json;
    part_detail_array json;
BEGIN
    IF part ISNULL THEN
        RETURN;
    END IF;
    IF part -> 'PartDetail' ISNULL THEN
        RETURN;
    END IF;
    part_detail_type = json_typeof(part -> 'PartDetail');
    IF part_detail_type = 'array' THEN
        FOR part_detail IN
        SELECT
            *
        FROM
            json_array_elements(part -> 'PartDetail')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.part_detail_insertion (part_detail, ro_num, part ->> 'JobNo', part ->> 'OpCode');
            END LOOP;
    ELSIF part_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.part_detail_insertion (part -> 'PartDetail', ro_num, part ->> 'JobNo', part ->> 'OpCode');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.part_detail_insertion (part_detail json, ro_num text, jobNo text, opCode text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.part_detail_insertion (part_detail json, ro_num text, jobNo text, opCode text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    part_roamts_type text;
    tmp_roamts_array json;
    part_detail_array jsonb;
    part_detail_object_array jsonb;
BEGIN
    IF part_detail ISNULL THEN
        RETURN;
    END IF;
    IF part_detail -> 'RoAmts' ISNULL THEN
        RETURN;
    END IF;
    part_roamts_type = json_typeof(part_detail -> 'RoAmts');
    SELECT
        part_detail -> 'RoAmts' INTO tmp_roamts_array;
    IF part_roamts_type = 'array' THEN
        SELECT
            tmp_roamts_array::json INTO part_detail_array;
    ELSIF part_roamts_type = 'object' THEN
        SELECT
            JSON_BUILD_ARRAY(tmp_roamts_array::json) INTO part_detail_array;
    END IF;
    WITH CTE AS (
        SELECT
            CASE WHEN t ->> 'PayType' = 'Cust' THEN
                t || jsonb_build_object('roNumber', trim(BOTH '"' FROM ro_num), 'JobNo', jobno, 'OpCode', opcode, 'CustQtyShip', part_detail ->> 'CustQtyShip', 'SeqNo', part_detail ->> 'SeqNo', 'PartNoDesc', part_detail ->> 'PartNoDesc', 'PartNo', part_detail ->> 'PartNo', 'PartKit', part_detail ->> 'PartKit')
            WHEN t ->> 'PayType' = 'Intr' THEN
                t || jsonb_build_object('roNumber', trim(BOTH '"' FROM ro_num), 'JobNo', jobno, 'OpCode', opcode, 'CustQtyShip', part_detail ->> 'IntrQtyShip', 'SeqNo', part_detail ->> 'SeqNo', 'PartNoDesc', part_detail ->> 'PartNoDesc', 'PartNo', part_detail ->> 'PartNo', 'PartKit', part_detail ->> 'PartKit')
            WHEN t ->> 'PayType' = 'Warr' THEN
                t || jsonb_build_object('roNumber', trim(BOTH '"' FROM ro_num), 'JobNo', jobno, 'OpCode', opcode, 'CustQtyShip', part_detail ->> 'WarrQtyShip', 'SeqNo', part_detail ->> 'SeqNo', 'PartNoDesc', part_detail ->> 'PartNoDesc', 'PartNo', part_detail ->> 'PartNo', 'PartKit', part_detail ->> 'PartKit')
            END AS "array_elemnts"
        FROM
            jsonb_array_elements(part_detail_array) AS t
)
    SELECT
        jsonb_agg("array_elemnts")
    FROM
        CTE INTO part_detail_object_array;
    INSERT INTO du_dms_reynoldsrci_model.etl_parts_detail
    SELECT
        *
    FROM
        jsonb_populate_recordset(NULL::du_dms_reynoldsrci_model.etl_parts_detail, part_detail_object_array);
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_miscs (ro_num text, miscs json) CASCADE;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_miscs (ro_num text, miscs json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_miscs (ro_num text, miscs json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    misc json;
    input_type text;
BEGIN
    IF miscs ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(miscs);
    IF input_type = 'array' THEN
        FOR misc IN
        SELECT
            *
        FROM
            json_array_elements(miscs)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_insertion (misc, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_insertion (miscs, ro_num);
    END IF;
    IF input_type = 'array' THEN
        FOR misc IN
        SELECT
            *
        FROM
            json_array_elements(miscs)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_amount_insertion (misc, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_amount_insertion (miscs, ro_num);
    END IF;

END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_insertion (misc json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_insertion (misc json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    misc_detail_type text;
    misc_detail json;
    misc_detail_array json;
BEGIN
    IF misc ISNULL THEN
        RETURN;
    END IF;
    IF misc -> 'MiscLineItmInfo' ISNULL THEN
        RETURN;
    END IF;
    misc_detail_type = json_typeof(misc -> 'MiscLineItmInfo');
    IF misc_detail_type = 'array' THEN
        FOR misc_detail IN
        SELECT
            *
        FROM
            json_array_elements(misc -> 'MiscLineItmInfo')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_detail_insertion (misc_detail, ro_num, misc ->> 'JobNo');
            END LOOP;
    ELSIF misc_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_detail_insertion (misc -> 'MiscLineItmInfo', ro_num, misc ->> 'JobNo');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_amount_insertion (misc json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_amount_insertion (misc json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    misc_detail_type text;
    misc_detail json;
    misc_detail_array json;
BEGIN
    IF misc ISNULL THEN
        RETURN;
    END IF;
    IF misc -> 'RoAmts' ISNULL THEN
        RETURN;
    END IF;
    misc_detail_type = json_typeof(misc -> 'RoAmts');
    IF misc_detail_type = 'array' THEN
        FOR misc_detail IN
        SELECT
            *
        FROM
            json_array_elements(misc -> 'RoAmts')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_amount_insertion (misc_detail, ro_num, misc ->> 'JobNo');
            END LOOP;
    ELSIF misc_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_amount_insertion (misc -> 'RoAmts', ro_num, misc ->> 'JobNo');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_detail_insertion (misc_detail json, ro_num text, jobNo text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_detail_insertion (misc_detail json, ro_num text, jobNo text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF misc_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_misc_detail (
     "roNumber",
     "JobNo",
     "CodeAmt",
     "MiscCodeDesc",
     "MiscCode",
     "CustPayTypeFlag",
     "PayType",
     "AutoCalc"
    )
        VALUES (trim(BOTH '"' FROM ro_num), 
        jobNo, 
        misc_detail ->> 'CodeAmt',
        misc_detail ->> 'MiscCodeDesc',
        misc_detail ->> 'MiscCode',
        misc_detail ->> 'CustPayTypeFlag',
        CASE WHEN misc_detail ->> 'IntrPayTypeFlag' IS NOT NULL THEN 'Intr' 
             WHEN misc_detail ->> 'CustPayTypeFlag' IS NOT NULL THEN 'Cust' 
             WHEN misc_detail ->> 'WarrPayTypeFlag' IS NOT NULL THEN 'Warr' 
        END,
        misc_detail ->> 'AutoCalc'
    );
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_amount_insertion (misc_detail json, ro_num text, jobNo text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_amount_insertion (misc_detail json, ro_num text, jobNo text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF misc_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_misc_detail (
     "roNumber",
     "JobNo",
     "CodeAmt",
     "MiscCodeDesc",
     "MiscCode",
     "CustPayTypeFlag",
     "PayType"
    )
      SELECT trim(BOTH '"' FROM ro_num), 
        jobNo, 
        misc_detail ->> 'TotalAmt',
        NULL,
        NULL,
        NULL,
        misc_detail ->> 'PayType'
     WHERE (misc_detail ->> 'TotalAmt')::numeric < 0
    ;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_miscs1 (ro_num text, miscs json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_miscs1 (ro_num text, miscs json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    misc json;
    input_type text;
BEGIN
    IF miscs ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(miscs);
    IF input_type = 'array' THEN
        FOR misc IN
        SELECT
            *
        FROM
            json_array_elements(miscs)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_insertion1 (misc, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_insertion1 (miscs, ro_num);
    END IF;
    IF input_type = 'array' THEN
        FOR misc IN
        SELECT
            *
        FROM
            json_array_elements(miscs)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_amount_insertion1 (misc, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            --du_dms_reynoldsrci_model.misc_amount_insertion1 (miscs, ro_num);
            du_dms_reynoldsrci_model.misc_amount_insertion1 (misc -> 'MiscOpCodeInfo' -> 'RoAmts', ro_num, misc ->> 'JobNo');
    END IF;

END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_insertion1 (misc json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_insertion1 (misc json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    misc_detail_type text;
    misc_detail json;
    misc_detail_array json;
BEGIN
    IF misc ISNULL THEN
        RETURN;
    END IF;
    IF misc -> 'MiscOpCodeInfo' ISNULL THEN
        RETURN;
    END IF;
    misc_detail_type = json_typeof(misc -> 'MiscOpCodeInfo');
    IF misc_detail_type = 'array' THEN
        FOR misc_detail IN
        SELECT
            *
        FROM
            json_array_elements(misc -> 'MiscOpCodeInfo')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_detail_insertion1 (misc_detail, ro_num, misc ->> 'JobNo');
            END LOOP;
    ELSIF misc_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_detail_insertion1 (misc -> 'MiscOpCodeInfo', ro_num, misc ->> 'JobNo');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_amount_insertion1 (misc json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_amount_insertion1 (misc json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    misc_detail_type text;
    misc_detail json;
    misc_detail_array json;
BEGIN
    IF misc ISNULL THEN
        RETURN;
    END IF;
    IF misc -> 'RoAmts' ISNULL THEN
        RETURN;
    END IF;
    misc_detail_type = json_typeof(misc -> 'RoAmts');
    IF misc_detail_type = 'array' THEN
        FOR misc_detail IN
        SELECT
            *
        FROM
            json_array_elements(misc -> 'RoAmts')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.misc_amount_insertion1 (misc_detail, ro_num, misc ->> 'JobNo');
            END LOOP;
    ELSIF misc_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.misc_amount_insertion1 (misc ->  'RoAmts', ro_num, misc ->> 'JobNo');
    END IF;
END;
$body$;
DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_detail_insertion1 (misc_detail json, ro_num text, jobNo text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_detail_insertion1 (misc_detail json, ro_num text, jobNo text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF misc_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_misc_detail1 (
     "roNumber",
     "JobNo"
    )
        VALUES (trim(BOTH '"' FROM ro_num), 
        jobNo
    );
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.misc_amount_insertion1 (misc_detail json, ro_num text, jobNo text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.misc_amount_insertion1 (misc_detail json, ro_num text, jobNo text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF misc_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_misc_detail1 (
     "roNumber",
     "JobNo",
     "CodeAmt",
     "PayType",
     "TotalAmt"
    )
      SELECT trim(BOTH '"' FROM ro_num), 
        jobNo, 
        misc_detail -> 'CodeAmt',
        misc_detail ->> 'PayType',
        misc_detail ->> 'TotalAmt';
        --WHERE (misc_detail ->> 'TotalAmt')::numeric < 0;
END;
$body$;
DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_sublets (ro_num text, sublets json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_sublets (ro_num text, sublets json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    sublet json;
    input_type text;
BEGIN
    IF sublets ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(sublets);
    IF input_type = 'array' THEN
        FOR sublet IN
        SELECT
            *
        FROM
            json_array_elements(sublets)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.sublet_insertion (sublet, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.sublet_insertion (sublets, ro_num);
    END IF;
END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.sublet_insertion (sublet json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.sublet_insertion (sublet json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    sublet_detail_type text;
    sublet_detail json;
    sublet_detail_array json;
BEGIN
    IF sublet ISNULL THEN
        RETURN;
    END IF;
    IF sublet -> 'SubDetail' ISNULL THEN
        RETURN;
    END IF;
    sublet_detail_type = json_typeof(sublet -> 'SubDetail');
    IF sublet_detail_type = 'array' THEN
        FOR sublet_detail IN
        SELECT
            *
        FROM
            json_array_elements(sublet -> 'SubDetail')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.sublet_detail_insertion (sublet_detail, ro_num, sublet ->> 'JobNo', sublet ->> 'OpCode');
            END LOOP;
    ELSIF sublet_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.sublet_detail_insertion (sublet -> 'SubDetail', ro_num, sublet ->> 'JobNo', sublet ->> 'OpCode');
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.sublet_detail_insertion (sublet_detail json, ro_num text, jobNo text, opCode text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.sublet_detail_insertion (sublet_detail json, ro_num text, jobNo text, opCode text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF sublet_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_sublet_detail (
     "roNumber",
     "JobNo",
     "OpCode",
     "PoRecDate", 
     "PoDate",
     "SubletPoNo", 
     "SubletVendNo",
     "VendorInvNo",
     "SubletDesc",
     "DlrCost",
     "PayType",
     "AmtType",
     "CustPrice"    )
        VALUES (trim(BOTH '"' FROM ro_num), 
        jobNo,
        opCode, 
        sublet_detail ->> 'PoRecDate' ,
        sublet_detail ->> 'PoDate' ,
	    sublet_detail ->> 'SubletPoNo' ,
	    sublet_detail ->> 'SubletVendNo' ,
        sublet_detail ->> 'VendorInvNo',
	    sublet_detail ->> 'SubletDesc',
        sublet_detail -> 'RoAmts' ->> 'DlrCost',
        sublet_detail -> 'RoAmts' ->> 'PayType',
        sublet_detail -> 'RoAmts' ->> 'AmtType',
        sublet_detail -> 'RoAmts' ->> 'CustPrice'
    );
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_estimate (ro_num text, estimates json) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_estimate (ro_num text, estimates json)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    estimate json;
    input_type text;
BEGIN
    IF estimates ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(estimates);
    IF input_type = 'array' THEN
        FOR estimate IN
        SELECT
            *
        FROM
            json_array_elements(estimates)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.estimate_insertion (estimate, ro_num);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.estimate_insertion (estimates, ro_num);
    END IF;
END;
$func$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.estimate_insertion (estimate json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.estimate_insertion (estimate json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF estimate ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_estimate_details (
     "roNumber",
     "EstPartsAmt",
     "EstLaborAmt",
     "EstTotalAmt",
     "EstDate",
     "EstTime",
     "EstContact",
     "EstComment",
     "EstWriter"    )
        VALUES (trim(BOTH '"' FROM ro_num), 
        estimate ->> 'EstPartsAmt' ,
        estimate ->> 'EstLaborAmt' ,
	    estimate ->> 'EstTotalAmt' ,
	    estimate ->> 'EstDate' ,
	    estimate ->> 'EstTime',
        estimate ->> 'EstContact',
        estimate ->> 'EstComment',
        estimate ->> 'EstWriter'
    );
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_other (ro_num text, others json, otherType text) CASCADE;

CREATE OR REPLACE FUNCTION du_dms_reynoldsrci_model.insert_other (ro_num text, others json, otherType text)
    RETURNS void
    LANGUAGE plpgsql
    AS $func$
DECLARE
    other json;
    input_type text;
BEGIN
    IF others ISNULL THEN
        RETURN;
    END IF;
    input_type = json_typeof(others);
    IF input_type = 'array' THEN
        FOR other IN
        SELECT
            *
        FROM
            json_array_elements(others)
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.other_insertion (other, ro_num, otherType);
            END LOOP;
    ELSIF input_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.other_insertion (others, ro_num, otherType);
    END IF;
END;
$func$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.other_insertion (other json, ro_num text, otherType text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.other_insertion (other json, ro_num text, otherType text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    other_detail_type text;
    other_detail json;
    other_detail_array json;
BEGIN
    IF other ISNULL THEN
        RETURN;
    END IF;
    IF other -> 'RoAmts' ISNULL THEN
        RETURN;
    END IF;
    other_detail_type = json_typeof(other -> 'RoAmts');
    IF other_detail_type = 'array' THEN
        FOR other_detail IN
        SELECT
            *
        FROM
            json_array_elements(other -> 'RoAmts')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.other_detail_insertion (other_detail, ro_num, other ->> 'AllTotAmtType', otherType );
            END LOOP;
    ELSIF other_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.other_detail_insertion (other -> 'RoAmts', ro_num, other ->> 'AllTotAmtType', otherType );
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.other_detail_insertion (other_detail json, ro_num text, totalAmountType text, otherType text ) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.other_detail_insertion (other_detail json, ro_num text, totalAmountType text, otherType text )
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF other_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_other_detail (
     "roNumber",
     "Type",
     "AllTotAmtType",
     "DlrCost",
     "NTxblAmt",
     "TxblAmt",
     "PayType",
     "AmtType",
     "CustPrice"
    )
        VALUES (trim(BOTH '"' FROM ro_num), 
        otherType,
        totalAmountType,
        other_detail ->> 'DlrCost', 
        other_detail ->> 'NTxblAmt',
        other_detail ->> 'TxblAmt',
        other_detail ->> 'PayType', 
        other_detail ->> 'AmtType',
        other_detail ->> 'CustPrice'
    );
END;
$body$;





DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.insert_warranty_cliam (ro_num text, warranty_claim json) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.insert_warranty_cliam (ro_num text, warranty_claim json)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    warranty_claim_detail_type text;
    warranty_claim_detail json;
    warranty_claim_detail_array json;
BEGIN
    IF warranty_claim ISNULL THEN
        RETURN;
    END IF;
    IF warranty_claim -> 'AllWarrClaimInfo' ISNULL THEN
        RETURN;
    END IF;
    warranty_claim_detail_type = json_typeof(warranty_claim -> 'AllWarrClaimInfo');
    IF warranty_claim_detail_type = 'array' THEN
        FOR warranty_claim_detail IN
        SELECT
            *
        FROM
            json_array_elements(warranty_claim -> 'AllWarrClaimInfo')
            LOOP
                PERFORM
                    du_dms_reynoldsrci_model.warranty_claim_detail_insertion (warranty_claim_detail, ro_num);
            END LOOP;
    ELSIF warranty_claim_detail_type = 'object' THEN
        PERFORM
            du_dms_reynoldsrci_model.warranty_claim_detail_insertion (warranty_claim -> 'AllWarrClaimInfo', ro_num);
    END IF;
END;
$body$;

DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.warranty_claim_detail_insertion (warranty_claim_detail json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.warranty_claim_detail_insertion (warranty_claim_detail json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
BEGIN
    IF warranty_claim_detail ISNULL THEN
        RETURN;
    END IF;
    INSERT INTO du_dms_reynoldsrci_model.etl_warrantyclaim_detail (
    "roNumber",
    "ClaimNo",
    "ClaimDate",
    "ClaimAmt",
    "WarrClaimTxblLbrAmt",
    "WarrClaimNTxblLbrAmt",
    "WarrClaimTxblPtsAmt",
    "WarrClaimNTxblPtsAmt",
    "WarrClaimTotalGogAmt",
    "WarrClaimTotalMiscAmt",
    "WarrClaimTotalSubLbrAmt",
    "WarrClaimTotalSubPtsAmt",
    "WarrClaimTotalCoreAmt",
    "WarrClaimTotalTaxAmt"
    )
    VALUES (
    trim(BOTH '"' FROM ro_num),
    warranty_claim_detail ->> 'ClaimNo',
    warranty_claim_detail ->> 'ClaimDate',
    warranty_claim_detail ->> 'ClaimAmt',
    warranty_claim_detail ->> 'WarrClaimTxblLbrAmt',
    warranty_claim_detail ->> 'WarrClaimNTxblLbrAmt',
    warranty_claim_detail ->> 'WarrClaimTxblPtsAmt',
    warranty_claim_detail ->> 'WarrClaimNTxblPtsAmt',
    warranty_claim_detail ->> 'WarrClaimTotalGogAmt',
    warranty_claim_detail ->> 'WarrClaimTotalMiscAmt',
    warranty_claim_detail ->> 'WarrClaimTotalSubLbrAmt',
    warranty_claim_detail ->> 'WarrClaimTotalSubPtsAmt',
    warranty_claim_detail ->> 'WarrClaimTotalCoreAmt',
    warranty_claim_detail ->> 'WarrClaimTotalTaxAmt'
    );
END;
$body$;


DROP FUNCTION IF EXISTS du_dms_reynoldsrci_model.labor_detail_insertion (job json, ro_num text) CASCADE;

CREATE FUNCTION du_dms_reynoldsrci_model.labor_detail_insertion (job json, ro_num text)
    RETURNS void
    LANGUAGE plpgsql
    AS $body$
DECLARE
    bill_time_type text;
    labor_roamts_type text;
    tmp_bill_time_array json;
    tmp_labor_roamts_array json;
    bill_time_array json;
    labor_roamts_array jsonb;
    tech_info_type text;
    tmp_tech_info_array json;
    tech_id_combined text;
    tech_name_combined text;
BEGIN
    bill_time_type = json_typeof(job -> 'BillTimeRateHrs');
    SELECT
        job -> 'BillTimeRateHrs' INTO tmp_bill_time_array;
    IF bill_time_type = 'array' THEN
        SELECT
            tmp_bill_time_array::json INTO bill_time_array;
    ELSIF bill_time_type = 'object' THEN
        SELECT
            JSON_BUILD_ARRAY(tmp_bill_time_array::json) INTO bill_time_array;
    END IF;
    labor_roamts_type = json_typeof(job -> 'RoAmts');
    SELECT
        job -> 'RoAmts' INTO tmp_labor_roamts_array;
    IF labor_roamts_type = 'array' THEN
        SELECT
            tmp_labor_roamts_array::json INTO labor_roamts_array;
    ELSIF labor_roamts_type = 'object' THEN
        SELECT
            JSON_BUILD_ARRAY(tmp_labor_roamts_array::jsonb) INTO labor_roamts_array;
    END IF;
    tech_info_type = json_typeof(job -> 'TechInfo');
    SELECT
        job -> 'TechInfo' INTO tmp_tech_info_array;
    IF tech_info_type = 'array' THEN
        SELECT
            string_agg("TechName", ' '),
            string_agg("TechNo", ' ')
        FROM
            json_to_recordset(tmp_tech_info_array::json) AS elem ("TechName" text,
        "TechNo" text) INTO tech_name_combined,
    tech_id_combined;
    ELSIF tech_info_type = 'object' THEN
        SELECT
            string_agg("TechName", ' '),
            string_agg("TechNo", ' ')
        FROM
            json_to_record(tmp_tech_info_array::json) AS elem ("TechName" text,
        "TechNo" text) INTO tech_name_combined,
    tech_id_combined;
    END IF;
    WITH CTE1 AS (
        SELECT
            t || jsonb_build_object('roNumber', trim(BOTH '"' FROM ro_num), 'JobNo', job ->> 'JobNo', 'OpCode', job ->> 'OpCode', 'OpCodeDesc', job ->> 'OpCodeDesc', 'TechNo', tech_id_combined, 'TechName', tech_name_combined) AS "array_elemnts"
        FROM
            jsonb_array_elements(labor_roamts_array) AS t
),
CTE11 AS (
    SELECT
        jsonb_agg("array_elemnts") AS "json_elemnts"
    FROM
        CTE1
),
CTE2 AS (
    SELECT
        "json_elemnts"::json jsn1
    FROM
        CTE11
),
CTE3 AS (
    SELECT
        jsn_with_ordinality.*
    FROM
        CTE2,
        ROWS
    FROM (json_to_recordset(jsn1)
    AS ("DlrCost" TEXT,
    "PayType" TEXT,
    "AmtType" TEXT,
    "TotalAmt" TEXT,
    "roNumber" TEXT,
    "JobNo" TEXT,
    "OpCode" TEXT,
    "OpCodeDesc" TEXT,
    "TechNo" TEXT,
    "TechName" TEXT)) WITH ORDINALITY jsn_with_ordinality
),
CTE4 AS (
SELECT
    "bill_time_array" AS "json_elemnts_bill"
),
CTE5 AS (
    SELECT
        "bill_time_array"::json jsn2
    FROM
        CTE4
),
CTE6 AS (
    SELECT
        jsn_with_ordinality.*
    FROM
        CTE5,
        ROWS
    FROM (json_to_recordset(jsn2)
    AS ("BillTime" TEXT,
    "BillRate" TEXT)) WITH ORDINALITY jsn_with_ordinality
),
CTE7 AS (
SELECT
    *
FROM
    CTE3
    INNER JOIN CTE6 USING ("ordinality"))
    INSERT INTO du_dms_reynoldsrci_model.etl_labor_details ("roNumber", "JobNo", "OpCode", "OpCodeDesc", "TechNo", "TechName", "PayType", "BillTime", "DlrCost", "TotalAmt")
    SELECT
        "roNumber",
        "JobNo",
        "OpCode",
        "OpCodeDesc",
        "TechNo",
        "TechName",
        "PayType",
        "BillTime",
        "DlrCost",
        "TotalAmt"
    FROM
        CTE7;
END;
$body$


