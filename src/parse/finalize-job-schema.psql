BEGIN;
\pset pager off

ALTER TABLE etl_head_detail ALTER COLUMN "RoCreateDate" TYPE date USING ("RoCreateDate"::date);

\ir './finalize-scripts/create-linkages.psql'

\o :WORK_DIR_IMPORT_FILES_DIR/import-log.txt
\C 'Invoice Master Rows With Non-Numeric RO#s'
WITH del_alpha_ros AS (
    DELETE FROM etl_head_detail WHERE "roNumber" ~ '[A-Z]'
    RETURNING "roNumber"
)
SELECT count(*) FROM del_alpha_ros;


UPDATE etl_job_detail
   SET "OpCodeDesc" = 'OP: ' || "OpCode"
 WHERE NULLIF("OpCodeDesc", '') IS NULL;

UPDATE etl_parts_detail
   SET "PartNoDesc" = 'PN: ' || "PartNo"
 WHERE NULLIF("PartNoDesc", '') IS NULL;

-- UPDATE etl_job_detail SET "BillTime" =  ROUND(("TotalAmt"::numeric/"BillRate"::numeric),2)
-- WHERE "BillTime" IS NULL AND ("TotalAmt" IS NOT NULL OR "BillRate" IS NOT NULL) AND "BillRate"::numeric != 0;

UPDATE etl_job_detail SET "BillTime" = '0.00'
WHERE "BillTime" IS NULL AND "BillRate"::numeric = 0;

UPDATE etl_job_detail SET "BillTime" = '0.00'
WHERE "BillTime" IS NULL AND "TotalAmt"::numeric != 0;

\C 'Removed Labor Items with No Hours or Sale Amount'
WITH dels AS (
DELETE FROM etl_job_detail
 WHERE "TotalAmt"::numeric = 0 AND "BillTime"::numeric = 0
       AND "roNumber" NOT IN (SELECT "roNumber" FROM etl_other_detail 
       UNION SELECT "roNumber" FROM etl_misc_detail)
 RETURNING *
) 
SELECT count(*) AS zero_zero_labor FROM dels;


\C 'Removed Parts Items with No Cost or Sale Amount'
WITH delsparts AS (
DELETE FROM etl_parts_detail
 WHERE "DlrCost"::numeric = 0 AND "CustPrice"::numeric = 0
 RETURNING *
) 
SELECT count(*) AS zero_zero_parts FROM delsparts;


\ir './core-logic/core-table-preparation.psql'

\ir './kit-logic/kit-table-preparation.psql'
--\ir './kit-logic/assign-kit-pseudo-jobs.psql'
--\ir './kit-logic/allocate-sale-to-kit-parts.psql'

\C 'Invoice Master'
SELECT count(*), min("roNumber"::integer), max("roNumber"::integer)
  FROM etl_head_detail;

\C 'Labor Detail'
SELECT count(*), min("roNumber"::integer), max("roNumber"::integer)
  FROM etl_job_detail;

\C 'Parts Detail'
SELECT count(*), min("roNumber"::integer), max("roNumber"::integer)
  FROM etl_parts_detail;

\ir './finalize-scripts/fix-part-numbers-and-descriptions.psql'


\C 'ROs older than 6 months'
SELECT count(*) FROM etl_head_detail WHERE "RoCreateDate"::date <  CURRENT_DATE - INTERVAL '6 months';

\C 
SELECT count(DISTINCT ("roNumber", "JobNo")) AS "Jobless parts"
  FROM etl_parts_detail
 WHERE NOT EXISTS (SELECT 1
                     FROM etl_job_detail
                    WHERE etl_job_detail."roNumber"::integer = etl_parts_detail."roNumber"::integer
                          AND etl_job_detail."JobNo"::integer = etl_parts_detail."JobNo"::integer);

\C 
SELECT  assert_none(count(DISTINCT ("roNumber", "JobNo")),
                        'Jobless Parts Without Paytype') AS "Jobless Parts Without Paytype"
  FROM etl_parts_detail
 WHERE NOT EXISTS (SELECT 1
                     FROM etl_job_detail
                    WHERE etl_job_detail."roNumber"::integer = etl_parts_detail."roNumber"::integer
                          AND etl_job_detail."JobNo"::integer = etl_parts_detail."JobNo"::integer)
       and etl_parts_detail."PayType" IS NULL;

\ir './populate-reynoldsrci-job-schema.psql'

COMMIT;
