#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

ZIP_TO_PROCESS="${1:?Zip File Required First}"
BASE_WORK_DIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work

mkdir -p "$BASE_WORK_DIR"/manual/dead-letter-processed
mkdir -p "$BASE_WORK_DIR"/manual/bundle
mkdir -p "$BASE_WORK_DIR"/manual/extraction-archive

./process-json --input-zip "$ZIP_TO_PROCESS" \
               --bundle-dir "$BASE_WORK_DIR"/manual/bundle \
               --dead-letter "$BASE_WORK_DIR"/manual/dead-letter-processed \
               --output-prefix 'PROC-' \
               --build-proxies \
               --single-store-flag 'true' \
               --brand 'FIAT' \
               --custom-branch-name 'QA' \
               --state 'NA' \
               --uuid '' \
               --company_ids '' \
               --exception-report 'true' \
               --performed-by '' \
                --pre-import-halt 'false'\
                --solve-db 'true' \
                --scheduler-db 'true'

if [[ $? = 0 ]]; then
    say "Processing Completed; Moving input to Archive"
    mv --force "$ZIP_TO_PROCESS" "$BASE_WORK_DIR"/manual/extraction-archive
    (
        cd ..
        find "$BASE_WORK_DIR"/manual/bundle/ \
             -maxdepth 1 -type f -exec ./send-bundle-live-hpdog {} {} \;
    )

else
    yell "Processing Failed; Leaving Extraction Zip File In Place"
fi
