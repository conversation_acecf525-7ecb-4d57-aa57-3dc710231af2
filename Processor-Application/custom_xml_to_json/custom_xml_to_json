#!/usr/bin/env node

"use strict";
const program = require("commander");
const parser = require("xml2json");
const fs = require("fs");

program
  .command("convert")
  .alias("c")
  .option("-i, --input <value>", "Specify input file")
  .description("Process complicated xml to json conversion")
  .action(async (options) => {
    // console.log("Action");
    // console.log(options.input);
    let xml_string, outputFile;
    try {
      if (options.input && options.input != "") {
        if (fs.existsSync(options.input)) {
          xml_string = fs.readFileSync(options.input, "utf8");
          // console.log(xml_string);
          outputFile = options.input.replace(".xml", ".json");
          fs.writeFileSync(outputFile, parser.toJson(xml_string));
          fs.unlinkSync(options.input);
        } else {
          console.log("File not exist!");
        }
      } else {
        console.log("Empty file path!");
      }
    } catch (err) {
      console.log(JSON.stringify(err));
    }
  });

program.parse(process.argv);
