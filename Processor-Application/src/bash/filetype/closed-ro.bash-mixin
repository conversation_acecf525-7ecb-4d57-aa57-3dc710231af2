# Sourced by process-json and provides function and constants
# for dealing with the ReynoldsRCI Model JSON File

source "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application/src/bash/process-library.bash-mixin

function process_closed_ro_json() {
    progress "Closed RO JSON"
    closed_ro_processing_log="$WORK_DIR_PROCESSING_LOG_DIR"/ClosedRO.proc
    # open a FD in order to minimize overhead since we are going to
    # be writing timestamps for each processed RO into the file
    exec 3>> "$closed_ro_processing_log"

    echo "$(date)"                     >&3
    echo "START_TIME=$(date '+%s.%N')" >&3

    if closed_ro_split_into_singles "$1"; then
        mv "$1" "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"/
    else
        move_to_work_dead_letter "$1"
    fi

    exec 3>&-
}

function closed_ro_split_into_singles() {
    input_file_with_path="$(pwd)/${1:?file name required}"
    progress "Beginning $PROCESSING_MODE processing in $JSON_CONVERSION_WORK_DIR $1" >&2
    (
        mkdir -p "$SINGLE_RO_JSON_DIR"
        cp "$1" "$JSON_CONVERSION_WORK_DIR"/

        progress "Convert $1 to json file"
        custom_xml_to_json convert -i "$JSON_CONVERSION_WORK_DIR"/"$1"

        jsonExtension=".json"
        jsonFile=$(echo "$1" | sed "s/.xml/$jsonExtension/")

        cd "$JSON_CONVERSION_WORK_DIR"
       
        RO_COUNT=$(closed_ro_get_repair_order_count $jsonFile)
        count_end=$(date +%s.%N)
        echo "$(date) Found # $RO_COUNT"
        {
            echo "SPLIT_END=$count_end"
            echo "SPLIT_COUNT=$RO_COUNT"
        } >&3    

        jq -c \
           ".rey_ArmatusDealerUpliftRepairOrder.ApplicationArea" \
           $jsonFile \
           > store_details.json \

        script_file='script.psql'
      
        local jq_file=$(basename 'store_details.json' .json).jq
        JQ_TRANS_RESULT=$(cat store_details.json)
        echo "$JQ_TRANS_RESULT" > "$jq_file"
        echo '\set jq_store_result `cat' "'$jq_file'" '| jq .+'"'{CreationDateTime : .CreationDateTime}'"'+'"'{BODId : .BODId}'"'+'"'{DestinationNameCode : .Destination.DestinationNameCode}'"'+'"'.Sender'"' `'>> "$script_file"

        cat >>"$script_file" <<'EOF'
        CREATE TABLE IF NOT EXISTS du_dms_reynoldsrci_model.etl_store_detail (
            "Component" text,
            "Task" text,
            "DealerNumber" text,
            "StoreNumber" text,
            "AreaNumber" text,
            "CreationDateTime" text,
            "BODId" text,
            "DestinationNameCode" text
        );
         INSERT INTO etl_store_detail
                SELECT * FROM json_populate_record(null::etl_store_detail, :'jq_store_result');

EOF
       psql_local --quiet --file "$script_file" > /dev/null || die "Insert store details"

       rm store_details.json


    if [[ "$RO_COUNT" -eq 0 ]]; then
        echo "RO_COUNT is 0"
    elif [[ "$RO_COUNT" -eq 1 ]]; then
        echo "RO_COUNT is 1"
        
        jq -c \
           ".rey_ArmatusDealerUpliftRepairOrder.RepairOrder" \
           $jsonFile \
           > lined_repair_orders.json \
           || die "JQ One-Line-Per-RO Tranformation Failed"
	
        place_lines_into_their_own_files lined_repair_orders.json || die "Failed to place json lines into individual files"

        rm lined_repair_orders.json
        rm $jsonFile

    else
        echo "$(date) Transform Begin"
        loop_beg=$(date)
        echo "RO_COUNT is greater than 1"

        jq -c \
           ".rey_ArmatusDealerUpliftRepairOrder.RepairOrder[]" \
           $jsonFile \
           > lined_repair_orders.json \
           || die "JQ One-Line-Per-RO Tranformation Failed"
	
        place_lines_into_their_own_files lined_repair_orders.json || die "Failed to place json lines into individual files"

        rm lined_repair_orders.json
        rm $jsonFile

        echo "$(date) Transform End"
        loop_end=$(date)

        echo "Loop Beg: $loop_beg" >&3
        echo "Loop End: $loop_end" >&3

    fi
    ) || die "Split of Closed ROS into Singles Failed"
    return $?
}

function closed_ro_get_repair_order_count() {
    jq '((.rey_ArmatusDealerUpliftRepairOrder).RepairOrder) | if type == "array" then length else 1 end' < "$1"
}
