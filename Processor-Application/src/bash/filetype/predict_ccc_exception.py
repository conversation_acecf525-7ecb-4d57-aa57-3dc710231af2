import json
import os
import sys

filepath = sys.argv[1]
OutputReport = sys.argv[2]

list_of_keys = ['Complaint', 'Cause', 'Correction']
bad_chars = ["(", ")", "|", "`", "~", "=", "_", "!", "@", "$", "%", "^", "&", ";", "/", "<", ">", "?", "*", "#", "-", "+"]
RoNum = None
OpCodeLaborInfo = None
JobNum = None
CCCStmts = None
ComplaintRecord = []
CauseRecord = []
CorrectionRecord = []
Complaint_missing = []
Cause_missing = []
Correction_missing = []
Total_count = 0
ExceptionCount = 0
ComplaintExceptionCount = 0
CauseExceptionCount = 0
CorrectionExceptionCount = 0
ComplaintExceptionStatus=None
CauseExceptionStatus = None
CorrectionExceptionStatus = None
rounded_ComplaintExceptionPercentage = None
rounded_CauseExceptionPercentage = None
rounded_CorrectionExceptionPercentage = None
rounded_CCCExceptionPercentage = None

f = open(OutputReport, "a")
# f.write("Ro_Number,Job_Number,ComplaintException,CauseException,CorrectionException\n")
f.write("Ro_Number,Job_Number\n")
f.close()

class bcolors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def check_missing_value_Complaint_missing(comp_array_item):
	global Complaint_missing
	Complaint_missing = []
	if len(comp_array_item) > 1:
		for comp_ele in range(comp_array_item[0], comp_array_item[-1]+1):
		    if comp_ele not in comp_array_item:
		        Complaint_missing.append(comp_ele)
	elif len(comp_array_item) == 1:
		for comp_one_ele in comp_array_item:
			if comp_one_ele != 1:
				Complaint_missing.append(comp_one_ele)

	else:
		pass

def check_missing_value_Cause_missing(cause_array_item):
	global Cause_missing
	Cause_missing = []
	if len(cause_array_item) > 1:
		for cause_ele in range(cause_array_item[0], cause_array_item[-1]+1):
		    if cause_ele not in cause_array_item:
		        Cause_missing.append(cause_ele)

	elif len(cause_array_item) == 1:
		for cause_one_ele in cause_array_item:
			if cause_one_ele != 1:
				Cause_missing.append(cause_one_ele)
	else:
		pass

def check_missing_value_Correction_missing(corr_array_item):
	global Correction_missing
	Correction_missing = []
	if len(corr_array_item) > 1:
		for corr_ele in range(corr_array_item[0], corr_array_item[-1]+1):
		    if corr_ele not in corr_array_item:
		        Correction_missing.append(corr_ele)

	elif len(corr_array_item) == 1:
		for corr_one_ele in corr_array_item:
			if corr_one_ele != 1:
				Correction_missing.append(corr_one_ele)

	else:
		pass
def predict_exception():

	global list_of_keys, bad_chars, RoNum, OpCodeLaborInfo, JobNum, CCCStmts, ComplaintRecord, CauseRecord, CorrectionRecord, Total_count, ExceptionCount, Complaint_missing, Cause_missing, Correction_missing, ComplaintExceptionStatus, CauseExceptionStatus, CorrectionExceptionStatus, ComplaintExceptionCount, CauseExceptionCount, CorrectionExceptionCount

	# to check if list is sorted
	Complaintflag = 0
	FinalStatus = None
	ComplaintException = None

	if(ComplaintRecord == sorted(ComplaintRecord)):
		Complaintflag = 1
	# printing result
	# if (Complaintflag) or not Complaint_missing:
	if not Complaint_missing and len(ComplaintRecord) > 0:
		ComplaintException = False
	elif len(ComplaintRecord) == 0:
		ComplaintException = False
	else :
		ComplaintException = True
	ComplaintRecord = []

	Causeflag = 0
	CauseException = None
	if(CauseRecord == sorted(CauseRecord)):
		Causeflag = 1
		
	# printing result
	# if (Causeflag) or not Cause_missing:
	if not Cause_missing and len(CauseRecord) > 0:
		CauseException = False
	elif len(CauseRecord) == 0:
		CauseException = False
	else :
		CauseException = True
	CauseRecord = []

	Correctionflag = 0
	CorrectionException = None
	if(CorrectionRecord == sorted(CorrectionRecord)):
		Correctionflag = 1
		
	# printing result
	# if (Correctionflag) or not Correction_missing::
	if not Correction_missing and len(CorrectionRecord) > 0:
		CorrectionException = False
	elif len(CorrectionRecord) == 0:
		CorrectionException = False
	else :
		CorrectionException = True
	CorrectionRecord = []

	if ComplaintException:
		ComplaintExceptionCount += 1
		ComplaintExceptionStatus = True
	else:
		ComplaintExceptionStatus = ""
	if CauseException:
		CauseExceptionCount += 1
		CauseExceptionStatus = True
	else:
		CauseExceptionStatus = ""
	if CorrectionException:
		CorrectionExceptionCount += 1
		CorrectionExceptionStatus = True
	else:
		CorrectionExceptionStatus = ""

	if ComplaintException or CauseException or CorrectionException:
		FinalStatus = "Exception"
	else:
		FinalStatus = "NotAnException"

	if FinalStatus == "Exception":
		ExceptionCount += 1
		f = open(OutputReport, "a")
		# f.write("{0},{1},{2},{3},{4}\n".format(RoNum, JobNum, ComplaintExceptionStatus, CauseExceptionStatus, CorrectionExceptionStatus))
		f.write("{0},{1}\n".format(RoNum, JobNum))
		f.close()
	else:
		pass

def checkKey():
	global list_of_keys, bad_chars, RoNum, OpCodeLaborInfo, JobNum, CCCStmts, ComplaintRecord, CauseRecord, CorrectionRecord, Total_count, ExceptionCount, Complaint_missing, Cause_missing, Correction_missing
	ComplaintRecord = []
	CauseRecord = []
	CorrectionRecord = []
	complaint_count = 0
	correction_count = 0
	cause_count = 0
	Total_count += 1
	for CCC in CCCStmts:
		for item in CCC:
			for chars in bad_chars:
				item = item.replace(chars, '')

		if isinstance(CCC, dict):
			comp_predict = CCC.get("Complaint", 'Key missing')
			cause_predict = CCC.get("Cause", 'Key missing')
			corr_predict = CCC.get("Correction", 'Key missing')

			if comp_predict == "Key missing":
				complaint_count += 1
				pass
			else:
				complaint_count += 1
				ComplaintRecord.append(complaint_count)

			if cause_predict == "Key missing":
				cause_count += 1
				pass
			else:
				cause_count += 1
				CauseRecord.append(cause_count)

			if corr_predict == "Key missing":
				correction_count += 1
				pass
			else:
				correction_count += 1
				CorrectionRecord.append(correction_count)

			check_missing_value_Complaint_missing(ComplaintRecord)
			check_missing_value_Cause_missing(CauseRecord)
			check_missing_value_Correction_missing(CorrectionRecord)
		else:
			pass
	
	predict_exception()

def read_json_file():
	global list_of_keys, bad_chars, RoNum, OpCodeLaborInfo, JobNum, CCCStmts, Total_count, ExceptionCount,  Complaint_missing, Cause_missing, Correction_missing
	# Opening JSON file
	try:
		for filename in os.listdir(filepath):
			with open(filepath+filename) as json_file:
			    data = json.load(json_file)			 
			    RoNum = data['RoRecord']['Rogen']['RoNo']
			    OpCodeLaborInfo = data['RoRecord']['Rolabor']['OpCodeLaborInfo']
			    if isinstance(OpCodeLaborInfo, list):
				    for content in OpCodeLaborInfo:
					    JobNum = content.get('JobNo', 'Key missing')
					    CCCStmts = content.get('CCCStmts', 'Key missing')
					    if isinstance(CCCStmts, dict):
					    	pass
					    else:
					    	checkKey()
			    else:
			    	# pass
			    	if "JobNo" in OpCodeLaborInfo:
				    	JobNum = OpCodeLaborInfo['JobNo']
			    	else:
				    	print ("Job not present")
			    	if "CCCStmts" in OpCodeLaborInfo:
				    	CCCStmts = OpCodeLaborInfo['CCCStmts']
				    	if isinstance(CCCStmts, dict):
					    	pass
				    	else:
				    		checkKey()
			    	else:
			    		pass

	except Exception as e:
		print ("Exception found: {0}".format(e))
		pass

def  process_json_items():
	global rounded_CCCExceptionPercentage, rounded_CorrectionExceptionPercentage, rounded_CauseExceptionPercentage, rounded_ComplaintExceptionPercentage, ComplaintExceptionCount, CauseExceptionCount, CorrectionExceptionCount
	if ExceptionCount > 0:
		CCCExceptionQuotient = 	ExceptionCount/Total_count
		CCCExceptionPercentage = CCCExceptionQuotient * 100
		rounded_CCCExceptionPercentage = round(CCCExceptionPercentage, 2)

		ComplaintExceptionQuotient = 	ComplaintExceptionCount/Total_count
		ComplaintExceptionPercentage = ComplaintExceptionQuotient * 100
		rounded_ComplaintExceptionPercentage = round(ComplaintExceptionPercentage, 2)

		CauseExceptionQuotient = 	CauseExceptionCount/Total_count
		CauseExceptionPercentage = CauseExceptionQuotient * 100
		rounded_CauseExceptionPercentage = round(CauseExceptionPercentage, 2)

		CorrectionExceptionQuotient = 	CorrectionExceptionCount/Total_count
		CorrectionExceptionPercentage = CorrectionExceptionQuotient * 100
		rounded_CorrectionExceptionPercentage = round(CorrectionExceptionPercentage, 2)
		print(bcolors.WARNING + "")
		print(bcolors.BOLD + "CCC Exception Percentage: {0}%".format(rounded_CCCExceptionPercentage))
	else:
		print(bcolors.WARNING + "")
		print(bcolors.BOLD + "No CCC Exception")

	f = open(OutputReport, "a")
	# f.write(",,Complaint Exception Percentage: {0}%,Cause Exception Percentage: {1}%, Correction Exception Percentage: {2}%\n\n".format(rounded_ComplaintExceptionPercentage, rounded_CauseExceptionPercentage, rounded_CorrectionExceptionPercentage))
	f.write("Total Count: {0},Exception Count: {1}, Exception Percentage: {2}%\n\n".format(Total_count, ExceptionCount, rounded_CCCExceptionPercentage))
	f.close()

read_json_file()
process_json_items()