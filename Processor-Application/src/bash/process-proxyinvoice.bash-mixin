# source into process-json
function generate_proxies_from_zip_for_company() {
    set -e  # Exit on failure

    export COMPANY_ID="$1"
    export SRC_SCHEMA="${BASE_SRC_SCHEMA}_${COMPANY_ID}"   # unique per company

    progress "Generating Proxies for COMPANY_ID=$COMPANY_ID using schema=$SRC_SCHEMA"

    # Check if we're in post-processed-proxies mode or regular ZIP processing mode
    if [[ -n "$POST_PROCESSED_ZIP_FILE" && -f "$POST_PROCESSED_ZIP_FILE" ]]; then
        # Post-processed-proxies mode: unzip the provided file
        echo "🔧 Using post-processed ZIP file: $POST_PROCESSED_ZIP_FILE"
        local local_work_dir="$WORK_DIR/post-process-proxies-$COMPANY_ID"
        clear_dir "$local_work_dir"
        unzip "$POST_PROCESSED_ZIP_FILE" -d "$local_work_dir"
        restore_dump_file_to_database "$local_work_dir/process-json-csv-results.pgdump"
    else
        # Regular ZIP processing mode: use the existing work directory
        echo "🔧 Using regular ZIP processing mode for COMPANY_ID=$COMPANY_ID"
        local local_work_dir="$WORK_DIR"
        # No need to unzip or restore database - data is already processed
    fi

    build_proxy_repair_orders "$local_work_dir" \
        "$SINGLE_STORE_FLAG" \
        "$CUSTOM_BRANCH_NAME" \
        "$PORSCHE_STORE" \
        "$BRAND" \
        "$STATE" \
        "$COMPANY_ID"

    compress_proxy_output "$local_work_dir"

    # Only prompt for continuation in post-processed-proxies mode (interactive mode)
    if [[ -n "$POST_PROCESSED_ZIP_FILE" && -f "$POST_PROCESSED_ZIP_FILE" ]]; then
        prompt_continuation "Review and Continue"
    fi
}



function restore_dump_file_to_database() {
    local dump_file="$1"          # arg 1 = pgdump path
    local MODEL_SCHEMA="${SRC_SCHEMA}_${COMPANY_ID}_model"

    function psql_local() {
        psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${MODEL_SCHEMA}'" \
             --set=ON_ERROR_STOP=1 "$@"
    }

    if ! psql_local -Atc "SELECT 1 FROM pg_namespace WHERE nspname = '${MODEL_SCHEMA}'" | grep -q 1 ; then
        say "Creating schema ${MODEL_SCHEMA}"
        psql_local --quiet -c "CREATE SCHEMA ${MODEL_SCHEMA};"
        progress "Restoring $dump_file into ${MODEL_SCHEMA}"
        pg_restore --verbose \
                   -d "service=$DU_ETL_PG_SERVICE options='-c search_path=${MODEL_SCHEMA}'" \
                   "$dump_file"
    else
        say "Schema ${MODEL_SCHEMA} already exists — skipping restore"
    fi
}

function build_proxy_repair_orders() {
    # echo "Processor status: 17/21 Generating Proxy Repair Orders per Request Started"
    # sleep 1
    say "Generating Proxy Repair Orders per Request"
    PROXY_SCHEMA="${SRC_SCHEMA}_proxy"
    SRC_SCHEMA_COMPANY="${SRC_SCHEMA}_${COMPANY_ID}"
    PROXY_SCHEMA="${SRC_SCHEMA_COMPANY}_proxy"
    SRC_PROXY_SCHEMA="${SRC_SCHEMA_COMPANY}_model"
    SINGLE_STORE_FLAG=$2
    CUSTOM_BRANCH_NAME=$3
    PORSCHE_STORE=$4
    BRAND=$5
    STATE=$6
    COMPANY_ID=$7

    (
        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        "$DU_ETL_HOME"/DU-ProxyInvoice/initialize-schema "$PROXY_SCHEMA"
    ) || die "Initialization Failed"

    (
        cd "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI 
        psql_etl_proxy --quiet --file ./src/parse/reynoldsrci-proxy-schema-alter.psql
        psql_etl_proxy --quiet --file ./src/parse/populate-reynoldsrci-proxy-invoice.psql
    ) || die "Failed to Populate ReynoldsRCI Proxy Invoice"

    (
        mkdir -p "$1"/proxy-invoice
        TEMPLATE_BACKGROUND_ARG=
        TEMPLATE_TYPE=
        if [[ -f "$1"/proxy-background.pdf ]]; then
            TEMPLATE_BACKGROUND_ARG="$1"/proxy-background.pdf
            TEMPLATE_TYPE='invoice_mimic'
        else
            TEMPLATE_BACKGROUND_ARG='none'
            TEMPLATE_TYPE='invoice_mimic'
        fi


        GENERATE_DUAL_PROXY=false
        # Convert BRAND to lowercase
        BRAND_LOWER=$(echo "$BRAND" | tr '[:upper:]' '[:lower:]')
        # Apply logic
        echo "BRAND_LOWER --------    $BRAND_LOWER" 
        echo "PORSCHE_STORE --------    $PORSCHE_STORE" 

        if [[ "$BRAND_LOWER" != "porsche" && "$PORSCHE_STORE" == "true" ]]; then
        GENERATE_DUAL_PROXY=true
        fi
        IS_PORSCHE_STORE=false
        if [[ "$BRAND_LOWER" == "porsche" ]]; then
        IS_PORSCHE_STORE=true
        fi
        echo "GENERATE_DUAL_PROXY --------    $GENERATE_DUAL_PROXY"

        cd "$DU_ETL_HOME"/DU-ProxyInvoice
        "$DU_ETL_HOME"/DU-ProxyInvoice/generate-fixed-width-proxy-invoice-reynoldsrci.bash \
            "$PROXY_SCHEMA" \
            "$1"/proxy-invoice \
            'true' "$TEMPLATE_TYPE" \
            "$TEMPLATE_BACKGROUND_ARG" \
            "$SINGLE_STORE_FLAG" \
            "$CUSTOM_BRANCH_NAME" \
            "$IS_PORSCHE_STORE" \
            "$BRAND" \
            "${STATE}" \
            "false" \
            "$COMPANY_ID"

        if [[ "$GENERATE_DUAL_PROXY" = 'true' ]]; then
            "$DU_ETL_HOME"/DU-ProxyInvoice/generate-fixed-width-proxy-invoice-reynoldsrci.bash \
                "$PROXY_SCHEMA" \
                "$1"/proxy-invoice \
                'true' "$TEMPLATE_TYPE" \
                "$TEMPLATE_BACKGROUND_ARG" \
                "$SINGLE_STORE_FLAG" \
                "$CUSTOM_BRANCH_NAME" \
                "true" \
                "PORSCHE" \
                "${STATE}" \
                "true" \
                "$COMPANY_ID"
        fi

    ) || die "Proxy invoice generation Failed"
    # echo "Processor status: 17/21 Generating Proxy Repair Orders per Request Completed"
    # sleep 1
}

function compress_proxy_output() {
(

    base_output_dir="$1"/proxy-invoice
        progress "compress $base_output_dir"

    txt_output_dir="$base_output_dir"/text
    pdf_output_dir="$base_output_dir"/pdf
    bundle_output_dir="$base_output_dir"/bundle

    cd "$base_output_dir"
    echo $base_output_dir
    if [[ -d "$txt_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-TXT.zip "$txt_output_dir"/*
        rmdir "$txt_output_dir"
    fi

    if [[ -d "$pdf_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-PDF.zip "$pdf_output_dir"/*
        rmdir "$pdf_output_dir"
    fi

    if [[ -d "$bundle_output_dir" ]]; then
        zip -j -qm Proxy-Invoices-Bundles.zip "$bundle_output_dir"/*
        echo "-----COPY FILE TO AUDIT DISPATCH DIRECTORY----------${INPUT_ZIP_FILE_NAME}-----"
        #zip -j "/etl/audit-dispatch-bundle/${INPUT_ZIP_FILE_NAME}" "$1"/processing-result/*INVOICE_MASTER* Proxy-Invoices-Bundles.zip
        zip -j "/etl/audit-dispatch-bundle/${OUTPUT_FILE_PREFIX}""${INPUT_ZIP_FILE_NAME}" "$1"/processing-result/invoice-master.csv Proxy-Invoices-Bundles.zip
        rmdir "$bundle_output_dir"
    fi

)
}

function compress_dual_proxy_output() {
(
    base_output_dir="$1"/proxy-invoice
    txt_output_dir="$base_output_dir"/text-porsche
    pdf_output_dir="$base_output_dir"/pdf-porsche
    bundle_output_dir="$base_output_dir"/bundle-porsche

    cd "$base_output_dir"

    if [[ -d "$txt_output_dir" ]]; then
        zip -j -r -qm Proxy-Invoices-TXT-Porsche.zip "$txt_output_dir"/
        rmdir "$txt_output_dir"
    fi
    if [[ -d "$pdf_output_dir" ]]; then
        zip -j -r -qm Proxy-Invoices-PDF-Porsche.zip "$pdf_output_dir"/
        rmdir "$pdf_output_dir"
    fi
    if [[ -d "$bundle_output_dir" ]]; then
        zip -j -r -qm Proxy-Invoices-Bundles-Porsche.zip "$bundle_output_dir"/
        echo "-----COPY FILE TO AUDIT DISPATCH DIRECTORY------$1----${INPUT_ZIP_FILE_NAME}-----"
        zip -j "/etl/audit-dispatch-bundle/${INPUT_ZIP_FILE_NAME}" "$1"/processing-result/invoice-master.csv Proxy-Invoices-Bundles.zip Proxy-Invoices-Bundles-Porsche.zip

        rmdir "$bundle_output_dir"
    fi
)
}
