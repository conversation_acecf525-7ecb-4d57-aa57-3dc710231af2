# To be sourced by process-json
TRANSFORMS_DIR="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/runtime/transforms
mkdir -p "$TRANSFORMS_DIR"

JQ_HEAD="$TRANSFORMS_DIR"/head.jq-trans
JQ_JOB="$TRANSFORMS_DIR"/job.jq-trans
JQ_PARTS="$TRANSFORMS_DIR"/parts.jq-trans
JQ_CUSTOMER="$TRANSFORMS_DIR"/customer.jq-trans
RO_COUNT_BATCH_FILE=/tmp/$(date +%s).tmp

function persist_and_export_data_and_schema() {
    # # echo "Processor status: 11/25 Persisting and Exporting Data and Schema Started"
    # sleep 1
    progress "Persisting and Exporting Data and Schema"

    psql_local --quiet <<'SQL'
    BEGIN;
    ALTER TABLE etl_head_detail ADD PRIMARY KEY("roNumber");
    COMMIT;
SQL
    pg_dump -Fc "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
            -t 'etl_json' \
            -t 'etl_*' \
            -t 'etl_make_model' \
            > "${1:?Directory Required}"/process-json-csv-results.pgdump
    # # echo "Processor status: 11/25 Persisting and Exporting Data and Schema Completed"
    # sleep 1
}
function fork_company_job() {
    local base_dir="$1"; shift        # typically $ZIP_WORK_DIR
    local cid="$1"; shift             # COMPANY_ID
    local cmd=("$@")                  # e.g. generate_proxies_from_zip_for_company

    local job_dir="${base_dir}/company-${cid}"
    mkdir -p "$job_dir"               # job‑specific directory

    (
        set -e
        # isolate globals *per* job
        export COMPANY_ID="$cid"
        export SRC_SCHEMA="${BASE_SRC_SCHEMA}_${cid}"
        export WORK_DIR="$job_dir"    # give the job its own temp tree

        say "🧵 Forked job for COMPANY_ID=$COMPANY_ID using schema=$SRC_SCHEMA"
        "${cmd[@]}" "$COMPANY_ID"
    ) &
}



function isolate_problematic_ros() {
    # echo "Processor status: 6/21 Detecting Problematic ROs Started"
    # sleep 1
    progress "Detecting Problematic ROs"
    psql_local --quiet <<'SQL'
    BEGIN;
    SET client_min_messages = 'WARNING';

    UPDATE etl_head_detail hd
    SET "OldStatus" = "RoStatus",
        "RoStatus" = "invoice_status"
    FROM etl_invoicemaster im
    WHERE im.invoicenumber = hd."roNumber"
        AND hd."RoStatus" != "invoice_status"
        AND hd."RoStatus" != 'OPEN' 
	AND "invoice_status" != 'CLOSED';
    
   UPDATE etl_head_detail hd
   SET "OldStatus" = "RoStatus",
        "RoStatus" = "invoice_status"
   FROM etl_invoicemaster im
   WHERE hd."RoStatus" = 'OPEN' AND "invoice_status" = 'CLOSED'
         AND im.closedate <= (SELECT MAX("CustInvoiceDate") FROM etl_head_detail);

   UPDATE etl_head_detail 
   SET "IsDelete" = TRUE
   WHERE "roNumber" NOT IN (SELECT im.invoicenumber
                            FROM etl_invoicemaster im);
    		

    INSERT INTO etl_problematic_ros("roNumber", comment)
    SELECT "roNumber", 'Opened Too Long Ago (' || "RoCreateDate" || ')'
      FROM etl_head_detail
     WHERE "RoCreateDate"::date < '2000-01-01'::date;

    INSERT INTO etl_problematic_ros("roNumber", comment)
    SELECT "roNumber", 'Voided RO'
      FROM etl_head_detail
     WHERE "RoCreateDate"::date IS NULL;

     \gset

    DELETE FROM etl_customer_detail
    WHERE "roNumber" IN (SELECT DISTINCT "roNumber" FROM etl_problematic_ros);
    
    DELETE FROM etl_parts_detail
    WHERE "roNumber" IN (SELECT DISTINCT "roNumber" FROM etl_problematic_ros);

    DELETE FROM etl_other_detail
    WHERE "roNumber" IN (SELECT DISTINCT "roNumber" FROM etl_problematic_ros);

    DELETE FROM etl_job_detail
    WHERE "roNumber" IN (SELECT DISTINCT "roNumber" FROM etl_problematic_ros);

    DELETE FROM etl_head_detail
    WHERE "roNumber" IN (SELECT DISTINCT "roNumber" FROM etl_problematic_ros);

    SELECT * FROM etl_problematic_ros ORDER BY "roNumber";

    COMMIT;
SQL

    if [[ $? != 0 ]]; then
        yell "Aborting During Problematic RO Detection"
        return 1
    fi

    # Move the problematic ROs to dead-letter path

    PROBLEMATIC_ROS=$(psql_local --quiet --tuples-only --no-align --command 'SELECT DISTINCT "roNumber" FROM etl_problematic_ros ORDER BY "roNumber";')
    ROs="$(echo "$PROBLEMATIC_ROS" | tr -s "\n" " ")" #Replace \n with space (" ")
    ROs=${ROs::-1} # Remove trailing space (" ")
    echo "ROs:$ROs"
    if [[ ! -z "$ROs" ]]; then
        for RO in ""$ROs""
        do
            mv "$SINGLE_RO_JSON_DIR"/"$RO".json "$WORK_DIR_DEAD_LETTER"/"$RO".json
            prob_comment=$(psql_local -At --quiet --set=ronum="$RO" <<SQL
            SELECT string_agg(comment, ';' ORDER BY comment) FROM etl_problematic_ros WHERE "roNumber" = :'ronum'
SQL
)
            echo "${RO}: ${prob_comment}" >> "$WORK_DIR_DEAD_LETTER"/../dead-letter-detail.txt
        done
    fi
    # echo "Processor status: 6/21 Detecting Problematic ROs Completed"
    # sleep 1
}

function export_job_data_to_csv(){

    # # echo "Processor status: 12/25 Creating New Status File Started"
    # sleep 1
    progress  "Creating New Status File!"
    mkdir "$WORK_DIR_IMPORT_FILES_DIR"
    cd "$WORK_DIR_IMPORT_FILES_DIR"
   psql_local --quiet \
               --set=DMS_HOME="$DMS_HOME" \
               --set=DU_ETL_HOME="$DU_ETL_HOME" \
               --set=WORK_DIR_IMPORT_FILES_DIR="$WORK_DIR_IMPORT_FILES_DIR" \
               >/dev/null \
<<'SQL'
SET client_min_messages = 'warning';

SELECT set_config('search_path', 'du_dms_reynoldsrci_model', true);

-- \i :DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/src/parse/finalize-job-schema.psql

SQL
# # echo "Processor status: 12/25 Creating New Status File Completed"
    # sleep 1
}


function append_ro_to_script() {
    local json_file="${1:?RO File Required}"
    local script_file="${2:?Script File Required}"
    local jq_file=$(dirname $script_file)/$(basename "$json_file" .json).jq
    (
        #JQ_TRANS_RESULT=$(jq 'map_values( (if has("_text") or ((keys | length) == 0) then ._text else (if (.V | type) == "array" then . else {"V":[.V]} end) end) )' "$json_file")
        JQ_TRANS_RESULT=$(cat $json_file)
        echo "$JQ_TRANS_RESULT" > "$jq_file"

        echo "\set json_file_name '$json_file'" >> "$script_file"
        echo '\set jq_result `cat' "'$jq_file'" '| jq .+'"'{roNumber : .RoRecord.Rogen.RoNo}'"'+'"'{TransactionPayType : .RoRecord.TransactionPayType}'"'+'"'{TransactionType : .RoRecord.TransactionType}'"'+'"'{RoComment : .RoRecord.Rogen.RoCommentInfo}'"'+'"'{TechRecommend : .RoRecord.Rogen.TechRecommends}'"'+'"'.RoRecord.Rogen'"'+'"'.ServVehicle.Vehicle'"'+'"'.ServVehicle.VehicleServInfo'"'+'"'.ServVehicle.Vehicle.VehicleDetail'"'+'"'.CustRecord.ContactInfo'"'+'"'.CustRecord.ContactInfo.Address'"' `' >> "$script_file"
        
        echo '\set jq_result_ro `cat' "'$jq_file'" '| jq .RoRecord.Rogen.RoNo`' >> "$script_file"

        echo '\set jq_result_jobs `cat' "'$jq_file'" '| jq .RoRecord.Rolabor.OpCodeLaborInfo`' >> "$script_file"

        echo '\set jq_result_rec_serv `cat' "'$jq_file'" '| jq .RoRecord.Rogen.RecommendedServc`' >> "$script_file"

        echo '\set jq_result_parts `cat' "'$jq_file'" '| jq .RoRecord.Ropart.PartInfoByJob`' >> "$script_file"

        echo '\set jq_result_misc `cat' "'$jq_file'" '| jq .RoRecord.Romisc.MiscOpCodeInfo`' >> "$script_file"
        
        echo '\set jq_result_sublet `cat' "'$jq_file'" '| jq .RoRecord.Rosub.SubInfoByJob`' >> "$script_file"

        echo '\set jq_result_gog_amount `cat' "'$jq_file'" '| jq .RoRecord.Rogog.AllGogTotalAmts`' >> "$script_file"

        echo '\set jq_result_sublet_amount `cat' "'$jq_file'" '| jq .RoRecord.Rosub.AllSubTotalAmts`' >> "$script_file"

        echo '\set jq_result_misc_amount `cat' "'$jq_file'" '| jq .RoRecord.Romisc`' >> "$script_file"

        echo '\set jq_result_estimate `cat' "'$jq_file'" '| jq .RoRecord.Rogen.EstimateInfo`' >> "$script_file"
        
        echo '\set jq_result_customer `cat' "'$jq_file'" '| jq .+'"'{roNumber : .RoRecord.Rogen.RoNo}'"'+'"'{CustNo : .RoRecord.Rogen.CustNo}'"'+'"'{CustName : .RoRecord.Rogen.CustName}'"'+'"'.CustRecord.ContactInfo'"'+'"'.CustRecord.ContactInfo.Address'"'+'"'.CustRecord.ContactInfo.Email'"' `' >> "$script_file"

        echo '\set jq_result_warranty_claim `cat' "'$jq_file'" '| jq .RoRecord.Rowarr`' >> "$script_file"

        echo '\set jq_result_gog `cat' "'$jq_file'" '| jq  .RoRecord.Rogog.AllGogOpCodeInfo`' >> "$script_file"

        cat >>"$script_file" <<'EOF'
        INSERT INTO etl_json ("roNumber", json_document) VALUES (:'json_file_name', :'jq_result');

        INSERT INTO etl_head_detail
        SELECT * FROM json_populate_record(null::etl_head_detail, :'jq_result');

        INSERT INTO etl_customer_detail
        SELECT * FROM json_populate_record(null::etl_customer_detail, :'jq_result_customer');

        SELECT * from du_dms_reynoldsrci_model.insert_jobs(:'jq_result_ro', :'jq_result_jobs');

        SELECT * from du_dms_reynoldsrci_model.insert_rec_serv(:'jq_result_ro', :'jq_result_rec_serv');

        SELECT * from du_dms_reynoldsrci_model.insert_parts(:'jq_result_ro', :'jq_result_parts');

        SELECT * from du_dms_reynoldsrci_model.insert_miscs(:'jq_result_ro', :'jq_result_misc');

        SELECT * from du_dms_reynoldsrci_model.insert_miscs1(:'jq_result_ro', :'jq_result_misc');

        SELECT * from du_dms_reynoldsrci_model.insert_other(:'jq_result_ro', :'jq_result_gog_amount', 'GOG');

        SELECT * from du_dms_reynoldsrci_model.insert_other(:'jq_result_ro', :'jq_result_sublet_amount', 'SUBLET');

        SELECT * from du_dms_reynoldsrci_model.insert_other(:'jq_result_ro', :'jq_result_misc_amount', 'MISC');

        SELECT * from du_dms_reynoldsrci_model.insert_warranty_cliam(:'jq_result_ro', :'jq_result_warranty_claim');

        SELECT * from du_dms_reynoldsrci_model.insert_sublets(:'jq_result_ro', :'jq_result_sublet'); 

        SELECT * from du_dms_reynoldsrci_model.insert_gog_job(:'jq_result_ro', :'jq_result_gog');

        SELECT * from du_dms_reynoldsrci_model.insert_estimate(:'jq_result_ro', :'jq_result_estimate'); 
        
EOF
    )
}

function load_individual_json_files() {
    # echo "Processor status: 4/21 Loading Individual ROs Started"
    # sleep 1
    progress "Loading Individual ROs (can take a while)"
    SCRIPT_WORK_DIR="$WORK_DIR"/json-load-script
    PSQL_SCRIPT_FILE="$WORK_DIR"/json-load-script/script.psql
    clear_dir "$SCRIPT_WORK_DIR"
    echo $(date)
    (
        i=1
        j=1
        limit=500
        cd "$SINGLE_RO_JSON_DIR" || die "Failed to CD to $SINGLE_RO_JSON_DIR"
        for json_file in ./*; do
            append_ro_to_script "$json_file" "$PSQL_SCRIPT_FILE"
            pageLimit=$(($j * $limit)) 
            if [[ "$i" -eq "$pageLimit" ]]; then
                progress "$(date +'%H:%M:%S') : Ready to Load next $limit ROs"
                psql_local --quiet --file "$PSQL_SCRIPT_FILE" > /dev/null || die "Bulk SQL Load Failed"
                truncate -s 0 "$PSQL_SCRIPT_FILE"
                progress "$(date +'%H:%M:%S') : $pageLimit ROs Imported"
                i=$(($i + 1))
                j=$(($j + 1)) 
            else
               i=$(($i + 1))
            fi
        done

	    progress "Ready to Load remaining ROs"
        psql_local --quiet --file "$PSQL_SCRIPT_FILE" > /dev/null || die "Bulk SQL Load Failed"
        echo $(date)
        progress "Individual ROs Imported"
    ) || die "Failed to load split ROs"
    # echo "Processor status: 4/21 Loading Individual ROs Completed"
    # sleep 1

}

function generate_jq_transforms() {
    # # echo "Processor status: 3/25 Generating JQ Transforms Started"
    # sleep 1
    progress "Generating JQ Transforms"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Head_detail');"  > "$JQ_HEAD"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Job_detail');"   > "$JQ_JOB"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Parts_detail');" > "$JQ_PARTS"
    psql_local --quiet --tuples-only --no-align --command "SELECT create_jq_transform_from_model('Customer_detail');" > "$JQ_CUSTOMER"
    # # echo "Processor status: 3/25 Generating JQ Transforms Completed"
    # sleep 1
}

function create_schema_from_model() {
    # echo "Processor status: 2/21 Creating Schema from Model Started"
    # sleep 1
    progress "Creating Schema from Models"
    psql_local --quiet \
               --set=DU_ETL_HOME="$DU_ETL_HOME" \
               >/dev/null \
<<'SQL'
SET client_min_messages = 'warning';
DROP SCHEMA  IF EXISTS  du_dms_reynoldsrci_model CASCADE;
CREATE SCHEMA du_dms_reynoldsrci_model;
SELECT set_config('search_path', 'du_dms_reynoldsrci_model', true);

\i :DU_ETL_HOME/DU-Transform/client/process/create-client-functions.psql

CREATE TABLE etl_json (
    "roNumber" text NOT NULL PRIMARY KEY,
    json_document json
);

CREATE TABLE etl_model (
    id serial primary key,
    field_name text not null,
    is_array boolean null, --can only know if has_children is true
    has_children boolean not null,
    target_table text null --update nulls after loading
);

-- Table to store exceptional ROs
CREATE TABLE etl_problematic_ros (
    "roNumber" text NOT NULL,
    comment text
);

-- Table to Misc Job A
CREATE TABLE etl_misc_job_a_detail (
    misc_code text NOT NULL,
    misc_desc text,
    count text
);

-- Table to invoice master
CREATE TABLE etl_invoicemaster (
    dms_store_id text,
    invoicenumber text,
    invoice_status text,
    accounting_make_code text ,
    departmentcode text,
    opendate text,
    closedate text,
    voiddate text,
    vehicle_identifier text,
    vehicle_year text,
    vehicle_make_name text,
    operation_count int
);

CREATE TABLE du_dms_reynoldsrci_model.etl_void_open (LIKE du_dms_reynoldsrci_model.etl_invoicemaster INCLUDING ALL);

CREATE TABLE etl_source_labor (
    invoicenumber         text,
    department            text,
    posteddate            text,  
    line_number           text,
    opcode                text,
    opdescription         text,
    sale_type             text NULL,
    book_hours            text,
    tech_hours            text NULL,
    sale_amount           text,
    gross_amount          text,
    vehicle_make          text,
    vehicle_model         text
);

CREATE  TABLE etl_source_parts (
    invoicenumber text,
    posteddate date,
    partnumber text,
    partdescription text,
    quantity text,
    part_cost text,
    part_sale text,
    sale_type text,
    ro_cost text,
    ro_sale text,
    ro_gross text
);

\cd :DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/data-dictionary

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-head.csv' with (format csv, header true)
update etl_model set target_table = 'Head_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-job.csv' with (format csv, header true)
update etl_model set target_table = 'Job_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-spginfo.csv' with (format csv, header true)
update etl_model set target_table = 'Spg_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-recommendedservice.csv' with (format csv, header true)
update etl_model set target_table = 'Recommended_service_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-parts.csv' with (format csv, header true)
update etl_model set target_table = 'Parts_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-customer.csv' with (format csv, header true)
update etl_model set target_table = 'Customer_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-other.csv' with (format csv, header true)
update etl_model set target_table = 'Other_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-misc.csv' with (format csv, header true)
update etl_model set target_table = 'Misc_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-misc1.csv' with (format csv, header true)
update etl_model set target_table = 'Misc_detail1' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-sublet.csv' with (format csv, header true)
update etl_model set target_table = 'Sublet_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-gog.csv' with (format csv, header true)
update etl_model set target_table = 'Gog_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-tech-details.csv' with (format csv, header true)
update etl_model set target_table = 'Tech_detail' where target_table IS NULL;


\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-labor-details.csv' with (format csv, header true)
update etl_model set target_table = 'Labor_details' where target_table IS NULL;


\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-warrantyclaim.csv' with (format csv, header true)
update etl_model set target_table = 'Warrantyclaim_detail' where target_table IS NULL;

\copy etl_model (field_name, is_array, has_children) from 'closed-repair-order-etl-estimate.csv' with (format csv, header true)
update etl_model set target_table = 'estimate_details' where target_table IS NULL;

-- Mainly dynamic code builder functions
\i :DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/src/parse/support-schema.psql

SELECT *, build_etl_table(target_table)
  FROM (SELECT DISTINCT target_table FROM etl_model) src;

-- Insert datas to the tables
\i :DU_ETL_HOME/DU-DMS/DMS-ReynoldsRCI/src/parse/data-insert-schema.psql

SQL
# echo "Processor status: 2/21 Creating Schema from Model Completed"
    # sleep 1
}

function move_or_process_duplicate_ro() {
    # When processing a given RO#, whether the Processing script encountered
    # that said RO# previously in its cycle. If it has/does placing the now
    # current duplicate file into a "duplicates" directory while skipping the
    # processing. And perform a diff on the active and previous files to see
    # whether they are indeed the same (which they should be) and add a ".diff"
    # file to the duplicates directory if they are not.

    if [[ -f "$1"/"$2".json ]]; then
        mkdir -p "$JSON_CONVERSION_WORK_DIR"/duplicates
        diff "$3" "$1"/"$2".json > /dev/null
        if [[ $? -ne 0 ]]; then
            diff --suppress-common-lines "$3" "$1"/"$2".json > "$JSON_CONVERSION_WORK_DIR"/duplicates/"$2".json.diff
        fi
        mv "$3" "$JSON_CONVERSION_WORK_DIR"/duplicates/"$2".json
    else
        mv "$3" "$1"/"$2".json
    fi
}

function place_lines_into_their_own_files() {
    input_file_with_path="$(pwd)/${1:?file name required}"
    dos2unix -q "$input_file_with_path"

    DUPLICATE_RO_JSON_DIR="$JSON_CONVERSION_WORK_DIR"/duplicates
    mkdir -p "$DUPLICATE_RO_JSON_DIR"

    export SINGLE_RO_JSON_DIR
    export DUPLICATE_RO_JSON_DIR

    PRG_PERFORM_SPLIT=$(cat <<'PERL'
    use warnings;
    use strict;

    my $outdir = $ENV{'SINGLE_RO_JSON_DIR'};
    my $dupdir = $ENV{'DUPLICATE_RO_JSON_DIR'};

    while (<>) {
        /"RoNo":"([^"]+)"/;
        my $ronum_from_line = "$1";
	$ronum_from_line =~ s/"//g; 
        if (-f ($outdir . '/' . $ronum_from_line . '.json')) {
            open ROOUT, '>' . $dupdir . '/' . $ronum_from_line . '.json';
        } else {
            open ROOUT, '>' . $outdir . '/' . $ronum_from_line . '.json';
        }
        print ROOUT $_;
        close ROOUT;
    }
    exit
PERL
)
    perl -e "$PRG_PERFORM_SPLIT" "$input_file_with_path" || die "Failed to split"
}

function findMissingROFromInvoiceMaster() {
    # echo "Processor status: 8/21 Finding Missing RO  with respective to Invoice master CSV file Started"
    # sleep 1
    progress "Finding Missing RO  with respective to Invoice master CSV file"
    psql_local --quiet <<'SQL'
    BEGIN;
    SET client_min_messages = 'WARNING';
        INSERT INTO du_dms_reynoldsrci_model.etl_void_open
        SELECT * FROM du_dms_reynoldsrci_model.etl_invoicemaster
        WHERE invoicenumber IN(
        WITH series_gap (max_ser_ro, min_ser_ro) AS (
                SELECT * FROM (SELECT
                                    "roNumber"::integer,
                                    lag("roNumber"::integer) OVER ranges,
                                    "roNumber"::integer - lag("roNumber"::integer) OVER ranges AS diff
                            FROM du_dms_reynoldsrci_model.etl_head_detail
                        WINDOW ranges AS (order by "roNumber"::integer))t
                )
        SELECT generate_series(min_ser_ro+1, max_ser_ro-1)::text AS missing_ros
        FROM series_gap	
        );
    COMMIT;	
SQL
    # echo "Processor status: 8/21 Finding Missing RO  with respective to Invoice master CSV file Completed"
    # sleep 1

}
