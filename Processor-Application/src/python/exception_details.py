import csv
import os
import sys
import json

exception_config = [
	{"TYPE": 'punch_time_missing', "RO_POS": '0', "RO_HEADER": 'roNumber', "FOOTER": 'false', "FILE": 'punch_time_missing.csv'},
	{"TYPE": 'extraros_in_xml', "RO_POS": '0', "RO_HEADER": 'roNumber', "FOOTER": 'false', "FILE": 'extraros_in_xml.csv'},
	{"TYPE": 'exception-closed-invoices', "RO_POS": '1', "RO_HEADER": 'RONumber', "FOOTER": 'false', "FILE": 'exception-closed-invoices.csv'},
	{"TYPE": 'im_opened_closed_rci_ros', "RO_POS": '0', "RO_HEADER": 'ronumber', "FOOTER": 'false', "FILE": 'im_opened_closed_rci_ros.csv'},
	{"TYPE": 'missing-invoices', "RO_POS": '1', "RO_HEADER": 'invoicenumber', "FOOTER": 'false', "FILE": 'missing-invoices.csv'},
	{"TYPE": 'suffixed-invoices', "RO_POS": '0', "RO_HEADER": 'roNumber', "FOOTER": 'false', "FILE": 'suffixed-invoices.csv'},
	{"TYPE": 'Misc-Job-A-exception', "RO_POS": '0', "RO_HEADER": 'MiscCode', "FOOTER": 'false', "FILE": 'Misc-Job-A-exception.csv'},
	{"TYPE": 'misc_paytype_exception', "RO_POS": '0', "RO_HEADER": 'RoNumber', "FOOTER": 'false', "FILE": 'misc_paytype_exception.csv'}
	]

input_folder = sys.argv[1]
output_folder = sys.argv[2]

output_file_name = "All_exception_details.csv"
ouput_file_path = os.path.join(output_folder, output_file_name)
type_key = "TYPE"
pos_key = "RO_POS"
header_key = "RO_HEADER"
file_key = "FILE"
footer_key = "FOOTER"
Ro_position = None
type_header = 'Type'
ro_header = 'RoNumber'
data_header = 'Data'
output_header = [type_header, ro_header, data_header]

def create_empty_csv():
    if not os.path.exists(ouput_file_path):
        with open(ouput_file_path, 'w', newline='') as csv_file:
            writer = csv.writer(csv_file)
            writer.writerow(output_header)
        print(f"Empty CSV file '{ouput_file_path}' created with header.")
    else:
        print(f"CSV file '{ouput_file_path}' already exists.")

def save_output(ro_column_value, exception_type, Ro_data):
	
	# ouput_file_path = os.path.join(output_folder, out_file_name)
	file_exists = os.path.exists(ouput_file_path)
	with open(ouput_file_path, 'a', newline='') as csv_file:

		csv_writer = csv.writer(csv_file)
		
		if not file_exists:
			if output_header:
				csv_writer.writerow(output_header)
		# else:
		# 	print ("File Exists!")
		rows = [''] * len(output_header)
		for header in output_header:
			# print ("header: ", header)
			if header == type_header:
				rows[output_header.index(header)] = exception_type

			elif header == ro_header:
				rows[output_header.index(header)] = ro_column_value

			elif header == data_header:
				rows[output_header.index(header)] = Ro_data

			else:
				print ("Unkonown Header!")	
						
		csv_writer.writerow(rows)


	# print(f'Data has been written to {ouput_file_path}.')

def get_invoice_number(csv_file_path, index):
	exception_dict = exception_config[index]
	ro_header = exception_dict[header_key]
	exception_type = exception_dict[type_key]
	footer_type = exception_dict[footer_key]
	# print ("ro_header: ", ro_header)
	json_objects = []
	ro_object = []
	for key, value in exception_dict.items():
		if key == "RO_POS":
			Ro_position = int(value)
			# print(f"{key}: {value}")
			with open(csv_file_path, 'r') as csv_file:
				csv_reader = csv.DictReader(csv_file)
				for row in csv_reader:
					if row:
						# print ("ROW: ", row)
						ro_column_value = row[ro_header]
						# print("ro_column_value: ", ro_column_value)
						json_objects.append(row)
						ro_object.append(ro_column_value)
					else:
						print ("Row is empty.")		
				# print ("json_objects length: ", len(json_objects))
				if footer_type == 'true':
					json_objects = json_objects[:-1]
					ro_object = ro_object[:-1]

				for json_object in json_objects:
					Ro_data = json.dumps(json_object, indent=2)
					# print ("Ro_data: ", Ro_data)
					ro_object_index = json_objects.index(json_object)
					save_output(ro_object[ro_object_index], exception_type, Ro_data)
		else:
			Ro_position = None

def extract_data():
	for exception_file in os.listdir(input_folder):
		if exception_file.endswith(".csv"):
			for index, exception_types in enumerate(exception_config):
				if file_key in exception_types and exception_types[file_key] == exception_file:
					# print(f'The file_key: {file_key}={exception_file} exists in the list. index: {index}')
					current_file = os.path.join(input_folder, exception_file)
					get_invoice_number(current_file, index)
				# else:
				# 	print(f'The file_key: {file_key}={exception_file} does not exist in the list.')
	create_empty_csv()

extract_data()
