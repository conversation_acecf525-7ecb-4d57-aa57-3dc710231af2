#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/ansi-color-constants.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

shopt -s nullglob

SCRIPT_DIR="$(cd $(dirname $0) && pwd)"

WORK_DIR="$HOME/tmp/du-etl-dms-reynolds3pa-extractor-work/process-json-temp"
ZIP_WORK_DIR="$WORK_DIR"/zip-temp
INVOICE_WORK_DIR="$WORK_DIR"/invoice-temp
PROCESSING_MODE='none'
FILE_TO_PROCESS=''
PERFORM_ZIP='false'
PERFORM_PROXY_RO_BUILD='false'
INPUT_BUNDLE_ZIP=''
INPUT_ZIP_FILE_NAME=''
INPUT_BUNDLE_DIR=''
DO_ZAP_INPUT='false'

OUTPUT_FILE_PREFIX=''

INPUT_STORE_NAME=''

SINGLE_STORE_FLAG='false'
CUSTOM_BRANCH_NAME=''

TRANSFORMS_DIR="$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/runtime/transforms
REPO_DIR="$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA

DEAD_LETTER_DIR=

source "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/Reynolds3PA.env

source "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/Processor-Application/src/bash/process-library.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/Processor-Application/src/bash/process-zip.bash-mixin

source "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/Processor-Application/src/bash/filetype/closed-ro.bash-mixin
source "$DU_ETL_HOME"/DU-DMS/DMS-Reynolds3PA/Processor-Application/src/bash/process-proxyinvoice.bash-mixin

function psql_local() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"

    return $?
}

function psql_etl_proxy() {
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${PROXY_SCHEMA},${SRC_PROXY_SCHEMA}'" \
         --set=ON_ERROR_STOP=1 "$@"
}

function main() {
    parse_options "$@"
    initialize
    if execute_processing_mode "$PROCESSING_MODE"; then
         cleanup "$PROCESSING_MODE"
    else
        move_input_to_deadletter_and_abort "$PROCESSING_MODE"
    fi
}

function execute_processing_mode() {
    proc_mode="${1:?Processing Mode Specification Required}"
    (
        case "$proc_mode" in
                zip) process_zip_file ;;
                post-processed-proxies) generate_proxies_from_zip ;;
        esac
    )
    return $?
}

function initialize() {
    mkdir -p "$WORK_DIR"
    clear_dir "$WORK_DIR"

    [[ -d "${DEAD_LETTER_DIR:?Please Pass a Dead-Letter Directory Path}" ]] \
        || die "Dead Letter Directory Must Exist"
}

function move_input_to_deadletter_and_abort() {
    proc_mode="${1:?Processing Mode Specification Required}"
    yell "Moving input to dead-letter bin: $DEAD_LETTER_DIR"

    case "$proc_mode" in
        zip)
            cp "$INPUT_BUNDLE_ZIP" "$DEAD_LETTER_DIR"/
            cleanup "$proc_mode"
            ;;
    esac
}

function cleanup() {
    proc_mode="${1:?Processing Mode Specification Required}"

    progress "Clearing working directory and remove input if requested"
    clear_dir "$WORK_DIR"

    if [[ "$DO_ZAP_INPUT" = 'true' ]]; then
        if [[ -f "$INPUT_BUNDLE_ZIP" ]]; then
            remove_input_zip
        else
            yell "Zap input not implemented for non-zip files"
        fi
    fi
}

function remove_input_zip() {
    [[ -f "$INPUT_BUNDLE_ZIP" ]] || die "Input zip must exist to be removed: $INPUT_BUNDLE_ZIP"
    say "Zapping Input Zip File As Requested"
    rm "$INPUT_BUNDLE_ZIP"
}

function parse_options() {
    OPT_ENV=$(getopt --options c:w: --long output-prefix:,build-proxies-using:,dead-letter:,no-build-proxies,build-proxies,work-dir:,bundle-dir:,bundle-id:,no-zip,zip,input-dir:,input-zip:,input-store-name:,single-store-flag:,custom-branch-name:,zap-input -n 'process-json' -- "$@")
    if [[ ! $? = '0' ]]; then
        die "Option Parsing Failed"
    fi

    eval set -- "$OPT_ENV"
    while true; do
        case "$1" in

            --dead-letter)
                DEAD_LETTER_DIR="$2"
                shift 2
                ;;
            --bundle-dir)
                BUNDLE_OUTPUT_DIRECTORY="$2"
                shift 2
                ;;
            --bundle-id)
                BUNDLE_IDENTIFIER="$2"
                shift 2
                ;;
            --input-dir)
                INPUT_BUNDLE_DIR="$2"
                PROCESSING_MODE='full'
                shift 2
                ;;
            --input-zip)
                INPUT_BUNDLE_ZIP="$2"
                INPUT_ZIP_FILE_NAME="$(basename $INPUT_BUNDLE_ZIP)"
                PROCESSING_MODE='zip'
                PERFORM_ZIP='true'
                shift 2
                ;;
            --input-store-name)
                INPUT_STORE_NAME="$2"
                shift 2
                ;;
             --single-store-flag)
                SINGLE_STORE_FLAG="$2"
                shift 2
                ;;
             --custom-branch-name)
                CUSTOM_BRANCH_NAME="$2"
                shift 2
                ;;
            --output-prefix)
                OUTPUT_FILE_PREFIX="$2"
                shift 2
                ;;
            --zap-input)
                DO_ZAP_INPUT='true'
                shift
                ;;
            --build-proxies)
                PERFORM_PROXY_RO_BUILD='true'
                shift
                ;;
            --build-proxies-using)
                POST_PROCESSED_ZIP_FILE="$2"
                PROCESSING_MODE="post-processed-proxies"
                shift 2
                ;;
            --no-build-proxies)
                PERFORM_PROXY_RO_BUILD='false'
                shift
                ;;
            --no-zip)
                PERFORM_ZIP='false'
                shift
                ;;
            --zip)
                PERFORM_ZIP='true'
                shift
                ;;
            --work-dir)
                say "Work Dir Change: $2"
                WORK_DIR="$2"
                shift 2
                ;;
            --) shift ; break ;;
            *) die "Unrecognized Argument $1"
        esac
    done
}

main "$@"
