#!/bin/bash

# Directory where files are located
DIR="/etl/etl-vagrant/etl-reynolds/webhook-reynolds"
LOGDIR="/etl/log"
mkdir -p $LOGDIR
cd $LOGDIR
LOGFILE="delete-webhook-files.log"
# Check if the file exists, if not, create it
if [ ! -f "$LOGFILE" ]; then
    touch "$LOGFILE"
    echo "$(date "+%Y-%m-%d %H:%M:%S") - Log file created." >> "$LOGDIR/$LOGFILE"
fi
cd -

echo "Date : $(date)" >> "$LOGDIR/$LOGFILE"
# Get the date 6 months ago in YYYYMMDDHHMMSS format
six_months_ago=$(date -d "6 months ago" +"%Y%m%d%H%M%S")

# Loop through files matching the pattern
for file in "$DIR"/*; do
    # Extract datetime part from filename (assumes "_" is the separator)
    datetime_part=$(echo "$file" | awk -F'_' '{print $(NF)}' | cut -d'.' -f1)

    # Check if the extracted datetime is numeric and compare
    if [[ "$datetime_part" =~ ^[0-9]+$ && "$datetime_part" -lt "$six_months_ago" ]]; then
        echo "Deleting: $file" >> "$LOGDIR/$LOGFILE"
        rm -f "$file"
    fi
done