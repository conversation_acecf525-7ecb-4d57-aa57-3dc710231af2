#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash

BUNDLE_ZIP_FILE="${1:?Zip File Required}"
REYNOLDS_ETL_DIR="${2:?REYNOLDS Zip Directory Required}"
FINAL_SOURCE_ZIP_DIR="${3:?Remote Distribution Directory Required}"
IMPORT_DIR="${4:?Import Directory Required}"

ETL_ZIP_FILE_PATH="$REYNOLDS_ETL_DIR"/"$(basename $BUNDLE_ZIP_FILE .zip)"-ETL.zip
IMPORT_ZIP_FILE_PATH="$IMPORT_DIR"/"$(basename $BUNDLE_ZIP_FILE .zip)"-IMPORT.zip

[[ -f "$BUNDLE_ZIP_FILE" ]] || die "Zip File Must Exist"
[[ -d "$REYNOLDS_ETL_DIR"  ]] || die "REYNOLDS ETL Zip Directory Must Exist"
[[ -d "$IMPORT_DIR"      ]] || die "Import Zip Directory Must Exist"

progress "Distributing ETL Data: $(basename $BUNDLE_ZIP_FILE)"

BASEFILE="$(basename $BUNDLE_ZIP_FILE)"
BUNDLE_DIR=$(echo $BUNDLE_ZIP_FILE | sed "s/$BASEFILE//")temp
rm -rf "$BUNDLE_DIR"
mkdir -p "$BUNDLE_DIR"

progress "Distributing ETL Data: $(basename $BUNDLE_ZIP_FILE)"
unzip -q "$BUNDLE_ZIP_FILE" -d "$BUNDLE_DIR"

TSV_FILE=$BUNDLE_DIR/processing-result/proxy-text.tsv

if [[ ! -f "$TSV_FILE" ]]; then
        zip -j -q "$ETL_ZIP_FILE_PATH" "$BUNDLE_DIR/process-json-csv-results.pgdump"
else
        zip -j -q "$ETL_ZIP_FILE_PATH" "$BUNDLE_DIR/process-json-csv-results.pgdump" "$TSV_FILE"
fi

CONFIG_DIR=$BUNDLE_DIR/processing-result/
file_list=$(ls $CONFIG_DIR/config_*)

AUDIT_DIR="/etl/audit-import"
HALT_FILE=$BUNDLE_DIR/processing-result/halt-import.txt
SCHEDULER_ID=$(cat "${BUNDLE_DIR}/processing-result/scheduler-id.txt")

    if [[ -f "$HALT_FILE" && -s "$HALT_FILE" ]]; then
        echo "This store has import halt exception"
        mkdir -p "/etl/audit-import-halt"
        AUDIT_DIR="/etl/audit-import-halt"
    fi
AUDIT_ZIP_FILE_PATH="$AUDIT_DIR"/"$(basename $BUNDLE_ZIP_FILE .zip)" 

for file in $file_list; do
echo -e "\nSCHEDULER_ID=$SCHEDULER_ID" >> $file
filename=$(basename "$file" .bash)
STORE_DETAIL="${filename#*_}"
if [[ ! -f "$TSV_FILE" ]]; then
        zip -j -q "$AUDIT_ZIP_FILE_PATH"_"$SCHEDULER_ID"_"$STORE_DETAIL"-ETL.zip "$BUNDLE_DIR/processing-result/process-json-csv-results-${SCHEDULER_ID}-${STORE_DETAIL}.pgdump" "$file"
        else
        zip -j -q "$AUDIT_ZIP_FILE_PATH"_"$SCHEDULER_ID"_"$STORE_DETAIL"-ETL.zip "$BUNDLE_DIR/processing-result/process-json-csv-results-${SCHEDULER_ID}-${STORE_DETAIL}.pgdump" "$TSV_FILE" "$file"
fi
ZIP_FILE_NAME="$AUDIT_ZIP_FILE_PATH"_"$SCHEDULER_ID"_"$STORE_DETAIL"-ETL.zip      
MANUAL_IMPORT_DIR="/etl/scheduler-manual-import"
mkdir -p "$MANUAL_IMPORT_DIR"
cp "$ZIP_FILE_NAME" "$MANUAL_IMPORT_DIR/"
echo "Zip file $ZIP_FILE_NAME copied to manual import dir $MANUAL_IMPORT_DIR"
done

rm -rf "$BUNDLE_DIR"

# unzip -q -p "$BUNDLE_ZIP_FILE" process-json-csv-results.pgdump \
#       | zip -q "$ETL_ZIP_FILE_PATH" - \
#       || die "Split out of ETL data failed"

# echo -e "@ -\n@=process-json-csv-results.pgdump" \
#      | zipnote -q -w "$ETL_ZIP_FILE_PATH" \
#      || die "Renaming ETL zip contents failed"



progress "Distributing Full Bundle"

cp -v "$BUNDLE_ZIP_FILE" "$FINAL_SOURCE_ZIP_DIR" || die "Full Bundle Copy Failed"

say "Bundle Distribution Completed"
