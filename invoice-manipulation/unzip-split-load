#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

WORK_DIR="$HOME"/tmp/du-etl-inv-reynoldsrci

source "$DU_ETL_HOME"/invoice-manipulation-common/shared/unzip-split-load--template.bash

RAWBASELOGFILENAME="$WORK_DIR"/raw-log-

function move_split_work_dir_items_to_destination() {

PERL_RONUM=$(cat <<'PERL'
    use warnings;
    use strict;

    my $ronum = '';
    my $rotype = '';

    while (<>) {

        if (/\t/) {
            if (/\w{2}\+\w\d+\t\d+/) {
                s/^(.+)(\w{2}\+\w\d+)\t(\d+)(\D.+)/$1$2$3$4/;
            }
        }

        if ($ronum eq '' && /\s(?:[A-Z]\d|\d[A-Z]|[A-Z]{2})\+[A-Z](\d{1,6}[A-Z]?)/) {
            $ronum = "$1";
        }
        if ($rotype eq '' && /PAGE\s+\d+\s+OF\s+\d+\s+([^[]*)\[/) {
            $rotype = "$1";
            $rotype =~ s/ //g;
            if ($rotype eq '') {
                $rotype="ACCOUNTING";
            }
            last;
        }
    }

    if ($ronum eq '') {
        print "NA EMPTY";
        exit;
    } else {
        print "$ronum $rotype";
        exit;
    }
PERL
)

PERL_TRIM=$(cat <<'PERL'
    use warnings;
    use strict;

    my $start_of_page=0;
    my $end_of_page=0;

    while (<>) {

        if (/\t/) {
            if (/\w{2}\+\w\d+\t\d+/) {
                s/^(.+)(\w{2}\+\w\d+)\t(\d+)(\s.+)/$1$2$3$4/;
            }
        }

        if (/[\w\d]+\+\w\d{1,6}[A-Z]?/) {
            if ($end_of_page == 1) {
                print "\f\n";
            }
            $start_of_page=1;
            $end_of_page=0;

        }
        if (($start_of_page == 1) && ($end_of_page == 0)) {
            print;
        }
        if (/PAGE\s+\d+\s+OF\s+\d+\s+/) {
            $start_of_page=0;
            $end_of_page=1;
        }
    }
    exit
PERL
)

    FLDS=
    RONUM=
    ROTYP=
    destination_directory="${1:?Destination Required}"
    for partialfile in ./*; do
        FLDS=($(perl -aln -e "$PERL_RONUM" "$partialfile")) || die "$partialfile RO# locator failed"
        RONUM=${FLDS[0]}
        ROTYP=${FLDS[1]}
        if [[ "$ROTYP" =~ 'ACCOUNTINGCOPY'   || \
              "$ROTYP" =~ 'ACCOUNITNGCOPY'   || \
              "$ROTYP" = 'ACCOUNTINIGCOPY'   || \
              "$ROTYP" = 'ACCOUNTING'        || \
              "$ROTYP" = 'ACOUNTINGCOPY'     || \
              "$ROTYP" = 'ACCOUNTINGDETAIL'  || \
              "$ROTYP" = 'ACCTCOPY'          || \
              "$ROTYP" = 'ALLDETAIL'         || \
              "$ROTYP" = 'ALLDETAILCOPY-A'   || \
              "$ROTYP" = 'ALLDETAILCOPY'     || \
              "$ROTYP" = 'ALL-DETAIL'        || \
              "$ROTYP" = 'ACCTDETAIL'        || \
              "$ROTYP" = 'ACCOUNTCOPYALLDET' || \
              "$ROTYP" = 'ACCTDETAILINVOICE' || \
              "$ROTYP" = 'MANUFACTURERSCOPY-A' || \
              "$ROTYP" = 'ACCTALLDETAILCOPY' ]]; then
            perl -e "$PERL_TRIM" "$partialfile" \
                 > "$destination_directory"/"$RONUM"
            echo "$RONUM" >> "$RAWBASELOGFILENAME"-accountingcopy
            echo "$RONUM" >> "$RAWBASELOGFILENAME"-"$ROTYP"
        elif [[ "$ROTYP" = 'ACCOUNTING-COPY' ]]; then
            perl -e "$PERL_TRIM" "$partialfile" \
                 > "$destination_directory"/"$RONUM"
            echo "$RONUM" >> "$RAWBASELOGFILENAME"-accountingcopy
            echo "$RONUM" >> "$RAWBASELOGFILENAME"-"$ROTYP"
        elif [[ "$ROTYP" = "EMPTY" ]]; then
            # discard empty files (blank lines only)
            # files that get spit out by csplit
            rm "$partialfile"
            # Do Not Log (at all) as this is expected
        else
            mkdir -p "$destination_directory"/"$ROTYP"
            perl -e "$PERL_TRIM" "$partialfile" \
                 > "$destination_directory"/"$ROTYP"/"$RONUM"
            echo "$RONUM" >> "$RAWBASELOGFILENAME"-"$ROTYP"
        fi
    done
}

function dms_hook_clean_input_document() {
PERL_CLEAN_PAGEMARKERS=$(cat <<'PERL'
    use warnings;
    use strict;

    while (<>) {
        if (/\[CONTINUED/ || /\[\s+END\s+OF/) {
            s/\t//g;
        }
        print;
    }
    exit
PERL
)
    LIVE_FILE="${1:?File Name Required}"
    TMP_CLEAN_FILE="$WORK_DIR"/cleaned-file.txt
    if [[ -f "$TMP_CLEAN_FILE" ]]; then
        rm "$TMP_CLEAN_FILE"
    fi
    perl -e "$PERL_CLEAN_PAGEMARKERS" "$LIVE_FILE" > "$TMP_CLEAN_FILE"
    cp "$TMP_CLEAN_FILE" "$LIVE_FILE"
}

function perform_split() {
    csplit -sz - < "${1:?File Required}" \
        '/END  OF  INVOICE/+1' \
        '{*}'
}

main "$@"

if [[ -f "$RAWBASELOGFILENAME"-ALLDETAIL-clean ]]; then
    say "Evaluating supplemental All Detail files"
    (
        cd "$WORK_DIR"
        find . -maxdepth 1 -name "raw-log-*" -exec bash -c 'sort {} | uniq > {}-clean' \;
        echo 'Unique "All Detail" files not present in "Accounting Copy"'
        wc -l < <(comm -13 \
                       "$RAWBASELOGFILENAME"-accountingcopy-clean \
                       "$RAWBASELOGFILENAME"-ALLDETAIL-clean)
    )
fi

say "Document Type Log Files Present"
ls -lh "$RAWBASELOGFILENAME"*

exit
