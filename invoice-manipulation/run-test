#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

RESULT_DIR=/tmp/du-etl-inv-reynoldsrci-test-run

mkdir -p "$RESULT_DIR"



function psql_qry() {
    psql "service=$GGS_LOCAL_PG_SERVICE" \
         --set=ON_ERROR_STOP=1 \
         "$@"
}

function test_reynoldsrci_invoice_1() {
    RESULT_OUT="$RESULT_DIR"/reynoldsrci-invoice-1.result
    EXPECTED_OUT="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/invoice-manipulation/src/test/data/output/reynoldseci-invoice-1.out
    if [[ -f "$RESULT_OUT " ]]; then
        rm "$RESULT_OUT"
    fi

    psql_qry -c "DELETE FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';"

    BEFORECNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")
    [[ "$BEFORECNT" = '0' ]] || die "Unexpected Before Count: $BEFORECNT; should be zero"

    ./unzip-split-load \
        --src-file src/test/data/reynoldsrci-invoice-1.zip \
        --ggs-db "$GGS_LOCAL_PG_SERVICE" \
        --store-id '[PIMS]SYSTEM'

    AFTERCNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")

    [[ "$AFTERCNT" = '1' ]] || die "Unexpected After Count: $AFTERCNT; should be one"

    psql_qry -Atc "SELECT invoicesource FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM' ORDER BY invoicenumber" \
             > "$RESULT_OUT"

    if [[ "${1:-test}" = 'copy' ]]; then
        cp "$RESULT_OUT" "$EXPECTED_OUT"
    fi

    diff -q "$EXPECTED_OUT" "$RESULT_OUT" || die "Invoice Files Differ"
}

function test_c_and_non_c() {
    RESULT_OUT="$RESULT_DIR"/test_c_and_non_c.result
    EXPECTED_OUT="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/invoice-manipulation/src/test/data/output/test_c_and_non_c.out
    if [[ -f "$RESULT_OUT " ]]; then
        rm "$RESULT_OUT"
    fi

    psql_qry -c "DELETE FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';"

    BEFORECNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")
    [[ "$BEFORECNT" = '0' ]] || die "Unexpected Before Count: $BEFORECNT; should be zero"

    ./unzip-split-load \
        --src-file src/test/data/172903_and_172903C.txt \
        --ggs-db "$GGS_LOCAL_PG_SERVICE" \
        --store-id '[PIMS]SYSTEM'

    AFTERCNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")

    [[ "$AFTERCNT" = '1' ]] || die "Unexpected After Count: $AFTERCNT; should be one"

    psql_qry -Atc "SELECT invoicesource FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM' ORDER BY invoicenumber" \
             > "$RESULT_OUT"

    if [[ "${1:-test}" = 'copy' ]]; then
        cp "$RESULT_OUT" "$EXPECTED_OUT"
    fi

    diff -q "$EXPECTED_OUT" "$RESULT_OUT" || die "Invoice Files Differ"
}

function test_invoices_with_tabs() {
    RESULT_OUT="$RESULT_DIR"/Test-Invoices-With-Tabs.result
    EXPECTED_OUT="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/invoice-manipulation/src/test/data/output/Test-Invoices-With-Tabs.out
    if [[ -f "$RESULT_OUT " ]]; then
        rm "$RESULT_OUT"
    fi

    psql_qry -c "DELETE FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';"

    BEFORECNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")
    [[ "$BEFORECNT" = '0' ]] || die "Unexpected Before Count: $BEFORECNT; should be zero"

    ./unzip-split-load \
        --src-file src/test/data/Test-Invoices-With-Tabs.zip \
        --ggs-db "$GGS_LOCAL_PG_SERVICE" \
        --store-id '[PIMS]SYSTEM'

    AFTERCNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")

    [[ "$AFTERCNT" = '2' ]] || die "Unexpected After Count: $AFTERCNT; should be two"

    psql_qry -Atc "SELECT invoicesource FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM' ORDER BY invoicenumber" \
             > "$RESULT_OUT"

    if [[ "${1:-test}" = 'copy' ]]; then
        cp "$RESULT_OUT" "$EXPECTED_OUT"
    fi

    diff -q "$EXPECTED_OUT" "$RESULT_OUT" || die "Invoice Files Differ"
}

function test_dual_deep_text_files() {
    RESULT_OUT="$RESULT_DIR"/DualTextFiles.result
    EXPECTED_OUT="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/invoice-manipulation/src/test/data/output/DualTextFiles.out
    if [[ -f "$RESULT_OUT " ]]; then
        rm "$RESULT_OUT"
    fi

    psql_qry -c "DELETE FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';"

    BEFORECNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")
    [[ "$BEFORECNT" = '0' ]] || die "Unexpected Before Count: $BEFORECNT; should be zero"

    ./unzip-split-load \
        --src-file src/test/data/DualTextFiles.zip \
        --ggs-db "$GGS_LOCAL_PG_SERVICE" \
        --store-id '[PIMS]SYSTEM'

    AFTERCNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")

    [[ "$AFTERCNT" = '4' ]] || die "Unexpected After Count: $AFTERCNT; should be four"

    psql_qry -Atc "SELECT invoicesource FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM' ORDER BY invoicenumber" \
             > "$RESULT_OUT"

    if [[ "${1:-test}" = 'copy' ]]; then
        cp "$RESULT_OUT" "$EXPECTED_OUT"
    fi

    diff -q "$EXPECTED_OUT" "$RESULT_OUT" || die "Invoice Files Differ"
}

function test_general_batch_two() {
    RESULT_OUT="$RESULT_DIR"/Test-Invoices-2.result
    EXPECTED_OUT="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/invoice-manipulation/src/test/data/output/Test-Invoices-2.out
    if [[ -f "$RESULT_OUT " ]]; then
        rm "$RESULT_OUT"
    fi

    psql_qry -c "DELETE FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';"

    BEFORECNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")
    [[ "$BEFORECNT" = '0' ]] || die "Unexpected Before Count: $BEFORECNT; should be zero"

    ./unzip-split-load \
        --src-file src/test/data/Test-Invoices-2.txt \
        --ggs-db "$GGS_LOCAL_PG_SERVICE" \
        --store-id '[PIMS]SYSTEM'

    AFTERCNT=$(psql_qry -Atc "SELECT count(*) FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM';")

    [[ "$AFTERCNT" = '6' ]] || die "Unexpected After Count: $AFTERCNT; should be six"

    psql_qry -Atc "SELECT invoicesource FROM fixedopsserviceinvoicebase WHERE s_id = '[PIMS]SYSTEM' ORDER BY invoicenumber" \
             > "$RESULT_OUT"

    if [[ "${1:-test}" = 'copy' ]]; then
        cp "$RESULT_OUT" "$EXPECTED_OUT"
    fi

    diff -q "$EXPECTED_OUT" "$RESULT_OUT" || die "Invoice Files Differ"
}

test_reynoldsrci_invoice_1 $1
test_c_and_non_c $1
test_invoices_with_tabs $1
test_dual_deep_text_files $1
test_general_batch_two $1

say "OK"

exit
