#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
export DU_ETL_DEBUG_MODE=2;
DO_COPY='false'

if [[ "${1:-check}" = 'copy' ]]; then
    DO_COPY='true'
fi

TEST_FIXTURE_DIR="$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/src/test-other/processing
EXPECTED_DIR="$TEST_FIXTURE_DIR"/expected

EXTRACTED_MOCK_ZIP="$TEST_FIXTURE_DIR"/extracted-mock.zip
PROCESSED_MOCK_ZIP="$TEST_FIXTURE_DIR"/processed-mock.zip

WORK_DIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work
TMP_DIR="$WORK_DIR"/test-work
PROCESSED_REAL_ZIP="$WORK_DIR"/manual/dist/PROC-test-extracted-bundle.zip

function psql_local() {
    SRC_SCHEMA='du_dms_reynoldsrci'
    psql "service=$DU_ETL_PG_SERVICE options='-c search_path=${SRC_SCHEMA}_model'" \
         --set=DU_ETL_HOME="$DU_ETL_HOME" \
         --set=ON_ERROR_STOP=1 \
         "$@"
}

function main() {

    clear_dir "$TMP_DIR"

    local failed=0

    if ! perform_split_of_ro_file; then
        failed=1
    fi

    if ! load_single_json 783404; then
        failed=1
    fi

    if ! validate_extracted_bundle "$EXTRACTED_MOCK_ZIP"; then
        failed=1
    fi

    if ! validate_processed_bundle "$PROCESSED_MOCK_ZIP"; then
        failed=1
    fi

    if [[ "$DO_COPY" = 'true' ]]; then
        say "Done Copying in New Expected Output"
        clear_dir "$TMP_DIR"
        exit 0
    fi

    # The check routine above acts upon the committed data bundles
    # Below we actually take an extraction and process it again using
    # current code and check that the results match out expectations
    perform_processing_on "$EXTRACTED_MOCK_ZIP"
    echo "Processed Zip @ $PROCESSED_REAL_ZIP"

    if ! validate_processed_bundle "$PROCESSED_REAL_ZIP"; then
        failed=1
    fi

    if [[ "$failed" = 0 ]]; then
        say "OK"
    else
        die "Test Failed"
    fi

    clear_dir "$TMP_DIR"
}

function perform_split_of_ro_file() {
    rofile="$TEST_FIXTURE_DIR"/'raw-ro-file-to-be-split.json'
    clear_dir "$TMP_DIR"/'split-to-single'
    cp "$rofile" "$TMP_DIR"/'split-to-single/raw-ro-file-to-be-split.json'
    (

        SINGLE_RO_JSON_DIR="$TMP_DIR"/'split-to-single/singles'
        JSON_CONVERSION_WORK_DIR="$TMP_DIR"/'split-to-single/work'
        ZIP_WORK_DIR="$TMP_DIR"/'split-to-single/zip-work'
        PROCESSING_MODE='TEST'
        mkdir "$JSON_CONVERSION_WORK_DIR"
        mkdir "$ZIP_WORK_DIR"
        source './Processor-Application/src/bash/process-library.bash-mixin'

        source './Processor-Application/src/bash/process-zip.bash-mixin'
        mkdir "$WORK_DIR_EXTRACTION_LOG_DIR"
        mkdir "$WORK_DIR_EXTRACTION_ARCHIVE_DIR"
        mkdir "$WORK_DIR_PROCESSING_LOG_DIR"
        mkdir "$WORK_DIR_DEAD_LETTER"

        source './Processor-Application/src/bash/filetype/closed-ro.bash-mixin'
        cd "$TMP_DIR"/'split-to-single'
        process_closed_ro_json 'raw-ro-file-to-be-split.json'
        (
            cd "$SINGLE_RO_JSON_DIR"
            tree . > "$TMP_DIR"/'split-ro-tree.txt'
        )
    )

    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/'split-ro-tree.txt' "$EXPECTED_DIR"/'split-ro-tree.txt'
        progress "Copying split-ro-tree.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/'split-ro-tree.txt' "$EXPECTED_DIR"/'split-ro-tree.txt'
        if [[ $? != 0 ]]; then
            scream "Split RO Tree Does Not Match"
            return 1
        else
            progress "Split RO Tree Layout Matches"
            return 0
        fi
    fi

}

function load_single_json() {
    local ronum="${1:?RO number required}"
    local ronum_file="$TEST_FIXTURE_DIR"/single-json/"${ronum}".json
    (
        source './Processor-Application/src/bash/process-library.bash-mixin'
        create_schema_from_model
        generate_jq_transforms
        PSQL_SCRIPT_FILE="$HOME"/tmp/reynoldsrci-json-load/reynoldsrci-json-script.psql
        if [[ -f "$PSQL_SCRIPT_FILE" ]]; then
            rm "$PSQL_SCRIPT_FILE"
        fi
        mkdir -p "$TMP_DIR"/reynoldsrci-json-load
        append_ro_to_script "$ronum_file" "$PSQL_SCRIPT_FILE"

        psql_local --quiet --file "$PSQL_SCRIPT_FILE"
        if [[ ! $(psql_local -Atc 'select "roNumber" from etl_head_detail') = $ronum ]]; then
            cat "$PSQL_SCRIPT_FILE"
            scream "RO did not load as expected"
            return 1
        else
            progress "RO $ronum loaded successfully"
        fi
        return 0
    )
}

function validate_extracted_bundle() {
    local extracted_bundle="${1:?Bundle File Required}"

    [[ -f "$extracted_bundle" ]] || die "Input Bundle Not Found: $extracted_bundle"

    clear_dir "$TMP_DIR"/extracted-bundle
    unzip -q "$extracted_bundle" -d "$TMP_DIR"/extracted-bundle
    (
        cd "$TMP_DIR"/extracted-bundle
        tree . > "$TMP_DIR"/extracted-bundle-tree.txt
    )
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/extracted-bundle-tree.txt "$EXPECTED_DIR"/extracted-bundle-tree.txt
        progress "Copying extracted-bundle-tree.txt as requested"
        return 0
    else

        diff "$TMP_DIR"/extracted-bundle-tree.txt "$EXPECTED_DIR"/extracted-bundle-tree.txt
        if [[ $? != 0 ]]; then
            scream "Extracted Bundle Tree Does Not Match"
            return 1
        else
            progress "Extracted Bundle Tree Layout Matches"
            return 0
        fi
    fi
}

function validate_processed_bundle() {
    local processed_bundle="${1:?Bundle File Required}"
    local failed=0
    declare -i failed

    [[ -f "$processed_bundle" ]] || die "Input Bundle Not Found: $processed_bundle"

    clear_dir "$TMP_DIR"/processed-bundle
    unzip -q "$processed_bundle" -d "$TMP_DIR"/processed-bundle

    if ! validate_processed_root; then
        failed=1
    fi

    if ! validate_repair_order_number_summary; then
        failed=1
    fi

    if ! validate_invoice_master; then
        failed=1
    fi

    # Uncomment it if you get any dead letter detail
    # if ! validate_dead_letter_detail; then
    #     failed=1
    # fi
    unzip -q "$TMP_DIR"/processed-bundle/jsonconversions.zip -d "$TMP_DIR"/processed-bundle/

    if ! validate_processed_jsonconversion; then
        failed=1
    fi

    unzip -q "$TMP_DIR"/processed-bundle/proxy-invoice/Proxy-Invoices-TXT.zip \
          -d "$TMP_DIR"/processed-bundle/proxy-txt/

    unzip -q "$TMP_DIR"/processed-bundle/proxy-invoice/Proxy-Invoices-PDF.zip \
          -d "$TMP_DIR"/processed-bundle/proxy-pdf/

    if ! validate_txt_proxy_dir; then
        failed=1
    fi

    if ! validate_pdf_proxy_dir; then
        failed=1
    fi

    if ! validate_proxy_text 783404; then
        failed=1
    fi

    return $failed
}

function validate_processed_root() {
    (
        cd "$TMP_DIR"/processed-bundle
        tree . > "$TMP_DIR"/processed-bundle-tree.txt
    )
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/processed-bundle-tree.txt "$EXPECTED_DIR"/processed-bundle-tree.txt
        progress "Copying processed-bundle-tree.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle-tree.txt "$EXPECTED_DIR"/processed-bundle-tree.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Tree Does Not Match"
            return 1
        else
            progress "Processed Bundle Tree Layout Matches"
            return 0
        fi
    fi
}

function validate_processed_jsonconversion() {
    (
        cd "$TMP_DIR"/processed-bundle/jsonconversions
        tree . > "$TMP_DIR"/processed-bundle-jsonconversions-tree.txt
    )
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/processed-bundle-jsonconversions-tree.txt "$EXPECTED_DIR"/processed-bundle-jsonconversions-tree.txt
        progress "Copying processed-bundle-jsonconversions-tree.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle-jsonconversions-tree.txt "$EXPECTED_DIR"/processed-bundle-jsonconversions-tree.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle JSONConversion Tree Does Not Match"
            return 1
        else
            progress "Processed Bundle JSONConversion Tree Layout Matches"
            return 0
        fi
    fi
}

function validate_repair_order_number_summary() {
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/processed-bundle/processing-result/repair-order-sequence-summary.txt \
           "$EXPECTED_DIR"/processed-bundle-repair-order-sequence-summary.txt
        progress "Copying repair-order-sequence-summary.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle/processing-result/repair-order-sequence-summary.txt \
             "$EXPECTED_DIR"/processed-bundle-repair-order-sequence-summary.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Repair Order Number Summary Does Not Match"
            return 1
        else
            progress "Processed Bundle Repair Order Number Summary Matches"
            return 0
        fi
    fi
}

function validate_invoice_master() {
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/processed-bundle/processing-result/invoice-master.csv \
           "$EXPECTED_DIR"/processed-bundle-invoice-master.csv
        progress "Copying repair-order-sequence-summary.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle/processing-result/invoice-master.csv \
             "$EXPECTED_DIR"/processed-bundle-invoice-master.csv
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Invoice Master Does Not Match"
            return 1
        else
            progress "Processed Bundle Invoice Master Matches"
            return 0
        fi
    fi
}

function validate_dead_letter_detail() {
    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/processed-bundle/dead-letter-detail.txt \
           "$EXPECTED_DIR"/processed-bundle-dead-letter-detail.txt
        progress "Copying dead-letter-detail.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle/dead-letter-detail.txt \
             "$EXPECTED_DIR"/processed-bundle-dead-letter-detail.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Dead Letter Detail Does Not Match"
            return 1
        else
            progress "Processed Bundle Dead Letter Detail Matches"
            return 0
        fi
    fi
}

function validate_txt_proxy_dir() {
    (
        cd "$TMP_DIR"/processed-bundle/proxy-txt
        tree . > "$TMP_DIR"/proxy-txt-tree.txt
    )

    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/proxy-txt-tree.txt "$EXPECTED_DIR"/proxy-txt-tree.txt
        progress "Copying proxy-txt-tree.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/proxy-txt-tree.txt "$EXPECTED_DIR"/proxy-txt-tree.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Proxy TXT Tree Does Not Match"
            return 1
        else
            progress "Processed Bundle Proxy TXT Tree Layout Matches"
            return 0
        fi
    fi
}

function validate_pdf_proxy_dir() {
    (
        cd "$TMP_DIR"/processed-bundle/proxy-pdf
        tree . > "$TMP_DIR"/proxy-pdf-tree.txt
    )

    if [[ "$DO_COPY" = 'true' ]]; then
        cp "$TMP_DIR"/proxy-pdf-tree.txt "$EXPECTED_DIR"/proxy-pdf-tree.txt
        progress "Copying proxy-pdf-tree.txt as requested"
        return 0
    else
        diff "$TMP_DIR"/proxy-pdf-tree.txt "$EXPECTED_DIR"/proxy-pdf-tree.txt
        if [[ $? != 0 ]]; then
            scream "Processed Bundle Proxy PDF Tree Does Not Match"
            return 1
        else
            progress "Processed Bundle Proxy PDF Tree Layout Matches"
            return 0
        fi
    fi
}

function validate_proxy_text() {
    if [[ "$DO_COPY" = 'true' ]]; then
        mkdir -p "$EXPECTED_DIR"/single-proxy
        cp "$TMP_DIR"/processed-bundle/proxy-txt/"${1:?RO Required}.txt" \
           "$EXPECTED_DIR"/single-proxy/"${1:?RO Required}.txt"
        progress "Copying RO $1 as requested"
        return 0
    else
        diff "$TMP_DIR"/processed-bundle/proxy-txt/"${1:?RO Required}.txt" \
             "$EXPECTED_DIR"/single-proxy/"${1:?RO Required}.txt"
        if [[ $? != 0 ]]; then
            scream "Proxy Repair Order Number $1 Does Not Match"
            return 1
        else
            progress "Proxy Repair Order Number $1 Matches"
            return 0
        fi
    fi
}

function perform_processing_on() {
(
    cd "$DU_ETL_HOME"/DU-DMS/DMS-ReynoldsRCI/Processor-Application
    cp "$1" "$TMP_DIR"/test-extracted-bundle.zip
    ./manual-zip-process "$TMP_DIR"/test-extracted-bundle.zip || die "Processing Failed"
) || die "Processing Failed"
}

main
