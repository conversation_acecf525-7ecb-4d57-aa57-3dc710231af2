{
roN<PERSON>ber,
TransactionPayType,
TransactionType,
CustTaxTotalAmt,
IntrTaxTotalAmt,
WarrTaxTotalAmt,
IntrRoTotalAmt,
WarrRoTotalAmt,
CustRoTotalAmt,
FinalPostDate,
CustInvoiceDate,
RoCreateTime,
RoCreateDate,
MileageOut,
MileageIn,
CarlineDesc,
Vin,
RoStatus,
DeptType,
AdvName,
AdvNo,
CustName,
CustNo,
Carline,
ModelDesc,
VehicleYr,
VehicleMake,
MakeName,
FirstName,
MidName,
LastName,
Zip,
State,
City,
Type,
RoComment,
TechRecommend,
ExtClrDesc,
OldStatus,
IsDelete
}
