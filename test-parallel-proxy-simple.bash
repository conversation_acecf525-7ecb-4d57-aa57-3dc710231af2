#!/usr/bin/env bash

# Simple test script to verify parallel proxy generation functionality
# This script only tests our specific function without external dependencies

set -e

echo "🧪 Testing Parallel Proxy Generation Setup"
echo "=========================================="

# Define minimal required functions for testing
function say() { echo "$@"; }
function progress() { echo "PROGRESS: $@"; }
function die() { echo "ERROR: $@"; exit 1; }
function yell() { echo "WARNING: $@"; }

# Set up test environment
TEST_DIR="/tmp/test-parallel-proxy-$$"
mkdir -p "$TEST_DIR/processing-result"

# Mock the required variables
export WORK_DIR_PROCESSING_RESULTS_DIR="$TEST_DIR/processing-result"
export PERFORM_PROXY_RO_BUILD='false'  # Start with false to test auto-detection
export COMPANY_IDS=''  # Start empty to test auto-detection

echo "📁 Test directory: $TEST_DIR"
echo "📁 Processing results dir: $WORK_DIR_PROCESSING_RESULTS_DIR"

# Create mock config files with different company IDs
echo "🔧 Creating mock configuration files..."

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company1.bash" << 'EOF'
# Mock config file for company 1
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
EOF

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company2.bash" << 'EOF'
# Mock config file for company 2
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
EOF

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company3.bash" << 'EOF'
# Mock config file for company 3 (should be ignored - company ID 0)
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
EOF

echo "✅ Created mock config files:"
ls -la "$WORK_DIR_PROCESSING_RESULTS_DIR"/config_*.bash

# Define our function directly (extracted from process-zip.bash-mixin)
function detect_and_setup_parallel_proxy_generation() {
    # 🔧 Auto-detect multiple company IDs from config files and enable parallel proxy generation
    local config_dir="$WORK_DIR_PROCESSING_RESULTS_DIR"
    local detected_company_ids=()
    
    echo "🔍 Scanning for configuration files in: $config_dir"
    
    # Look for config files in the processing results directory
    if [[ -d "$config_dir" ]]; then
        echo "📁 Config directory exists, searching for config_*.bash files..."
        
        # List all config files for debugging
        local config_files=($(find "$config_dir" -name "config_*.bash" 2>/dev/null))
        echo "📋 Found ${#config_files[@]} config files: ${config_files[*]}"
        
        # Extract company IDs from all config_*.bash files
        while IFS= read -r -d '' config_file; do
            if [[ -f "$config_file" ]]; then
                echo "🔍 Examining config file: $(basename "$config_file")"
                # Extract COMPANY_ID from the config file
                local company_id=$(grep '^COMPANY_ID=' "$config_file" | cut -d'"' -f2 | head -1)
                echo "   Found COMPANY_ID: '$company_id'"
                if [[ -n "$company_id" && "$company_id" != "0" ]]; then
                    detected_company_ids+=("$company_id")
                    echo "✅ Added COMPANY_ID: $company_id from $(basename "$config_file")"
                else
                    echo "⚠️  Skipping invalid COMPANY_ID: '$company_id'"
                fi
            fi
        done < <(find "$config_dir" -name "config_*.bash" -print0 2>/dev/null)
    else
        echo "❌ Config directory does not exist: $config_dir"
    fi
    
    echo "📊 Total detected company IDs: ${#detected_company_ids[@]}"
    echo "📋 Company IDs: ${detected_company_ids[*]}"
    
    # If multiple company IDs detected, set up parallel processing
    if [[ ${#detected_company_ids[@]} -gt 1 ]]; then
        # Remove duplicates and create comma-separated list
        local unique_company_ids=($(printf '%s\n' "${detected_company_ids[@]}" | sort -u))
        export COMPANY_IDS=$(IFS=','; echo "${unique_company_ids[*]}")
        export PERFORM_PROXY_RO_BUILD='true'
        
        echo "🚀 Multiple company IDs detected: ${#unique_company_ids[@]} companies"
        echo "🔧 Setting COMPANY_IDS='$COMPANY_IDS'"
        echo "🔧 Setting PERFORM_PROXY_RO_BUILD='true'"
        echo "✅ Parallel proxy generation enabled for companies: $COMPANY_IDS"
    elif [[ ${#detected_company_ids[@]} -eq 1 ]]; then
        echo "📋 Single company ID detected: ${detected_company_ids[0]}"
        export COMPANY_ID="${detected_company_ids[0]}"
        # Keep existing PERFORM_PROXY_RO_BUILD setting
        echo "🔧 Single company mode - COMPANY_ID='$COMPANY_ID'"
    else
        echo "⚠️  No valid company IDs detected in config files"
        echo "🔍 This may be normal if config files haven't been generated yet"
    fi
}

echo ""
echo "🧪 Testing detect_and_setup_parallel_proxy_generation function..."
echo "================================================================="

# Test the function
detect_and_setup_parallel_proxy_generation

echo ""
echo "📊 Results:"
echo "==========="
echo "COMPANY_IDS: '$COMPANY_IDS'"
echo "PERFORM_PROXY_RO_BUILD: '$PERFORM_PROXY_RO_BUILD'"
echo "COMPANY_ID: '${COMPANY_ID:-<not set>}'"

# Verify results
echo ""
echo "🔍 Verification:"
echo "================"

if [[ "$COMPANY_IDS" == "*********,*********" || "$COMPANY_IDS" == "*********,*********" ]]; then
    echo "✅ COMPANY_IDS correctly set with multiple companies"
else
    echo "❌ COMPANY_IDS not set correctly. Expected: '*********,*********' or '*********,*********', Got: '$COMPANY_IDS'"
fi

if [[ "$PERFORM_PROXY_RO_BUILD" == "true" ]]; then
    echo "✅ PERFORM_PROXY_RO_BUILD correctly enabled"
else
    echo "❌ PERFORM_PROXY_RO_BUILD not enabled. Expected: 'true', Got: '$PERFORM_PROXY_RO_BUILD'"
fi

# Test with single company
echo ""
echo "🧪 Testing with single company..."
echo "================================="

# Remove one config file
rm "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company2.bash"

# Reset variables
export COMPANY_IDS=''
export PERFORM_PROXY_RO_BUILD='false'
unset COMPANY_ID

# Test again
detect_and_setup_parallel_proxy_generation

echo ""
echo "📊 Single Company Results:"
echo "=========================="
echo "COMPANY_IDS: '$COMPANY_IDS'"
echo "PERFORM_PROXY_RO_BUILD: '$PERFORM_PROXY_RO_BUILD'"
echo "COMPANY_ID: '${COMPANY_ID:-<not set>}'"

# Cleanup
echo ""
echo "🧹 Cleaning up test directory..."
rm -rf "$TEST_DIR"

echo ""
echo "✅ Test completed!"
