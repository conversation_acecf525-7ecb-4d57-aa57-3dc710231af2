#!/usr/bin/env bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash
source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
# if [[ -z "$2" ]]; then
#     ETLDIR=/etl/etl-vagrant/etl-reynoldsrci/reynoldsrci-zip
# else
#     ETLDIR=$2
# fi
ETLDIR=/etl/etl-vagrant/etl-reynoldsrci/reynoldsrci-zip
DISTDIR="$HOME"/armatus-dropbox/Imports
ARCHIVEDIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/processed-archive
IMPORTDIR=/etl/etl-vagrant/etl-reynoldsrci/reynoldsrci-import

# Debug targets
if [ "${DU_ETL_DEBUG_MODE:=0}" -gt 0 ]; then
    ETLDIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/etl
    IMPORTDIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/import
fi
DISTDIR="$HOME"/tmp/du-etl-dms-reynoldsrci-extractor-work/manual/dist
mkdir -p "$ETLDIR" "$DISTDIR" "$IMPORTDIR"

mkdir -p "$ARCHIVEDIR" || die "Failed to create archive directory"

[[ -d "$DISTDIR" ]] || die "Distribution Directory Does Not Exist (mounted?)"
[[ -d "$ETLDIR"  ]] || die "ETL Directory Does Not Exist (mounted?)"
[[ -d "$IMPORTDIR" ]] || die "Import Zip Directory Must Exist"

./send-bundle-dump-to-etl \
    "${1:?Zip File Must Be Specified}" \
    "$ETLDIR" \
    "$DISTDIR" \
    "${IMPORTDIR}" \
    || die "Failed during Distribution"

progress "Moving Bundle to Archive"

mv --force "$1" "$ARCHIVEDIR"/ || die "Moving to Archive Failed"