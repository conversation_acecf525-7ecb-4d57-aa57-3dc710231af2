#!/usr/bin/env bash

# Test script to verify parallel proxy generation functionality
# This script simulates the conditions needed for parallel proxy generation

set -e

echo "🧪 Testing Parallel Proxy Generation Setup"
echo "=========================================="

# Define minimal required functions for testing
function say() { echo "$@"; }
function progress() { echo "PROGRESS: $@"; }
function die() { echo "ERROR: $@"; exit 1; }
function yell() { echo "WARNING: $@"; }

# Mock other functions that might be called
function generate_mock_config_file() { echo "Mock: generate_mock_config_file called"; }
function load_data_from_scheduler_database() { echo "Mock: load_data_from_scheduler_database called"; return 0; }

# Set up test environment
TEST_DIR="/tmp/test-parallel-proxy-$$"
mkdir -p "$TEST_DIR/processing-result"

# Mock the required variables
export WORK_DIR_PROCESSING_RESULTS_DIR="$TEST_DIR/processing-result"
export PERFORM_PROXY_RO_BUILD='false'  # Start with false to test auto-detection
export COMPANY_IDS=''  # Start empty to test auto-detection

echo "📁 Test directory: $TEST_DIR"
echo "📁 Processing results dir: $WORK_DIR_PROCESSING_RESULTS_DIR"

# Create mock config files with different company IDs
echo "🔧 Creating mock configuration files..."

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company1.bash" << 'EOF'
# Mock config file for company 1
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY522"
EOF

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company2.bash" << 'EOF'
# Mock config file for company 2
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="*********"
SOURCE_COMPANY_ID="*********"
PROJECT_ID="*********"
PROJECT_TYPE="Parts UL"
PROJECT_NAME="QAPREY52Du"
EOF

cat > "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company3.bash" << 'EOF'
# Mock config file for company 3 (should be ignored - company ID 0)
export DMS_BASE='ReynoldsRCI'
COMPANY_ID="0"
SOURCE_COMPANY_ID="0"
PROJECT_ID=""
PROJECT_TYPE=""
PROJECT_NAME=""
EOF

echo "✅ Created mock config files:"
ls -la "$WORK_DIR_PROCESSING_RESULTS_DIR"/config_*.bash

# Source the function we want to test
source "./Processor-Application/src/bash/process-zip.bash-mixin"

echo ""
echo "🧪 Testing detect_and_setup_parallel_proxy_generation function..."
echo "================================================================="

# Test the function
detect_and_setup_parallel_proxy_generation

echo ""
echo "📊 Results:"
echo "==========="
echo "COMPANY_IDS: '$COMPANY_IDS'"
echo "PERFORM_PROXY_RO_BUILD: '$PERFORM_PROXY_RO_BUILD'"
echo "COMPANY_ID: '${COMPANY_ID:-<not set>}'"

# Verify results
echo ""
echo "🔍 Verification:"
echo "================"

if [[ "$COMPANY_IDS" == "*********,*********" || "$COMPANY_IDS" == "*********,*********" ]]; then
    echo "✅ COMPANY_IDS correctly set with multiple companies"
else
    echo "❌ COMPANY_IDS not set correctly. Expected: '*********,*********' or '*********,*********', Got: '$COMPANY_IDS'"
fi

if [[ "$PERFORM_PROXY_RO_BUILD" == "true" ]]; then
    echo "✅ PERFORM_PROXY_RO_BUILD correctly enabled"
else
    echo "❌ PERFORM_PROXY_RO_BUILD not enabled. Expected: 'true', Got: '$PERFORM_PROXY_RO_BUILD'"
fi

# Test with single company
echo ""
echo "🧪 Testing with single company..."
echo "================================="

# Remove one config file
rm "$WORK_DIR_PROCESSING_RESULTS_DIR/config_company2.bash"

# Reset variables
export COMPANY_IDS=''
export PERFORM_PROXY_RO_BUILD='false'
unset COMPANY_ID

# Test again
detect_and_setup_parallel_proxy_generation

echo ""
echo "📊 Single Company Results:"
echo "=========================="
echo "COMPANY_IDS: '$COMPANY_IDS'"
echo "PERFORM_PROXY_RO_BUILD: '$PERFORM_PROXY_RO_BUILD'"
echo "COMPANY_ID: '${COMPANY_ID:-<not set>}'"

# Cleanup
echo ""
echo "🧹 Cleaning up test directory..."
rm -rf "$TEST_DIR"

echo ""
echo "✅ Test completed!"
