#!/usr/bin/env bash

source "$DU_ETL_HOME"/src/shared/bash/set-default-errors.bash
source "$DU_ETL_HOME"/src/shared/bash/globals-index.bash

dms=reynolds
DMS=Reynolds
[[ -d "${DU_ETL_HOME}/DU-DMS/DMS-${DMS}" ]] || die "DMS-${DMS} does not exist"

DMS_REPO_BASE="$DU_ETL_HOME"/DU-DMS/DMS-${DMS}
TMP_AUDIT_DIR=$HOME/tmp/du-etl-audit-upload-${dms}-invoices
AUDITLOAD_RESULT_FILE="$TMP_AUDIT_DIR"/invoice-audit-load-result.txt

INVOICES_BASE="${INVOICES_BASE:-/etl}"
INVOICES_IN=$INVOICES_BASE/invoices-in-audit/${dms}
INVOICES_ARCHIVE=$INVOICES_BASE/invoices-archive-audit/${dms}
INVOICES_ERR=$INVOICES_BASE/invoices-err-audit/${dms}
LOG_FILE=/etl/log/reynolds-audit-invoice-import.log

AUDIT_LOAD="true"
LOADITAUDIT="true"

function load_it_to_audit() {
    local store_exists

    clear_dir "$TMP_AUDIT_DIR"

    function psql_audit() {
        psql "service=${AUDIT_PROD_PG_SERVICE:-AUDIT service needed for processing}" --set=ON_ERROR_STOP=1 "$@"
    }

    FILE_TO_LOAD="${1:?File Specification Required}"

    progress "Loading file $(basename $FILE_TO_LOAD)"

    echo "-----------------/\/\/\/"
    echo "$AUDITLOAD_RESULT_FILE"
    echo "Loading $FILE_TO_LOAD : $(date)" >> "$AUDITLOAD_RESULT_FILE"
    echo "" >> "$AUDITLOAD_RESULT_FILE"

    echo "--------------------"

    unzip -q "$FILE_TO_LOAD" -d "$TMP_AUDIT_DIR"

    COMPANY_ID="$(< $TMP_AUDIT_DIR/store-id)"
    COMPANY_NAME="$(< $TMP_AUDIT_DIR/store-name)"
    
    if [[ ! -s "$TMP_AUDIT_DIR"/store-id ]]; then
       echo "non-empty store-id file expected in zip file but it is not present" >> "$AUDITLOAD_RESULT_FILE"
       echo "non-empty store-id file expected in zip file but it is not present" >> "$LOG_FILE"
       return 1
    fi   

 
    if  [[ ! -s "$TMP_AUDIT_DIR"/du-etl-invoices-to-upload.tsv ]]; then
      echo "non-empty tsv expected in zip file but it is not present" >> "$AUDITLOAD_RESULT_FILE"
      echo "non-empty tsv expected in zip file but it is not present" >> "$LOG_FILE"
      return 1
    fi 

    {
        echo "Store Identification: $(cat $TMP_AUDIT_DIR/store-id)"
        echo "Loading Outcome:"
    } >> "$AUDITLOAD_RESULT_FILE"

    if psql_audit -q <<SQL >> "$AUDITLOAD_RESULT_FILE" 2>&1; then

    CREATE TEMP TABLE invsrc_audit (
        invoicenumber text NOT NULL,
        invoicesource text NOT NULL
    );

    \set QUIET off
    \copy invsrc_audit FROM '$TMP_AUDIT_DIR/du-etl-invoices-to-upload.tsv'  WITH (FORMAT text, DELIMITER E'\t')

    INSERT INTO invoice_master.actual_ro_document 
    WITH company_store_code AS (
     SELECT '[' || mage_group_code || ']' || mage_store_code AS store_code
      FROM solve360_fd.s360_company_mage
      WHERE company_id = '${COMPANY_ID}'
      LIMIT 1
   )

   SELECT 
     current_date,
     cs.store_code,
     i.invoicenumber,
     i.invoicesource
   FROM invsrc_audit i
   CROSS JOIN company_store_code cs
   WHERE NOT EXISTS (
     SELECT 1
     FROM invoice_master.actual_ro_document ai
     WHERE ai.ro_number = i.invoicenumber
     AND ai.dealership_id = cs.store_code
  );

SQL

    cat "$AUDITLOAD_RESULT_FILE" >> "$LOG_FILE"

    if [ $? -eq 0 ]; then
        echo "Audit insertion of invoices completed"  >> "$AUDITLOAD_RESULT_FILE"
        echo "Audit insertion of invoices completed"  >> "$LOG_FILE"
    else
        echo "Audit insertion of invoices failed."  >> "$AUDITLOAD_RESULT_FILE"
        echo "Audit insertion of invoices failed."  >> "$LOG_FILE"
    fi
        echo "INSERT: indicates how many not already present" >> "$AUDITLOAD_RESULT_FILE"
        echo "INSERT: indicates how many not already present" >> "$LOG_FILE"
    fi

}

function email() {
    local SUBJECT="$1"
    if [[ ! "$SEND_TO_ETL_INVOICES_NOTIFY" = '' ]]; then
        if type send-email >/dev/null 2>&1; then
            echo "Sending Summary Emails"  >> "$LOG_FILE"

            send-email --to "$SEND_TO_ETL_INVOICES_NOTIFY" \
                       --subject "$SUBJECT" \
                       --body-file "$AUDITLOAD_RESULT_FILE" \
                       --send
        fi
    fi
}

function release_lock() {
    rm "${OUTPUT_DIRECTORY}/.lock"
 }

function perform_load() {
    if [[ -e "${OUTPUT_DIRECTORY}/.lock" ]]; then
        return 0
    fi

    trap release_lock EXIT
    touch "${OUTPUT_DIRECTORY}/.lock"
    while true
    do
        # Exit if no zip files
        if [ "$(find $OUTPUT_DIRECTORY -name '*.zip' | wc -l)" = '0' ]; then
            break
        fi

        LOAD_FILE=$(stat --printf="%y %n\n" $OUTPUT_DIRECTORY/*.zip  \
         | sort --reverse | head --lines=1 | awk '{print $4}')

        say "Beginning Load - this can take a while"
        echo "Beginning Load - this can take a while" >> "$LOG_FILE"
        {
            echo "Beginning Load"
            echo ""
            echo "Current Output Directory Contents:"
            ls -lh "$OUTPUT_DIRECTORY"
            echo ""
        } > "$AUDITLOAD_RESULT_FILE"

        if [[ $AUDIT_LOAD = 'true' ]]; then
            if load_it_to_audit "$LOAD_FILE"; then
                echo "--------Audit load Completed--------" >> "$LOG_FILE"
                say "Audit Load Completed"
                LOADITAUDIT="true"
            else
                say "Audit Load Not Completed" 
                echo "Audit Load Not Completed"  >> "$LOG_FILE"
                LOADITAUDIT="false"
            fi
        fi
        if [[ $LOADITAUDIT == "true" ]]; then
            say "Load Completed - Archiving File"
            echo "Load Completed - Archiving File"  >> "$LOG_FILE"
            mv "$LOAD_FILE" "$INVOICES_ARCHIVE"
            email "${DMS} Invoices Load From DS To Audit is Success - $COMPANY_NAME"
        else
            yell "Load To Audit Failed"
            echo "Load To Audit Failed"  >> "$LOG_FILE"
            mv "$LOAD_FILE" "$INVOICES_ERR"
            email "${DMS} Invoices Load From DS To Audit Failed - $COMPANY_NAME"
        fi
        #cat "$AUDITLOAD_RESULT_FILE"
    done
}

OUTPUT_DIRECTORY="$INVOICES_IN"

perform_load

exit
